/* eslint-disable no-restricted-imports */
import React from 'react';
import styled from 'styled-components';
import PropTypes from 'prop-types';

// imported separetely to enable tree shaking
import { Heading } from 'ext/components/ui/Text';
import Modal from 'ext/components/ui/Modal';

const ModalStyled = styled(Modal)`
  display: flex;
  flex-direction: column;
  align-items: center;
  min-width: 498px;
  margin-bottom: ${props =>
    `${props.outerHeight - props.innerHeight}px` || '0'};

  p {
    margin: 0 0 24px 0;
    text-align: center;
  }

  h2 {
    margin: 18px 0 10px 0;
  }
`;

const EmptySpace = styled.div`
  width: 100%;
  height: 200px;
  margin-top: 35px;
`;

const ArrowWrapper = styled.div`
  display: flex;
  justify-content: end;
  width: 100%;
`;

const PermissionAllowModal = ({ onRequestPermission }) => {
  onRequestPermission();

  return (
    <ModalStyled
      theme="light"
      innerHeight={window.innerHeight}
      outerHeight={window.outerHeight}
    >
      <Heading>Allow Appcues on chrome</Heading>

      <EmptySpace />

      <ArrowWrapper>
        <svg
          xmlns="http://www.w3.org/2000/svg"
          width="36"
          height="42"
          fill="none"
          viewBox="0 0 36 42"
        >
          <path
            fill="url(#paint0_linear_37_108)"
            stroke="#5C5CFF"
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeMiterlimit="10"
            d="M32.703 25.381c.858 0 1.716-.508 2.083-1.27.368-.762.245-1.778-.245-2.54L19.838 1.89C19.348 1.254 18.735 1 18 1c-.735 0-1.348.38-1.838.889L1.46 21.57c-.49.762-.613 1.651-.245 2.54.367.762 1.225 1.27 2.083 1.27h5.636v10.92c0 2.667 2.083 4.699 4.534 4.699h9.066c2.574 0 4.534-2.159 4.534-4.698V25.38h5.636z"
          />
          <defs>
            <linearGradient
              id="paint0_linear_37_108"
              x1="35"
              x2="1"
              y1="21"
              y2="21"
              gradientUnits="userSpaceOnUse"
            >
              <stop stopColor="#5C5CFF" />
              <stop offset="1" stopColor="#8960FF" />
            </linearGradient>
          </defs>
        </svg>
      </ArrowWrapper>
    </ModalStyled>
  );
};

PermissionAllowModal.propTypes = {
  onRequestPermission: PropTypes.func.isRequired,
};

export default PermissionAllowModal;
