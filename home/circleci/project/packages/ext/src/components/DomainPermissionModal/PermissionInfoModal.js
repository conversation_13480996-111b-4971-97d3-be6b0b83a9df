/* eslint-disable no-restricted-imports */
import React from 'react';
import styled from 'styled-components';
import PropTypes from 'prop-types';

// imported separetely to enable tree shaking
import Modal from 'ext/components/ui/Modal';
import Button from 'ext/components/ui/Button';
import { Heading, P } from 'ext/components/ui/Text';

const ModalStyled = styled(Modal)`
  display: flex;
  flex-direction: column;
  align-items: center;
  min-width: 498px;

  p {
    margin: 0 0 24px 0;
    text-align: center;
  }

  h2 {
    margin: 18px 0 10px 0;
  }
`;

const PermissionInfoModal = ({ onContinue }) => {
  return (
    <ModalStyled theme="light">
      <svg
        width="160"
        height="160"
        viewBox="0 0 160 160"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M160 80C160 124.183 124.183 160 80 160C35.8172 160 0 124.183 0 80C0 35.8172 35.8172 0 80 0C124.183 0 160 35.8172 160 80Z"
          fill="#F4FBFF"
        />
        <mask
          id="mask0_29_167"
          style={{ maskType: 'alpha' }}
          maskUnits="userSpaceOnUse"
          x="0"
          y="0"
          width="160"
          height="160"
        >
          <path
            d="M160 80C160 124.183 124.183 160 80 160C35.8172 160 0 124.183 0 80C0 35.8172 35.8172 0 80 0C124.183 0 160 35.8172 160 80Z"
            fill="white"
          />
        </mask>
        <g mask="url(#mask0_29_167)">
          <rect x="6" y="114" width="148" height="46" fill="#E9F7FF" />
          <line
            x1="16.5"
            y1="114.5"
            x2="143.5"
            y2="114.5"
            stroke="#5C5CFF"
            strokeLinecap="round"
          />
          <line
            x1="148.5"
            y1="114.5"
            x2="153.5"
            y2="114.5"
            stroke="#5C5CFF"
            strokeLinecap="round"
          />
          <line
            x1="6.5"
            y1="114.5"
            x2="11.5"
            y2="114.5"
            stroke="#5C5CFF"
            strokeLinecap="round"
          />
          <path
            d="M38 52.5H122C125.038 52.5 127.5 54.9624 127.5 58V94C127.5 97.0376 125.038 99.5 122 99.5H38C34.9624 99.5 32.5 97.0376 32.5 94V58C32.5 54.9624 34.9624 52.5 38 52.5Z"
            fill="white"
            stroke="#5C5CFF"
          />
          <rect x="52" y="59" width="42" height="4" rx="2" fill="#D7D7FF" />
          <rect x="38" y="70" width="83" height="4" rx="2" fill="#D7D7FF" />
          <rect x="38" y="78" width="83" height="4" rx="2" fill="#D7D7FF" />
        </g>
        <path
          d="M120.558 94H98.4423C98.1769 94 98 93.7 98 93.25V88.75C98 88.3 98.1769 88 98.4423 88H120.558C120.823 88 121 88.3 121 88.75V93.25C121 93.7 120.823 94 120.558 94Z"
          fill="url(#paint0_linear_29_167)"
          stroke="#5C5CFF"
          strokeMiterlimit="10"
        />
        <path
          d="M94.5577 94H72.4423C72.1769 94 72 93.7 72 93.25V88.75C72 88.3 72.1769 88 72.4423 88H94.5577C94.8231 88 95 88.3 95 88.75V93.25C95 93.7 94.8231 94 94.5577 94Z"
          stroke="#5C5CFF"
          strokeMiterlimit="10"
        />
        <path
          d="M132.244 118.111L117.564 92.3757C117.357 92.0139 117.694 91.5839 118.094 91.6973L144.742 99.2364C145.135 99.3475 145.204 99.8746 144.853 100.084L135.827 105.467C135.715 105.534 135.636 105.645 135.61 105.773L133.104 117.974C133.017 118.395 132.456 118.484 132.244 118.111Z"
          fill="url(#paint1_linear_29_167)"
          stroke="#5C5CFF"
        />
        <circle cx="43.5" cy="61.5" r="5.5" fill="#5C5CFF" />
        <path
          d="M43.3802 61.7202C43.3359 61.6611 43.3359 61.5724 43.3802 61.5207L45.2646 59.119C45.3681 58.9934 45.575 59.0673 45.575 59.2298V63.7672C45.575 63.8633 45.5011 63.9372 45.405 63.9372H44.8951C44.836 63.9372 44.7769 63.915 44.7473 63.8633L43.3802 61.7202ZM41.8431 63.9446C41.7027 63.9446 41.614 63.782 41.7027 63.6637L42.6043 62.5035C42.7077 62.3779 42.9146 62.4518 42.9146 62.6144V63.7746C42.9146 63.8707 42.8407 63.9446 42.7447 63.9446H41.8431Z"
          fill="white"
        />
        <defs>
          <linearGradient
            id="paint0_linear_29_167"
            x1="98"
            y1="91"
            x2="121"
            y2="91"
            gradientUnits="userSpaceOnUse"
          >
            <stop stopColor="#5C5CFF" />
            <stop offset="1" stopColor="#8960FF" />
          </linearGradient>
          <linearGradient
            id="paint1_linear_29_167"
            x1="124.928"
            y1="105.286"
            x2="143.139"
            y2="94.8978"
            gradientUnits="userSpaceOnUse"
          >
            <stop stopColor="#FF5290" />
            <stop offset="0.0001" stopColor="#FF5290" />
            <stop offset="1" stopColor="#8960FF" />
          </linearGradient>
        </defs>
      </svg>

      <Heading>We can automatically open the Builder for you</Heading>

      <P>
        Chome will ask you to <b>allow</b> permissions on the next step.
      </P>

      <Button kind="primary" onClick={onContinue} type="button">
        Continue
      </Button>
    </ModalStyled>
  );
};

PermissionInfoModal.propTypes = {
  onContinue: PropTypes.func.isRequired,
};

export default PermissionInfoModal;
