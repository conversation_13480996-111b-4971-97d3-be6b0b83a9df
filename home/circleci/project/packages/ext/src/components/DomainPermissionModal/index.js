import React, { useState } from 'react';
import PropTypes from 'prop-types';
import { connect } from 'react-redux';
import { requestDomainPermission } from 'ext/lib/crx/actions';
import PermissionInfoModal from './PermissionInfoModal';
import PermissionAllowModal from './PermissionAllowModal';

export const DomainPermissionModalContainer = ({ onRequestPermission }) => {
  const [modalType, setModalType] = useState('PermissionInfoModal');

  const onContinue = () => {
    setModalType('PermissionAllowModal');
  };

  return modalType === 'PermissionInfoModal' ? (
    <PermissionInfoModal onContinue={onContinue} />
  ) : (
    <PermissionAllowModal onRequestPermission={onRequestPermission} />
  );
};

DomainPermissionModalContainer.propTypes = {
  onRequestPermission: PropTypes.func,
};

const mapDispatchToProps = {
  onRequestPermission: requestDomainPermission,
};

export default connect(
  null,
  mapDispatchToProps
)(DomainPermissionModalContainer);
