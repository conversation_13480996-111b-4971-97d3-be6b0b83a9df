import React, { useEffect, useState } from 'react';
import PropTypes from 'prop-types';
import { connect } from 'react-redux';
import { Text } from 'ext/components/ui';
import ElementOverlay from 'ext/components/ElementOverlay';
import {
  cancelSelectorRequest,
  completeSelectorRequest,
} from 'ext/lib/targeting-interface';
import useEscape from 'ext/lib/hooks/use-escape';
import useSelectElement from './use-select-element';
import {
  Wrapper,
  KeyCommand,
  FlexContainer,
  WrapperLight,
  InstructionsWrapper,
} from './styled';

export function TargetingInterface({
  maxChildren = null,
  onElementSelected,
  onCancel,
  pattern = null,
}) {
  const hoveredElement = useSelectElement(onElementSelected, { maxChildren });

  useEscape(onCancel);

  useEffect(() => {
    const cursorOverrideStyle = document.createElement('style');
    cursorOverrideStyle.innerHTML = `* {
      cursor: crosshair !important;
    }`;
    document.head.append(cursorOverrideStyle);
    return () => cursorOverrideStyle.remove();
  }, []);

  const [isVisible, setVisible] = useState(true);

  useEffect(() => {
    function onKeyDown(e) {
      if (/shift/i.test(e.key)) {
        e.preventDefault();
        e.stopPropagation();
        setVisible(false);
      }
    }

    function onKeyUp(e) {
      if (/shift/i.test(e.key)) {
        e.preventDefault();
        e.stopPropagation();
        setVisible(true);
      }
    }

    document.addEventListener('keydown', onKeyDown, true);
    document.addEventListener('keyup', onKeyUp, true);
    return () => {
      document.removeEventListener('keydown', onKeyDown, true);
      document.removeEventListener('keyup', onKeyUp, true);
    };
  }, [setVisible]);

  const highlightedElements = hoveredElement
    ? [
        {
          element: hoveredElement.element,
          label: hoveredElement.selector,
          padding: hoveredElement.padding,
        },
      ]
    : [];

  return (
    <>
      <ElementOverlay elements={highlightedElements} />
      {isVisible &&
        (pattern === 'launchpad' ? (
          <WrapperLight>
            <FlexContainer>
              <Text bold>Click where you want to place the launcher</Text>
              <InstructionsWrapper>
                <Text>
                  <KeyCommand>Hold Shift</KeyCommand> to interact with the page
                </Text>
                <Text>
                  <KeyCommand>Esc</KeyCommand> to cancel
                </Text>
              </InstructionsWrapper>
            </FlexContainer>
          </WrapperLight>
        ) : (
          <Wrapper>
            <Text>
              <KeyCommand>Hold Shift</KeyCommand> to interact with the page
            </Text>
            <Text>
              <KeyCommand>Esc</KeyCommand> to cancel
            </Text>
          </Wrapper>
        ))}
    </>
  );
}

TargetingInterface.propTypes = {
  maxChildren: PropTypes.number,
  onElementSelected: PropTypes.func.isRequired,
  onCancel: PropTypes.func.isRequired,
  pattern: PropTypes.string,
};

export default connect(null, {
  onCancel: cancelSelectorRequest,
  onElementSelected: completeSelectorRequest,
})(TargetingInterface);
