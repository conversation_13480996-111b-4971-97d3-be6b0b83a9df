import styled, { keyframes } from 'styled-components';
import { Chyron } from 'ext/components/ui';

const slideFromBottom = keyframes`
0% { bottom: 32px; }
100% { bottom: 152px; }
`;

export const Wrapper = styled(Chyron)`
  width: 400px;
  pointer-events: none;
  z-index: 1;
`;

export const WrapperLight = styled(Wrapper)`
  background-color: #fff;
  color: #425678;
  padding: 16px;
  height: auto;

  animation-name: ${slideFromBottom};
  animation-duration: 0.3s;
  animation-timing-function: cubic-bezier(0.15, 1.03, 0.8, 1.03);
  animation-fill-mode: forwards;
  animation-delay: 0.2s;
  animation-iteration-count: 1;
`;

export const KeyCommand = styled.span`
  font-weight: var(--bold);
  color: var(--primary-light);
`;

export const FlexContainer = styled.div`
  display: flex;
  width: 100%;
  flex-direction: column;
  align-items: center;
`;

export const InstructionsWrapper = styled.div`
  margin-top: 8px;
  display: flex;
  justify-content: space-between;
  width: 100%;

  > span > span {
    color: #0072d6;
  }
`;
