import { useEffect, useState } from 'react';
import {
  generateSelector,
  getElementAttributes,
  getParentPadding,
} from 'ext/lib/targeting-interface';
import { evaluateSelectorAll } from 'ext/lib/evaluate-selector';

const DISABLED_ELEMENTS = new Set(['HTML', 'BODY']);

function getParent(node) {
  return node.nodeType === Node.DOCUMENT_FRAGMENT_NODE
    ? node.host
    : node.parentNode;
}

function canAccessIframe(iframe) {
  try {
    return Boolean(iframe.contentDocument);
  } catch {
    return false;
  }
}

// check if the first element is a slot element
// if it is, then we should skip it and check the next one
export const setValidTarget = elementTree => {
  if (elementTree.length === 0) return null;

  const [target] = elementTree;
  if (target.tagName === 'SLOT') {
    return setValidTarget(elementTree.slice(1));
  }
  return elementTree;
};

export function getAncestorTree($element) {
  const tree = [$element];
  let parent = getParent($element);
  while (parent) {
    tree.push(parent);
    parent = getParent(parent);
  }
  return setValidTarget(tree);
}

function retargetSvgElements(tree) {
  const [target] = tree;
  const svgAncestor = target.closest('svg');
  const element = svgAncestor ? svgAncestor.parentElement : target;
  return tree.slice(tree.indexOf(element));
}

const getElementsIterators = root => {
  return document.createNodeIterator(root, NodeFilter.SHOW_ELEMENT, node => {
    return node.shadowRoot &&
      node.nodeName !== 'APPCUES-EXPERIENCE-CONTAINER' &&
      node.nodeName !== 'APPCUES-EXPERIENCE-CONTAINER-BUILDER' &&
      node.nodeName !== 'APPCUES-BUILDER'
      ? NodeFilter.FILTER_ACCEPT
      : NodeFilter.FILTER_REJECT;
  });
};

const getIframeIterators = root => {
  return document.createNodeIterator(root, NodeFilter.SHOW_ELEMENT, node => {
    return node.nodeName === 'IFRAME'
      ? NodeFilter.FILTER_ACCEPT
      : NodeFilter.FILTER_REJECT;
  });
};

function forEachIframe(root, callback) {
  const bodyIterators = getIframeIterators(root);
  let currentNode = bodyIterators.nextNode();

  while (currentNode) {
    if (canAccessIframe(currentNode)) callback(currentNode);
    currentNode = bodyIterators.nextNode();
  }
}

function forEachShadowRoot(root, callback) {
  let currentNode;

  const bodyIterators = getElementsIterators(root);

  const forEachIterator = iterator => {
    // eslint-disable-next-line no-cond-assign
    while ((currentNode = iterator.nextNode()) !== null) {
      callback(currentNode.shadowRoot);

      //  node iterator wasn't finding child web components
      // to be then able to add mouse-over events to all elements.
      // It was only finding two parent elements, as we could see in the logs file shared by the user.
      // Besides creating node iterators for each web component found in the root level,
      // we should also try to find children that are web components and recursively
      // add the mouse-over event to all of them.
      const childIterator = getElementsIterators(currentNode.shadowRoot);
      forEachIterator(childIterator);
    }
  };

  forEachIterator(bodyIterators);
}

export function isGenericElement(target, max = 100) {
  if (max === null) {
    return false;
  }

  let totalChildren = 0;

  // if target has no children than it is not generic
  if (target.children.length <= 0) return false;

  // Creating a queue and pushing the target element
  // so we can perform a DFS to count the children by tree level
  // until we reach the max number enabled or not.
  const queue = [];
  queue.push(target);

  while (queue.length > 0) {
    // Here we take a current snapshot length
    // since the queue can grow in case we need to keep
    // going until we reach the max limit of children in the tree
    let queueSnapshotLength = queue.length;

    while (queueSnapshotLength > 0) {
      // Dequeue an element from queue
      const element = queue[0];
      queue.shift();

      totalChildren += element.children.length;

      // if totalChildren count
      // is greater or equal the max of children available
      // then this element is a super generic element
      // and we should stop everything and return true
      if (totalChildren >= max) return true;

      // if we still investigating if the node has more children
      // than the max,
      // we should enqueue all current item children
      // so we can keep going down the tree
      [...element.children].forEach(child => {
        queue.push(child);
      });

      queueSnapshotLength -= queueSnapshotLength;
    }
  }

  return false;
}

export default function useSelectElement(onElementSelected, opts = {}) {
  const [hoveredElement, setHoveredElement] = useState(null);

  const maxChildren = opts?.maxChildren;

  useEffect(() => {
    function onClick(e) {
      const { shiftKey, clientX, clientY } = e;
      const [initialTarget] = e.composedPath();
      const initialTree = getAncestorTree(initialTarget);

      if (shiftKey) {
        return;
      }

      e.preventDefault();
      e.stopImmediatePropagation();

      if (
        isGenericElement(initialTree[0], maxChildren) ||
        DISABLED_ELEMENTS.has(initialTree[0].tagName)
      ) {
        return;
      }

      const tree = retargetSvgElements(initialTree);
      const [target] = tree;
      const padding = getParentPadding(target);
      const selector = generateSelector(tree);

      if (evaluateSelectorAll(selector).length === 1) {
        onElementSelected({
          selector,
          element: target,
          elementAttributes: getElementAttributes(target),
          clientX: clientX + (padding?.left ?? 0),
          clientY: clientY + (padding?.top ?? 0),
          padding,
        });
      } else {
        onElementSelected({
          selector: null,
          element: target,
          elementAttributes: {},
          clientX,
          clientY,
          padding,
        });
      }
    }

    function cancelMouseEvent(e) {
      if (e.shiftKey) {
        return;
      }

      e.preventDefault();
      e.stopImmediatePropagation();
    }

    document.addEventListener('mousedown', cancelMouseEvent, true);
    document.addEventListener('mouseup', cancelMouseEvent, true);
    document.addEventListener('click', onClick, true);
    forEachIframe(document.body, iframe => {
      iframe.contentWindow.document.addEventListener(
        'mousedown',
        cancelMouseEvent,
        true
      );
      iframe.contentWindow.document.addEventListener(
        'mouseup',
        cancelMouseEvent,
        true
      );
      iframe.contentWindow.document.addEventListener('click', onClick, true);
    });

    return () => {
      document.removeEventListener('mousedown', cancelMouseEvent, true);
      document.removeEventListener('mouseup', cancelMouseEvent, true);
      document.removeEventListener('click', onClick, true);
      forEachIframe(document.body, iframe => {
        iframe.contentWindow.document.removeEventListener(
          'mousedown',
          cancelMouseEvent,
          true
        );
        iframe.contentWindow.document.removeEventListener(
          'mouseup',
          cancelMouseEvent,
          true
        );
        iframe.contentWindow.document.removeEventListener(
          'click',
          onClick,
          true
        );
      });
    };
  }, [maxChildren]); // eslint-disable-line react-hooks/exhaustive-deps

  useEffect(() => {
    function onMouseOver(e) {
      const { shiftKey } = e;
      const [initialTarget] = e.composedPath();
      const initialTree = getAncestorTree(initialTarget);
      if (shiftKey) {
        setHoveredElement(null);
        return;
      }

      if (
        isGenericElement(initialTree[0], maxChildren) ||
        DISABLED_ELEMENTS.has(initialTree[0].tagName)
      ) {
        setHoveredElement(null);
        return;
      }

      const tree = retargetSvgElements(initialTree);
      const [target] = tree;

      setHoveredElement({
        element: target,
        selector: generateSelector(tree),
        padding: getParentPadding(target),
      });
    }

    document.body.addEventListener('mouseover', onMouseOver, true);
    forEachShadowRoot(document.body, shadowRoot => {
      shadowRoot.addEventListener('mouseover', onMouseOver, true);
    });

    forEachIframe(document.body, iframe => {
      iframe.contentWindow.document.addEventListener(
        'mouseover',
        onMouseOver,
        true
      );
    });

    return () => {
      document.body.removeEventListener('mouseover', onMouseOver, true);
      forEachShadowRoot(document.body, shadowRoot =>
        shadowRoot.removeEventListener('mouseover', onMouseOver, true)
      );

      forEachIframe(document.body, iframe => {
        iframe.contentWindow.document.removeEventListener(
          'mouseover',
          onMouseOver,
          true
        );
      });
    };
  }, [maxChildren]);

  useEffect(() => {
    function onMouseLeave() {
      setHoveredElement(null);
    }

    hoveredElement?.element.addEventListener('mouseleave', onMouseLeave, true);
    return () =>
      hoveredElement?.element.removeEventListener(
        'mouseleave',
        onMouseLeave,
        true
      );
  }, [hoveredElement]);

  return hoveredElement;
}
