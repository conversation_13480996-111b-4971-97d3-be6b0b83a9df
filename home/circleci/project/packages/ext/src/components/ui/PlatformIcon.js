import React from 'react';
import styled from 'styled-components';
import PropTypes from 'prop-types';

const iOS = (
  <path
    d="M400.598 270.564c-.229-41.951 18.746-73.614 57.153-96.932-21.49-30.749-53.953-47.666-96.818-50.981-40.578-3.201-84.929 23.661-101.161 23.661-17.146 0-56.467-22.518-87.33-22.518-63.783 1.029-131.567 50.866-131.567 152.256 0 29.949 5.487 60.888 16.46 92.817 14.631 41.951 67.441 144.826 122.537 143.112 28.805-.686 49.152-20.461 86.644-20.461 36.35 0 55.21 20.461 87.33 20.461 55.553-.8 103.333-94.303 117.279-136.368-74.528-35.092-70.527-102.876-70.527-105.047ZM335.9 82.872C367.106 45.837 364.248 12.117 363.334 0c-27.548 1.6-59.44 18.746-77.614 39.893-20.004 22.633-31.778 50.638-29.263 82.186 29.834 2.286 57.039-13.031 79.443-39.207Z"
    fill="currentColor"
  />
);

const Android = (
  <path
    d="M373.822 327.548a21.335 21.335 0 0 1-4.162-42.257 21.332 21.332 0 0 1 25.496 20.923 21.333 21.333 0 0 1-21.334 21.334Zm-235.644 0a21.332 21.332 0 0 1-19.71-13.17 21.34 21.34 0 0 1 15.548-29.087 21.332 21.332 0 1 1 4.162 42.257Zm243.289-128.427 42.613-73.778a8.885 8.885 0 0 0 1.279-6.957 8.89 8.89 0 0 0-16.63-1.932l-43.147 74.729a267.788 267.788 0 0 0-219.164 0l-43.147-74.729a8.89 8.89 0 1 0-15.351 8.889l42.613 73.778C57.36 238.916 7.324 312.988 0 400.499h512c-7.324-87.511-57.369-161.583-130.533-201.378"
    fill="currentColor"
  />
);

const Xamarin = (
  <path
    d="M147.733 28.22c-13.828.03-27.364 7.88-34.326 19.854L5.14 236.147c-6.853 12.006-6.853 27.7 0 39.706l108.267 188.074c6.962 11.974 20.498 19.825 34.326 19.853h216.535c13.828-.03 27.363-7.879 34.325-19.853l108.268-188.074c6.852-12.006 6.852-27.7 0-39.706L398.593 48.074c-6.962-11.975-20.497-19.826-34.325-19.853H147.733Zm1.965 110.026c.297-.03.609-.03.908 0h37.349c1.653.034 3.257.989 4.082 2.424l63.358 112.905c.319.558.525 1.18.603 1.819a4.86 4.86 0 0 1 .602-1.819l63.207-112.905c.851-1.478 2.53-2.439 4.233-2.424h37.349c3.307.029 5.784 4.195 4.234 7.123L303.778 256l61.845 110.48c1.697 2.945-.841 7.303-4.234 7.274H324.04c-1.733-.014-3.421-1.042-4.233-2.576L256.6 258.273a4.862 4.862 0 0 1-.602-1.818 4.864 4.864 0 0 1-.603 1.818l-63.358 112.905c-.788 1.491-2.4 2.508-4.082 2.576h-37.349c-3.394.03-5.931-4.329-4.234-7.274L208.217 256l-61.845-110.631c-1.483-2.672.326-6.553 3.326-7.123Z"
    fill="currentColor"
  />
);

const Flutter = (
  <>
    <path
      d="M304.565 0 49.601 254.964l78.916 78.916L462.399 0H304.565ZM302.788 235.123 166.273 371.638l79.213 80.396 78.771-78.77 138.142-138.141H302.788Z"
      fill="currentColor"
    />
    <path
      d="M245.487 452.035 305.452 512h156.946L324.256 373.264l-78.769 78.771ZM165.385 372.526l78.918-78.918 79.952 79.657-78.769 78.77-80.101-79.509Z"
      fill="currentColor"
    />
  </>
);

const ReactNative = (
  <path
    d="M418.2 177.2c-5.4-1.8-10.8-3.5-16.2-5.1.9-3.7 1.7-7.4 2.5-11.1 12.3-59.6 4.2-107.5-23.1-123.3-26.3-15.1-69.2.6-112.6 38.4-4.3 3.7-8.5 7.6-12.5 11.5-2.7-2.6-5.5-5.2-8.3-7.7-45.5-40.4-91.1-57.4-118.4-41.5-26.2 15.2-34 60.3-23 116.7 1.1 5.6 2.3 11.1 3.7 16.7-6.4 1.8-12.7 3.8-18.6 5.9C38.3 196.2 0 225.4 0 255.6c0 31.2 40.8 62.5 96.3 81.5 4.5 1.5 9 3 13.6 4.3-1.5 6-2.8 11.9-4 18-10.5 55.5-2.3 99.5 23.9 114.6 27 15.6 72.4-.4 116.6-39.1 3.5-3.1 7-6.3 10.5-9.7 4.4 4.3 9 8.4 13.6 12.4 42.8 36.8 85.1 51.7 111.2 36.6 27-15.6 35.8-62.9 24.4-120.5-.9-4.4-1.9-8.9-3-13.5 3.2-.9 6.3-1.9 9.4-2.9 57.7-19.1 99.5-50 99.5-81.7 0-30.3-39.4-59.7-93.8-78.4zM282.9 92.3c37.2-32.4 71.9-45.1 87.7-36 16.9 9.7 23.4 48.9 12.8 100.4-.7 3.4-1.4 6.7-2.3 10-22.2-5-44.7-8.6-67.3-10.6-13-18.6-27.2-36.4-42.6-53.1 3.9-3.7 7.7-7.2 11.7-10.7zM167.2 307.5c5.1 8.7 10.3 17.4 15.8 25.9-15.6-1.7-31.1-4.2-46.4-7.5 4.4-14.4 9.9-29.3 16.3-44.5 4.6 8.8 9.3 17.5 14.3 26.1zm-30.3-120.3c14.4-3.2 29.7-5.8 45.6-7.8-5.3 8.3-10.5 16.8-15.4 25.4-4.9 8.5-9.7 17.2-14.2 26-6.3-14.9-11.6-29.5-16-43.6zm27.4 68.9c6.6-13.8 13.8-27.3 21.4-40.6s15.8-26.2 24.4-38.9c15-1.1 30.3-1.7 45.9-1.7s31 .6 45.9 1.7c8.5 12.6 16.6 25.5 24.3 38.7s14.9 26.7 21.7 40.4c-6.7 13.8-13.9 27.4-21.6 40.8-7.6 13.3-15.7 26.2-24.2 39-14.9 1.1-30.4 1.6-46.1 1.6s-30.9-.5-45.6-1.4c-8.7-12.7-16.9-25.7-24.6-39s-14.8-26.8-21.5-40.6zm180.6 51.2c5.1-8.8 9.9-17.7 14.6-26.7 6.4 14.5 12 29.2 16.9 44.3-15.5 3.5-31.2 6.2-47 8 5.4-8.4 10.5-17 15.5-25.6zm14.4-76.5c-4.7-8.8-9.5-17.6-14.5-26.2-4.9-8.5-10-16.9-15.3-25.2 16.1 2 31.5 4.7 45.9 8-4.6 14.8-10 29.2-16.1 43.4zM256.2 118.3c10.5 11.4 20.4 23.4 29.6 35.8-19.8-.9-39.7-.9-59.5 0 9.8-12.9 19.9-24.9 29.9-35.8zM140.2 57c16.8-9.8 54.1 4.2 93.4 39 2.5 2.2 5 4.6 7.6 7-15.5 16.7-29.8 34.5-42.9 53.1-22.6 2-45 5.5-67.2 10.4-1.3-5.1-2.4-10.3-3.5-15.5-9.4-48.4-3.2-84.9 12.6-94zm-24.5 263.6c-4.2-1.2-8.3-2.5-12.4-3.9-21.3-6.7-45.5-17.3-63-31.2-10.1-7-16.9-17.8-18.8-29.9 0-18.3 31.6-41.7 77.2-57.6 5.7-2 11.5-3.8 17.3-5.5 6.8 21.7 15 43 24.5 63.6-9.6 20.9-17.9 42.5-24.8 64.5zm116.6 98c-16.5 15.1-35.6 27.1-56.4 35.3-11.1 5.3-23.9 5.8-35.3 1.3-15.9-9.2-22.5-44.5-13.5-92 1.1-5.6 2.3-11.2 3.7-16.7 22.4 4.8 45 8.1 67.9 9.8 13.2 18.7 27.7 36.6 43.2 53.4-3.2 3.1-6.4 6.1-9.6 8.9zm24.5-24.3c-10.2-11-20.4-23.2-30.3-36.3 9.6.4 19.5.6 29.5.6 10.3 0 20.4-.2 30.4-.7-9.2 12.7-19.1 24.8-29.6 36.4zm130.7 30c-.9 12.2-6.9 23.6-16.5 31.3-15.9 9.2-49.8-2.8-86.4-34.2-4.2-3.6-8.4-7.5-12.7-11.5 15.3-16.9 29.4-34.8 42.2-53.6 22.9-1.9 45.7-5.4 68.2-10.5 1 4.1 1.9 8.2 2.7 12.2 4.9 21.6 5.7 44.1 2.5 66.3zm18.2-107.5c-2.8.9-5.6 1.8-8.5 2.6-7-21.8-15.6-43.1-25.5-63.8 9.6-20.4 17.7-41.4 24.5-62.9 5.2 1.5 10.2 3.1 15 4.7 46.6 16 79.3 39.8 79.3 58 0 19.6-34.9 44.9-84.8 61.4zm-149.7-15c25.3 0 45.8-20.5 45.8-45.8s-20.5-45.8-45.8-45.8c-25.3 0-45.8 20.5-45.8 45.8s20.5 45.8 45.8 45.8z"
    fill="currentColor"
  />
);

const Ionic = (
  <>
    <path
      d="M61.44 256c0-107.447 87.113-194.56 194.56-194.56 43.264 0 83.2 14.08 115.511 37.98a73.316 73.316 0 0 1 46.573-41.582A255.047 255.047 0 0 0 256 0C114.615 0 0 114.615 0 256s114.615 256 256 256 256-114.615 256-256c0-29.586-5.029-58.057-14.3-84.553a73.045 73.045 0 0 1-55.003 29.586c5.12 17.408 7.863 35.84 7.863 54.967 0 107.447-87.113 194.56-194.56 194.56S61.44 363.447 61.44 256Z"
      fill="currentColor"
    />
    <path
      d="M506.734 204.142a254.31 254.31 0 0 0-9.034-32.695 73.054 73.054 0 0 1-55.003 29.586 193.856 193.856 0 0 1 7.168 38.363 79.981 79.981 0 0 0 56.869-35.254ZM256 368a112.13 112.13 0 0 0 112-112 112.132 112.132 0 0 0-32.841-79.159A112.132 112.132 0 0 0 256 144a112.128 112.128 0 0 0-112 112 112.183 112.183 0 0 0 112 112Zm168.009-208a56.003 56.003 0 0 0 56-56 55.997 55.997 0 0 0-56-56 56 56 0 0 0 0 112Z"
      fill="currentColor"
    />
  </>
);

const Mobile = (
  <path
    d="M144 0c-35.3 0-64 28.7-64 64v384c0 35.3 28.7 64 64 64h224c35.3 0 64-28.7 64-64V64c0-35.3-28.7-64-64-64H144Zm80 432h64c8.8 0 16 7.2 16 16s-7.2 16-16 16h-64c-8.8 0-16-7.2-16-16s7.2-16 16-16Z"
    fill="currentColor"
  />
);

export const knownFrameworks = {
  android: Android,
  ios: iOS,
  'react-native': ReactNative,
  xamarin: Xamarin,
  flutter: Flutter,
  ionic: Ionic,
};

export const unknownFrameworks = {
  mobile: Mobile,
  web: null,
};

const Icons = ({ className, type }) => {
  if (type === 'web') return null;

  const icon = knownFrameworks[type] || unknownFrameworks.mobile;

  return (
    <svg
      aria-label={type}
      className={className}
      role="img"
      viewBox="0 0 512 512"
      xmlns="http://www.w3.org/2000/svg"
    >
      {icon}
    </svg>
  );
};

Icons.propTypes = {
  className: PropTypes.string,
  type: PropTypes.string.isRequired,
};

export default styled(Icons)``;
