import React, { useState } from 'react';
import PropTypes from 'prop-types';
import { SquareButton, FontIcon, Tooltip } from 'ext/components/ui';
import useAnalytics from 'ext/lib/hooks/use-analytics';

const SettingsManager = ({ sidebarToggle }) => {
  const [visibleTooltip, setVisibleTooltip] = useState(false);
  const { track } = useAnalytics();

  const handleClick = () => {
    setVisibleTooltip(false);
    track('Builder interaction', {
      name: 'Toggled Sidebar Visibility',
      component: 'ManagerBar',
      type: 'Settings button',
    });
    sidebarToggle();
  };

  return (
    <Tooltip
      label="Settings"
      onMouseOut={() => setVisibleTooltip(false)}
      onMouseOver={() => setVisibleTooltip(true)}
      visible={visibleTooltip}
    >
      <SquareButton aria-label="Open settings sidebar" onClick={handleClick}>
        <FontIcon icon="cog" />
      </SquareButton>
    </Tooltip>
  );
};

SettingsManager.propTypes = {
  sidebarToggle: PropTypes.func,
};

export default SettingsManager;
