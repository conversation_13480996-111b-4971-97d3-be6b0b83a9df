import React from 'react';
import PropTypes from 'prop-types';
import styled, { keyframes } from 'styled-components';

const Message = styled.h3`
  font-weight: var(--bold);
  font-size: var(--large);
  margin-top: 10px;
`;

const Container = styled.div`
  color: var(--white);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
`;

const Ring = styled.div`
  display: inline-block;
  position: relative;
  width: 80px;
  height: 80px;
`;

const resizeRingSection = keyframes`
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
`;

const RingSection = styled.div`
  box-sizing: border-box;
  display: block;
  position: absolute;
  width: 64px;
  height: 64px;
  margin: 8px;
  border: 8px solid var(--white);
  border-radius: 50%;
  border-color: var(--white) transparent transparent transparent;
  animation: ${resizeRingSection} 1.2s cubic-bezier(0.5, 0, 0.5, 1) infinite;
  animation-delay: ${({ delay }) => delay}s;
`;

export default function Loader({ message, className }) {
  return (
    <Container aria-label="Loading" className={className}>
      <Ring>
        <RingSection delay={0} />
        <RingSection delay={-0.15} />
        <RingSection delay={-0.3} />
        <RingSection delay={-0.45} />
      </Ring>
      {message && <Message>{message}</Message>}
    </Container>
  );
}

Loader.propTypes = {
  message: PropTypes.string,
  className: PropTypes.string,
};
