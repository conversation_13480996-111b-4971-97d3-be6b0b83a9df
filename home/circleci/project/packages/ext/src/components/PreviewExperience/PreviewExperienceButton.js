import React from 'react';
import PropTypes from 'prop-types';
import { MiniButton, FontIcon } from 'ext/components/ui';

export function PreviewExperienceButton({ icon, onClick, ariaLabel }) {
  return (
    <MiniButton aria-label={ariaLabel} onClick={onClick}>
      <FontIcon icon={icon} />
    </MiniButton>
  );
}

PreviewExperienceButton.propTypes = {
  icon: PropTypes.string,
  onClick: PropTypes.func.isRequired,
  ariaLabel: PropTypes.string,
};
