import styled, { css, keyframes } from 'styled-components';
import { Chyron } from 'ext/components/ui';

export const slideIn = keyframes`
  from {
    margin-bottom: -80px;
  }
  to {
    margin-bottom: 0;
  }
`;

export const dance = keyframes`
15% {
    transform: translate3d(5px, 0, 0);
  }
  30% {
    transform: translate3d(-5px, 0, 0);
  }
  50% {
    transform: translate3d(3px, 0, 0);
  }
  65% {
    transform: translate3d(-3px, 0, 0);
  }
  80% {
    transform: translate3d(2px, 0, 0);
  }
  100% {
    transform: translate3d(0, 0, 0);
  }
`;

export const Container = styled(Chyron)`
  width: 252px;
  animation: ${slideIn} 0.5s ease-out 0.25s 1 both;
  ${({ done }) =>
    done &&
    css`
      animation: ${dance} 1s ease;
      animation-iteration-count: 1;
    `}
`;
