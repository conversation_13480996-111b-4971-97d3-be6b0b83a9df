import React from 'react';
import PropTypes from 'prop-types';
import { ControlGroup, Text } from 'ext/components/ui';
import { Container } from './styled';

export default function PreviewExperienceContainer({
  children,
  done,
  position,
}) {
  return (
    <Container done={done} position={position}>
      <Text>{done ? 'Preview complete' : 'Previewing'}</Text>
      <ControlGroup>{children}</ControlGroup>
    </Container>
  );
}

PreviewExperienceContainer.propTypes = {
  done: PropTypes.bool,
  position: PropTypes.oneOf(['top', 'bottom']),
  children: PropTypes.node,
};
