/* eslint-disable no-restricted-imports */
import React, { useState } from 'react';
import PropTypes from 'prop-types';
import { Icon } from '@appcues/sonar';
import { faTimes } from '@fortawesome/free-solid-svg-icons/faTimes';
import Modal from 'ext/components/ui/Modal';
import FeatureFlags from './FeatureFlags';
import ApplicationSettings from './ApplicationSettings';
import { Container, Close, Title, Tab, Tabs } from './styled';

function getTool(tab) {
  switch (tab) {
    case 'app':
      return <ApplicationSettings />;
    case 'flags':
    default:
      return <FeatureFlags />;
  }
}

export default function DevTools({ onClose, visible }) {
  const [tab, changeTab] = useState('flags');
  return (
    <Modal onClose={onClose} visible={visible}>
      <Close>
        <Icon icon={faTimes} onClick={onClose} />
      </Close>

      <Container>
        <Title>Builder DevTools</Title>
        <Tabs>
          <Tab active={tab === 'flags'} onClick={() => changeTab('flags')}>
            Feature Flags
          </Tab>
          <Tab active={tab === 'app'} onClick={() => changeTab('app')}>
            Application
          </Tab>
        </Tabs>
        {getTool(tab)}
      </Container>
    </Modal>
  );
}

DevTools.propTypes = {
  onClose: PropTypes.func,
  visible: PropTypes.bool,
};
