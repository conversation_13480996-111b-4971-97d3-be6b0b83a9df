import { useCallback } from 'react';
import {
  EVENT_BUILDER_BETA,
  ONLY_CUSTOM_BUTTONS,
  ENABLE_PINS,
  ENABLE_BUTTON_PINS,
  useGates,
  ENABLE_BANNERS,
  ENABLE_LAUNCHPAD,
  ENABLE_KNOWLEDGE_BASE,
  INLINE_LAUNCHPAD_V2,
} from 'ext/lib/gates';
import { get, set, remove } from 'ext/lib/local-storage';
import { FLAGS_KEY } from 'ext/lib/devtools';

// Registered feature flags that can be overridden in the DevTools
const FLAGS = {
  [EVENT_BUILDER_BETA]: {
    type: 'boolean',
    default: false,
  },
  [ONLY_CUSTOM_BUTTONS]: {
    type: 'boolean',
    default: false,
  },
  [ENABLE_PINS]: {
    type: 'boolean',
    default: false,
  },
  [ENABLE_BUTTON_PINS]: {
    type: 'boolean',
    default: false,
  },
  [ENABLE_BANNERS]: {
    type: 'boolean',
    default: false,
  },
  [ENABLE_LAUNCHPAD]: {
    type: 'boolean',
    default: false,
  },
  [ENABLE_KNOWLEDGE_BASE]: {
    type: 'boolean',
    default: false,
  },
  [INLINE_LAUNCHPAD_V2]: {
    type: 'boolean',
    default: false,
  },
};

// Map of default values for the registered feature flags above
const defaults = Object.entries(FLAGS).reduce(
  (acc, [gate, value]) => ({ ...acc, [gate]: value.default }),
  {}
);

export default () => {
  const gates = useGates();

  // Partition the gates based on whether they are registered above and can be
  // edited or if they were included by the Gates service but are not registered
  // for override.
  const { editable, uneditable, types } = Object.entries({
    ...defaults,
    ...gates,
  }).reduce(
    (acc, [gate, value]) => {
      if (Object.prototype.hasOwnProperty.call(FLAGS, gate)) {
        acc.editable[gate] = value;
        acc.types[gate] = FLAGS[gate].type;
      } else {
        acc.uneditable[gate] = value;
        acc.types[gate] = typeof value === 'boolean' ? 'boolean' : 'text';
      }
      return acc;
    },
    { editable: {}, uneditable: {}, types: {} }
  );

  // Handler to remove all feature flags from local storage
  const reset = useCallback(() => {
    remove(FLAGS_KEY);
    window.location.reload();
  }, []);

  // Handler to save feature flag overrides to local storage
  const save = useCallback(patch => {
    set(FLAGS_KEY, patch);
    window.location.reload();
  }, []);

  return {
    overrides: get(FLAGS_KEY, {}),
    editable,
    uneditable,
    types,
    reset,
    save,
  };
};
