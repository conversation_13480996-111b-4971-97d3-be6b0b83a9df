import React from 'react';
import PropTypes from 'prop-types';
import { Checkbox, Input } from 'ext/components/ui';

const types = {
  text: PropTypes.string,
  number: PropTypes.number,
  boolean: PropTypes.bool,
};

export default function Toggle({ disabled, onChange, type, value }) {
  switch (type) {
    case 'text': {
      const handleChange = event => {
        const {
          target: { value: next },
        } = event;
        onChange(next, event);
      };

      return (
        <Input
          defaultValue={value}
          disabled={disabled}
          onChange={handleChange}
          type={type}
        />
      );
    }

    case 'number': {
      const handleChange = event => {
        const {
          target: { valueAsNumber: next },
        } = event;
        onChange(Number.isNaN(next) ? value : next, event);
      };

      return (
        <Input
          defaultValue={value}
          disabled={disabled}
          onChange={handleChange}
          type={type}
        />
      );
    }

    case 'boolean': {
      return (
        <Checkbox disabled={disabled} onChange={onChange} checked={value} />
      );
    }

    default: {
      const expected = Object.keys(types).join(', ');
      throw new Error(
        `expected type to be one of [${expected}], but got [${type}]`
      );
    }
  }
}

Toggle.propTypes = {
  disabled: PropTypes.bool,
  onChange: PropTypes.func,
  type: PropTypes.oneOf(Object.keys(types)),
  value: PropTypes.oneOfType(Object.values(types)),
};
