import React, { useState } from 'react';
import isEmpty from 'lodash.isempty';
import isEqual from 'lodash.isequal';
import { Button, ButtonGroup } from 'ext/components/ui';
import Toggle from './Toggle';
import { Cell, Description, Grid, Row, Tool, Warning } from './styled';
import useFlagOverrides from './use-flag-overrides';

export function FeatureFlags() {
  const { overrides, editable, uneditable, types, reset, save } =
    useFlagOverrides();

  // Initialize state with original and overridden flags
  const [patch, update] = useState({ ...editable, ...overrides });

  // Create change handler for a given feature flag
  const handleChangeFor = flag => value => {
    update({ ...editable, ...patch, [flag]: value });
  };

  const overridden = !isEmpty(overrides);
  const changed = !isEqual(editable, patch);

  return (
    <Tool>
      <Description>
        The settings here will temporarily override any flags provided by the
        Gates service on this domain. Only flags that have been registered as
        editable may be updated while the remaining flags Gates service will be
        disabled by default.
      </Description>

      <Grid>
        {Object.entries(editable).map(([flag, value]) => (
          <Row key={flag}>
            <Cell>{flag}</Cell>
            <Cell>
              <Toggle
                onChange={handleChangeFor(flag)}
                type={types[flag]}
                value={value}
              />
            </Cell>
          </Row>
        ))}

        {Object.entries(uneditable).map(([flag, value]) => (
          <Row disabled key={flag}>
            <Cell>{flag}</Cell>
            <Cell>
              <Toggle
                disabled
                onChange={handleChangeFor(flag)}
                type={types[flag]}
                value={value}
              />
            </Cell>
          </Row>
        ))}
      </Grid>

      <ButtonGroup right>
        {(changed || overridden) && (
          <Warning>Changes will reload the builder</Warning>
        )}

        {overridden && (
          <Button kind="danger" onClick={reset} type="button">
            Reset
          </Button>
        )}

        <Button
          disabled={!changed}
          kind="primary"
          onClick={() => save(patch)}
          type="button"
        >
          Apply changes
        </Button>
      </ButtonGroup>
    </Tool>
  );
}

export default FeatureFlags;
