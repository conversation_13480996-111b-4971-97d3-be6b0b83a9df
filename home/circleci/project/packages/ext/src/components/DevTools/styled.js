import styled from 'styled-components';
import { easing } from 'ext/lib/style/easing';
import { ButtonGroup, Heading } from 'ext/components/ui';

export const Close = styled.span`
  align-items: center;
  color: var(--background);
  cursor: pointer;
  display: flex;
  font-size: var(--x-large);
  height: 24px;
  justify-content: center;
  margin: 12px;
  position: absolute;
  right: 0;
  top: 0;
  width: 24px;
  transition: ${easing('color')};

  :hover {
    color: var(--background-x-light);
  }
`;

export const Container = styled.div`
  min-width: 480px;
  max-width: 720px;

  * {
    font-family: monospace;
  }
`;

export const Title = styled.h2`
  color: var(--error);
  text-transform: uppercase;
`;

export const Warning = styled.span`
  align-items: center;
  color: var(--warning-x-light);
  display: flex;
  font-size: var(--x-small);
`;

export const Error = styled.span`
  align-items: center;
  color: var(--error-light);
  display: flex;
  font-size: var(--x-small);
`;

export const Tool = styled.div`
  border: 1px solid var(--white);
  color: var(--text-color);
  padding: 24px 24px 32px;
  margin: 0;

  :last-of-type {
    margin-bottom: 16px;
  }

  > ${Heading} {
    color: var(--light-text-color);
  }

  > ${ButtonGroup} {
    border-top: 1px solid var(--background-light);
    margin-top: 12px;
    padding-top: 12px;
  }
`;

export const Description = styled.p`
  color: var(--info-light);
  font-size: var(--x-small);
  margin: 16px 0;
  padding: 8px;
`;

export const Cell = styled.span`
  align-items: center;
  display: flex;
  padding: 0 8px;
`;

export const Row = styled.div`
  display: contents;
  padding: 4px 0;
  transition: ${easing('border-left-color', 'padding')};

  > ${Cell}:first-of-type {
    border-left-width: 2px;
    border-left-style: solid;
    border-left-color: transparent;
  }

  :hover > ${Cell}:first-of-type {
    border-left-color: var(--secondary);
  }

  ${({ disabled }) =>
    disabled &&
    `
      color: var(--disabled);

      :hover > ${Cell}:first-of-type {
        border-left-color: var(--disabled);
      }
    `}
`;

export const Grid = styled.div`
  display: grid;
  grid-auto-rows: 1fr;
  grid-template-columns: minmax(320px, auto) minmax(120px, 160px);
`;

export const Tabs = styled.ul`
  align-items: flex-start;
  display: flex;
  flex-direction: row;
  list-style-type: none;
  margin: 0;
  padding: 0;
  width: 100%;
`;

export const Tab = styled.li`
  border-width: 0;
  border-style: solid;
  border-color: var(--white);
  border-bottom-color: var(--background-dark);
  cursor: pointer;
  padding: 24px;
  margin-bottom: -1px;
  ${({ active }) =>
    active &&
    `
    border-width: 1px;
    cursor: default;
    font-weight: var(--bold);
`}
`;

export const TextButton = styled.button`
  background: none;
  border: none;
  color: var(--primary-light);
  cursor: pointer;
  margin: 0;
  padding: 0;
  text-decoration: underline;
`;
