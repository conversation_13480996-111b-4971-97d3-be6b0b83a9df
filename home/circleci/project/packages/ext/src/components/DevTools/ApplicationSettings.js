/* globals RELEASE */
import React, { useState } from 'react';
import { Button, ButtonGroup, Input } from 'ext/components/ui';
import { useOverrides } from 'ext/lib/devtools';
import {
  Cell,
  Description,
  Grid,
  Row,
  TextButton,
  Tool,
  Error,
  Warning,
} from './styled';

export default function ApplicationSettings() {
  const {
    overrides: { sha },
    save,
    revert,
  } = useOverrides();

  /* @var overridden - true if the CRX provided a SHA; false otherwise */
  const overridden = !!sha;
  /* @var {string} current - the SHA we _tried_ to load, whether we did or not */
  const current = sha || RELEASE;

  const [value, update] = useState(current);

  /* @var errored - true if the SHA loaded is different from the one provided;
   * false otherwise */
  const errored = overridden && sha !== RELEASE;

  /* @var changed - true if the input value differs from the loaded SHA; false
   * otherwise */
  const changed = value !== current;

  const handleReset = () => update(current);

  return (
    <Tool>
      <Description>
        Use these settings to modify which Builder bundle is loaded by the CRX.
      </Description>

      <Grid>
        <Row key="sha">
          <Cell>Builder SHA</Cell>

          <Cell>
            <Input
              onChange={e => update(e.target.value)}
              type="text"
              value={value}
            />
          </Cell>
        </Row>
        {overridden && (
          <Row key="revert">
            <Cell>
              {errored ? (
                <Error>SHA NOT FOUND. Default Builder loaded.</Error>
              ) : (
                <Warning>Builder SHA overridden!</Warning>
              )}
            </Cell>
            <Cell>
              <TextButton type="button" onClick={revert}>
                revert
              </TextButton>
            </Cell>
          </Row>
        )}
        {}
      </Grid>

      <ButtonGroup right>
        {changed && (
          <>
            <Warning>Changes will reload the builder</Warning>

            <Button kind="danger" onClick={handleReset} type="button">
              Reset
            </Button>
          </>
        )}
        <Button
          disabled={!changed}
          kind="primary"
          onClick={() => save(value)}
          type="button"
        >
          Apply changes
        </Button>
      </ButtonGroup>
    </Tool>
  );
}
