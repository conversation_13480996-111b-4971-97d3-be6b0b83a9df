import styled from 'styled-components';
import { easing } from 'ext/lib/style';

export const Container = styled.div`
  margin-top: 32px;
  padding: 0 16px;
`;

export const Target = styled.div`
  align-items: center;
  background: var(--background-x-light);
  border-radius: 50%;
  cursor: pointer;
  display: flex;
  justify-content: center;
  height: 10px;
  position: relative;
  transition: ${easing('transform')};
  width: 10px;

  &:hover {
    background: var(--primary-light);
    transform: scale(1.6);
  }

  ${({ selected }) =>
    selected &&
    `
      background: var(--primary-light);
      transform: scale(1.6);

      &::before {
        content: "";
        border: 1px solid var(--background);
        border-radius: 50%;
        cursor: pointer;
        height: 8px;
        transform: scale(1.0);
        width: 8px;
      }
    `}
`;

export const Row = styled.div`
  align-items: center;
  display: flex;
  justify-content: space-between;
  padding: 0 36px;
`;

export const Column = styled.div`
  align-items: center;
  flex-direction: column;
  display: flex;
  height: 80px;
  justify-content: space-between;
  padding: 12px 0;
`;

export const Middle = styled.div`
  align-items: center;
  display: flex;
  justify-content: space-between;
`;

export const Text = styled.p`
  color: var(--text-color);
  font-size: var(--regular);
  text-align: center;
`;

export const Arrow = styled.div`
  border-style: solid;
  border-width: 6px;
  position: absolute;

  ${({ alignment }) => {
    // NOTE: This can probably be programmatically DRY'd up... but we can cross
    //       that bridge later when need
    switch (alignment) {
      case 'bottom-right':
        return `
          border-color: transparent transparent var(--background-x-light);
          bottom: 100%;
          left: 12px;
        `;
      case 'bottom':
        return `
          border-color: transparent transparent var(--background-x-light);
          bottom: 100%;
          left: calc(50% - 6px);
        `;
      case 'bottom-left':
        return `
          border-color: transparent transparent var(--background-x-light);
          bottom: 100%;
          right: 12px;
        `;
      case 'left-bottom':
        return `
          border-color: transparent transparent transparent var(--background-x-light);
          left: 100%;
          top: 12px;
        `;
      case 'left':
        return `
          border-color: transparent transparent transparent var(--background-x-light);
          left: 100%;
          top: calc(50% - 6px);
        `;
      case 'left-top':
        return `
          border-color: transparent transparent transparent var(--background-x-light);
          left: 100%;
          bottom: 12px;
        `;
      case 'top-left':
        return `
          border-color: var(--background-x-light) transparent transparent;
          top: 100%;
          right: 12px;
        `;
      case 'top':
        return `
          border-color: var(--background-x-light) transparent transparent;
          top: 100%;
          left: calc(50% - 6px);
        `;
      case 'top-right':
        return `
          border-color: var(--background-x-light) transparent transparent;
          top: 100%;
          left: 12px;
        `;
      case 'right-top':
        return `
          border-color: transparent var(--background-x-light) transparent transparent;
          right: 100%;
          bottom: 12px;
        `;
      case 'right':
        return `
          border-color: transparent var(--background-x-light) transparent transparent;
          right: 100%;
          top: calc(50% - 6px);
        `;
      case 'right-bottom':
        return `
          border-color: transparent var(--background-x-light) transparent transparent;
          right: 100%;
          top: 12px;
        `;

      default:
        return `
          border-color: transparent;
        `;
    }
  }}
`;

export const Center = styled.div`
  align-items: center;
  background: var(--background-x-light);
  border-radius: 4px;
  display: flex;
  flex-grow: 1;
  height: 80px;
  justify-content: center;
  margin: 12px;
  padding: 24px 12px;
  position: relative;
  width: 100%;
`;
