import React, { useEffect, useState } from 'react';
import PropTypes from 'prop-types';
import {
  Container,
  Target,
  Row,
  Column,
  Middle,
  Text,
  Arrow,
  Center,
} from './styled';

export default function AlignmentPicker({ alignment, onClick }) {
  const [selected, setSelected] = useState(alignment);
  const [hovering, setHovering] = useState(alignment);

  useEffect(() => {
    setSelected(alignment);
  }, [alignment]);

  const handleClick = position => {
    setSelected(position);
    onClick(position);
  };

  return (
    <Container
      aria-label="Alignment selector"
      onMouseLeave={() => setHovering(selected)}
    >
      <Row>
        {['bottom-right', 'bottom', 'bottom-left'].map(position => (
          <Target
            key={position}
            aria-label={position}
            selected={selected === position}
            hovering={hovering === position}
            onClick={() => handleClick(position)}
            onMouseEnter={() => setHovering(position)}
          />
        ))}
      </Row>

      <Middle>
        <Column>
          {['right-bottom', 'right', 'right-top'].map(position => (
            <Target
              key={position}
              aria-label={position}
              selected={selected === position}
              hovering={hovering === position}
              onClick={() => handleClick(position)}
              onMouseEnter={() => setHovering(position)}
            />
          ))}
        </Column>

        <Center>
          <Text>
            {!alignment && 'Automatically choose the optimal position'}

            {alignment === 'auto' && 'Manually choose the optimal position'}

            {alignment &&
              alignment !== 'auto' &&
              `Always show from ${hovering}`}
          </Text>
          <Arrow alignment={hovering} />
        </Center>

        <Column>
          {['left-bottom', 'left', 'left-top'].map(position => (
            <Target
              key={position}
              aria-label={position}
              selected={selected === position}
              hovering={hovering === position}
              onClick={() => handleClick(position)}
              onMouseEnter={() => setHovering(position)}
            />
          ))}
        </Column>
      </Middle>

      <Row>
        {['top-right', 'top', 'top-left'].map(position => (
          <Target
            key={position}
            aria-label={position}
            selected={selected === position}
            hovering={hovering === position}
            onClick={() => handleClick(position)}
            onMouseEnter={() => setHovering(position)}
          />
        ))}
      </Row>
    </Container>
  );
}

AlignmentPicker.propTypes = {
  alignment: PropTypes.oneOf([
    'auto',
    'bottom-right',
    'bottom',
    'bottom-left',
    'left-bottom',
    'left',
    'left-top',
    'top-left',
    'top',
    'top-right',
    'right-top',
    'right',
    'right-bottom',
  ]),
  onClick: PropTypes.func,
};
