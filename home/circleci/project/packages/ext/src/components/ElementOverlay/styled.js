import styled, { css } from 'styled-components';
import { Chyron, Truncated } from 'ext/components/ui';

const HIGHLIGHT_PADDING = 5;

// Relative `top` position of the label when positioned above the element
export const LABEL_HEIGHT = 64;

export const ElementHighlight = styled.div`
  pointer-events: none;
  position: fixed;
  border: 2px solid var(--success-light);
  background: rgba(111, 221, 219, 0.75);
  top: ${({ top }) => top - HIGHLIGHT_PADDING}px;
  left: ${({ left }) => left - HIGHLIGHT_PADDING}px;
  height: ${({ height }) => height + HIGHLIGHT_PADDING * 2}px;
  width: ${({ width }) => width + HIGHLIGHT_PADDING * 2}px;
`;

const positionAbove = css`
  top: -${LABEL_HEIGHT}px;
`;

const positionBelow = css`
  top: calc(100% + 16px);
`;

export const ElementLabel = styled(Chyron)`
  position: relative;
  width: 500px;

  ${({ position }) => (position === 'above' ? positionAbove : positionBelow)};

  ${Truncated} {
    font-family: monospace;
  }
`;
