import React from 'react';
import PropTypes from 'prop-types';
import { Truncated } from 'ext/components/ui';
import { ElementHighlight, ElementLabel, LABEL_HEIGHT } from './styled';
import useHighlights from './use-highlights';

export default function ElementOverlay({ elements = [] }) {
  const highlights = useHighlights(elements);

  return (
    <>
      {highlights.map(({ top, left, height, width, label }, index) => (
        <ElementHighlight
          top={top}
          left={left}
          height={height}
          width={width}
          aria-label="Element highlight"
          // Disabling because there's no unique string to use here
          // eslint-disable-next-line react/no-array-index-key
          key={index}
        >
          {label && (
            <ElementLabel position={top <= LABEL_HEIGHT ? 'below' : 'above'}>
              <Truncated>{label}</Truncated>
            </ElementLabel>
          )}
        </ElementHighlight>
      ))}
    </>
  );
}

ElementOverlay.propTypes = {
  elements: PropTypes.arrayOf(
    PropTypes.shape({
      element: PropTypes.instanceOf(Element),
      label: PropTypes.string,
    })
  ),
};
