import React, { useRef, useState } from 'react';
import useToggle from 'ext/lib/hooks/use-toggle';
import useAnalytics from 'ext/lib/hooks/use-analytics';
import Fade from 'ext/components/Fade';
import {
  BottomBarMenu,
  FontIcon,
  Tooltip,
  SquareButton,
} from 'ext/components/ui';
import useClickOutside from 'ext/lib/hooks/use-click-outside';
import HelpMenu from './HelpMenu';

export function HelpManager() {
  const $button = useRef();
  const $menu = useRef();
  const { track } = useAnalytics();

  const [visibleTooltip, setVisibleTooltip] = useState(false);

  const [visible, toggle] = useToggle();

  useClickOutside([$button, $menu], visible && toggle);

  return (
    <>
      <Tooltip
        label="Help"
        onMouseOut={() => setVisibleTooltip(false)}
        onMouseOver={() => !visible && setVisibleTooltip(true)}
        visible={visibleTooltip}
      >
        <SquareButton
          ref={$button}
          aria-label="Open help menu"
          onClick={() => {
            setVisibleTooltip(false);
            track('Builder view', {
              name: 'Opened Help Menu',
              component: 'HelpManager',
            });
            toggle();
          }}
        >
          <FontIcon icon="question-circle" />
        </SquareButton>
      </Tooltip>

      <BottomBarMenu ref={$menu}>
        <Fade from="bottom" visible={visible}>
          <HelpMenu />
        </Fade>
      </BottomBarMenu>
    </>
  );
}

export default HelpManager;
