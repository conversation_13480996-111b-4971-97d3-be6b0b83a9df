import React from 'react';
import styled from 'styled-components';
import useAnalytics from 'ext/lib/hooks/use-analytics';
import { FontIcon, Menu, List, ListItem } from 'ext/components/ui';

const OptionLabel = styled.span`
  font-weight: var(--medium);
`;

const AnchorWrapper = styled.a`
  text-decoration: none;
  color: var(--white);
`;

const helpMenuOptions = [
  {
    id: 'help-docs',
    label: 'Help docs',
    href: 'https://docs.appcues.com/category/23-building-flows',
    icon: 'info-circle',
    event: 'Clicked Help Docs Link',
  },
  {
    id: 'contact-support',
    label: 'Contact support',
    href: 'mailto:<EMAIL>',
    icon: 'envelope',
    event: 'Clicked Contact Support Link',
  },
  {
    id: 'flow-inspiration',
    label: 'Flow inspiration',
    href: 'https://www.reallygoodux.io',
    icon: 'lightbulb',
    event: 'Clicked Flow Inspiration Link',
  },
  {
    id: 'whats-new',
    label: "What's new",
    href: 'https://appcues.com/whats-new',
    icon: 'paper-plane',
    event: 'Clicked Whats New Link',
  },
];

export function HelpMenu() {
  const { track } = useAnalytics();

  return (
    <Menu>
      <List aria-label="Help menu" role="list">
        {helpMenuOptions.map(option => (
          <ListItem role="listitem" key={option.label}>
            <AnchorWrapper
              href={option.href}
              target="_blank"
              rel="noreferrer"
              onClick={() => {
                track('Builder interaction', {
                  name: option.event,
                  component: 'HelpMenu',
                });
              }}
            >
              <FontIcon icon={option.icon} />
              <OptionLabel aria-label={option.label}>
                {option.label}
              </OptionLabel>
            </AnchorWrapper>
          </ListItem>
        ))}
      </List>
    </Menu>
  );
}

export default HelpMenu;
