import styled from 'styled-components';
import { easing } from 'ext/lib/style';
import { FontIcon, BrandMark } from 'ext/components/ui';
import { TOP_MENU_HEIGHT } from 'ext/constants';

export const Items = styled.ul`
  margin: 0;
  padding: 0;
`;

export const Item = styled.li.attrs(({ href }) =>
  href ? { as: 'a', target: '_blank', rel: 'noreferrer noopener' } : {}
)`
  align-items: center;
  color: ${({ selected }) =>
    selected ? 'var(--secondary)' : 'var(--background-x-light)'};
  cursor: pointer;
  display: flex;
  font-size: var(--small);
  list-style: none;
  padding: 10px 21px;
  text-decoration: none;
  font-weight: bold;
  gap: 8px;

  &:hover {
    background: var(--tint-black);
  }

  ${({ disabled }) =>
    disabled &&
    `
      color: var(--background-x-light);
      opacity: 0.8;
    `}

  ${({ icon }) =>
    icon &&
    `
      display: grid;
      grid-template-columns: 24px 1fr;
      padding: 10px 16px;
    `}
`;

export const Versions = styled.span`
  align-items: center;
  color: var(--background-x-light);
  display: flex;
  font-size: var(--x-small);
  justify-content: center;
  padding: 4px 16px 2px;

  ${FontIcon} {
    font-size: 4px;
    margin: 0 8px;
  }
`;

export const Logo = styled.div`
  background: linear-gradient(90deg, #5c5cff 0%, #7f5fff 96.12%, #8960ff 100%);
  border-radius: 0px 0px 0px 200px;
  width: 60px;
  height: ${TOP_MENU_HEIGHT}px;

  ${BrandMark} {
    margin-top: 14px;
    margin-left: 30px;

    svg path {
      fill: var(--white);
    }
  }
`;

export const Menu = styled.div`
  background: var(--white);
  border-radius: 4px;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.2);
  display: flex;
  flex-direction: column;
  margin: 0;
  padding: 12px 0;
  white-space: nowrap;
`;

export const MenuContainer = styled.div`
  left: 50%;
  position: absolute;
  top: 60px;
  transform: translate3d(-50%, 0, 0);
`;

export const Selector = styled.div`
  align-items: center;
  background: #242a35;
  border-radius: 0px 0px 200px 0px;
  color: var(--white);
  cursor: pointer;
  display: flex;
  font-size: var(--regular);
  font-weight: var(--bold);
  height: ${TOP_MENU_HEIGHT}px;
  justify-content: center;
  width: 200px;
  padding-right: 20px;
`;

export const SelectorText = styled.div`
  text-align: center;
  width: 136px;
`;

export const Separator = styled.hr`
  border: none;
  border-top: 1px solid var(--disabled);
  margin: 8px 0;
`;

export const Container = styled.div`
  display: flex;
  filter: drop-shadow(0 0 10px rgba(0, 0, 0, 0.2));
  height: 36px;
  position: fixed;
  left: 50%;
  transform: ${({ collapsed }) =>
    collapsed ? 'translate3d(-50%, -50px, 0)' : 'translate3d(-50%, 0, 0)'};
  transition: ${easing('transform')};
  top: 0;
  z-index: 10;
`;

export const DisabledTooltipContentLink = styled.a`
  text-decoration: none;
  color: currentColor;
`;
