/* globals PKG_VERSION */

import React, { useCallback, useMemo } from 'react';
import PropTypes from 'prop-types';
import { connect } from 'react-redux';
import { ENABLE_BANNERS, useGates } from 'ext/lib/gates';
import useAnalytics from 'ext/lib/hooks/use-analytics';
import DevTools from 'ext/components/DevTools';
import {
  selectAccountId,
  selectVersion,
  selectFullName,
  selectEmail,
} from 'ext/entities/account';
import { enabled } from 'ext/lib/devtools';
import useToggle from 'ext/lib/hooks/use-toggle';
import { getCurrentBuilder, openBuilder } from 'ext/lib/open-builder';
import { reload, unload } from 'ext/lib/crx';
import { isEntitlementEnabled, PINS_ACCESS } from 'ext/entities/entitlements';
import TopMenu from './TopMenu';

export const TopMenuManager = ({
  accountId,
  collapsed = false,
  onExit,
  onReload,
  version,
  hasPinAccess,
  userEmail,
  userFullName,
}) => {
  const gates = useGates();
  const { track } = useAnalytics();
  const [devtools, launch] = useToggle();
  const selected = getCurrentBuilder();

  console.log({
    hasPinAccess,
    userEmail,
    userFullName,
  });

  const actions = useMemo(() => {
    const handleReload = () => {
      track('Builder interaction', {
        name: 'Clicked Reload Builder',
        component: 'TopMenu',
      });
      onReload();
    };

    const handleClose = () => {
      track('Builder interaction', {
        name: 'Clicked Close Builder',
        component: 'TopMenu',
      });
      onExit();
    };

    return [
      ...(enabled()
        ? [{ icon: 'flask', label: 'Launch DevTools', onClick: launch }]
        : []),
      { icon: 'sync', label: 'Reload Builder', onClick: handleReload },
      { icon: 'times', label: 'Close Builder', onClick: handleClose },
    ];
  }, [launch, onExit, onReload, track]);

  const options = useMemo(
    () => [
      { label: 'Build Flows', value: 'flow' },
      {
        label: 'Drop Pins',
        value: 'pin',
        disabled: !hasPinAccess,
        DisabledTooltipContent: (
          <>
            Click to <strong>upgrade</strong> and unlock Pins
          </>
        ),
      },
      { label: 'Track Events', value: 'event' },
      ...(gates[ENABLE_BANNERS]
        ? [
            {
              label: 'Create Banners',
              value: 'experience',
              experiencePattern: 'banner',
            },
          ]
        : []),
    ],
    [gates, hasPinAccess]
  );

  const handleBuilderChange = useCallback(
    (type, experiencePattern) => {
      if (selected !== type) {
        track('Builder interaction', {
          name: `Clicked ${type} Builder Toggle`,
          component: 'TopMenu',
        });
        openBuilder(type, { pattern: experiencePattern });
      }
    },
    [selected, track]
  );

  return (
    <>
      <TopMenu
        accountId={accountId}
        actions={actions}
        collapsed={collapsed}
        onBuilderChange={handleBuilderChange}
        options={options}
        selected={selected}
        versions={{
          builder: PKG_VERSION,
          extension: version,
        }}
        userEmail={userEmail}
        userFullName={userFullName}
      />

      <DevTools onClose={launch} visible={devtools} />
    </>
  );
};

TopMenuManager.propTypes = {
  accountId: PropTypes.string,
  collapsed: PropTypes.bool,
  onExit: PropTypes.func,
  onReload: PropTypes.func,
  version: PropTypes.string,
  hasPinAccess: PropTypes.bool,
  userEmail: PropTypes.string,
  userFullName: PropTypes.string,
};

const mapStateToProps = state => ({
  accountId: selectAccountId(state),
  version: selectVersion(state),
  hasPinAccess: isEntitlementEnabled(state, PINS_ACCESS, { type: 'boolean' }),
  userEmail: selectEmail(state),
  userFullName: selectFullName(state),
});

const mapDispatchToProps = {
  onExit: unload,
  onReload: reload,
};

export default connect(mapStateToProps, mapDispatchToProps)(TopMenuManager);
