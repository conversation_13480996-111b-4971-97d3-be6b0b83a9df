import React, { useMemo, useRef } from 'react';
import PropTypes from 'prop-types';
import isEmpty from 'lodash.isempty';
import { Tooltip, Icon } from '@appcues/sonar';
import { faLock } from '@fortawesome/pro-regular-svg-icons/faLock';

import Fade from 'ext/components/Fade';
import { FontIcon, BrandMark, Text } from 'ext/components/ui';
import useToggle from 'ext/lib/hooks/use-toggle';
import useAnalytics from 'ext/lib/hooks/use-analytics';
import useClickOutside from 'ext/lib/hooks/use-click-outside';
import { getPortalRoot } from 'ext/lib/document';

import {
  Container,
  Item,
  Items,
  Logo,
  Menu,
  MenuContainer,
  Selector,
  SelectorText,
  Separator,
  Versions,
  DisabledTooltipContentLink,
} from './styled';

export default function TopMenu({
  accountId,
  actions,
  collapsed = false,
  onBuilderChange,
  options,
  selected,
  title,
  versions,
  userEmail,
  userFullName,
}) {
  const $menu = useRef();
  const $selector = useRef();
  const [open, toggle] = useToggle();
  const { track } = useAnalytics();
  const $portal = getPortalRoot();

  useClickOutside([$menu, $selector], open && toggle);

  const header = useMemo(() => {
    const exists = options?.find(({ value }) => selected === value);
    return exists?.label || title;
  }, [options, selected, title]);

  return (
    <Container collapsed={collapsed} role="tablist">
      <Logo>
        <BrandMark size={16} />
      </Logo>
      <Selector ref={$selector} onClick={toggle}>
        <SelectorText>{header}</SelectorText>
        <FontIcon icon="angle-down" color="var(--white)" size="1x" />
      </Selector>

      <MenuContainer ref={$menu}>
        <Fade from="top" visible={open}>
          <Menu role="menu">
            {/* Builder Options */}
            {!isEmpty(options) && (
              <>
                <Items aria-label="Change builder" onClick={toggle}>
                  {options.map(
                    ({
                      label,
                      value,
                      experiencePattern,
                      disabled,
                      DisabledTooltipContent,
                      onDisabledItemClick,
                    }) => (
                      <Item
                        key={value}
                        onClick={() =>
                          disabled
                            ? onDisabledItemClick()
                            : onBuilderChange(value, experiencePattern)
                        }
                        selected={selected === value}
                        disabled={disabled}
                      >
                        <Text>{label}</Text>
                        {disabled && (
                          <Tooltip
                            content={DisabledTooltipContent}
                            delayDuration={0}
                            container={$portal}
                            size="medium"
                          >
                            <DisabledTooltipContentLink
                              href={`https://appcues.typeform.com/to/QD5X3rU2#email=${userEmail}&name=${userFullName}`}
                              target="_blank"
                              rel="noreferrer"
                            >
                              <Icon icon={faLock} />
                            </DisabledTooltipContentLink>
                          </Tooltip>
                        )}
                      </Item>
                    )
                  )}
                </Items>
                <Separator />
              </>
            )}

            {/* Common Links */}
            <Items aria-label="Support links" onClick={toggle}>
              <Item
                href={`${STUDIO_URL}?account=${accountId}`}
                icon
                onClick={() => {
                  track('Builder interaction', {
                    name: 'Clicked Appcues Dashboard Link',
                    component: 'TopMenu',
                  });
                }}
              >
                <FontIcon icon="external-link-alt" size="1x" />
                <Text>Appcues Dashboard</Text>
              </Item>
              <Item
                href="https://docs.appcues.com"
                icon
                onClick={() => {
                  track('Builder interaction', {
                    name: 'Clicked Help and Documentation Link',
                    component: 'TopMenu',
                  });
                }}
              >
                <FontIcon icon="question-circle" size="1x" />
                <Text>Help & Documentation</Text>
              </Item>
            </Items>

            {/* Builder Actions */}
            {!isEmpty(actions) && (
              <Items aria-label="Builder actions" onClick={toggle}>
                {actions.map(({ icon, label, onClick }) => (
                  <Item icon key={label} onClick={onClick}>
                    <FontIcon icon={icon} size="1x" />
                    <Text>{label}</Text>
                  </Item>
                ))}
              </Items>
            )}

            {/* Versions Footer */}
            {!isEmpty(versions) && (
              <>
                <Separator />
                <Versions aria-label="Versions">
                  {versions.builder && (
                    <Text>Builder: {versions.builder || 'x.x.x'}</Text>
                  )}
                  {versions.builder && versions.extension && (
                    <FontIcon icon="circle" />
                  )}
                  {versions.extension && (
                    <Text>Extension: {versions.extension || 'x.x.x'}</Text>
                  )}
                </Versions>
              </>
            )}
          </Menu>
        </Fade>
      </MenuContainer>
    </Container>
  );
}

TopMenu.propTypes = {
  accountId: PropTypes.string,
  actions: PropTypes.arrayOf(
    PropTypes.shape({
      icon: PropTypes.string,
      label: PropTypes.string,
      onClick: PropTypes.func,
    })
  ),
  collapsed: PropTypes.bool,
  onBuilderChange: PropTypes.func,
  options: PropTypes.arrayOf(
    PropTypes.shape({
      label: PropTypes.string,
      value: PropTypes.string,
    })
  ),
  selected: PropTypes.string,
  title: PropTypes.string,
  versions: PropTypes.shape({
    builder: PropTypes.string,
    extension: PropTypes.string,
  }),
  userEmail: PropTypes.string,
  userFullName: PropTypes.string,
};
