import React, { useRef, useState } from 'react';
import PropTypes from 'prop-types';
import useAnalytics from 'ext/lib/hooks/use-analytics';
import useToggle from 'ext/lib/hooks/use-toggle';
import {
  BottomBarMenu,
  SquareButton,
  FontIcon,
  Tooltip,
} from 'ext/components/ui';
import Fade from 'ext/components/Fade';
import useClickOutside from 'ext/lib/hooks/use-click-outside';

export function PreviewManager({ visible: initial = false, children }) {
  const $button = useRef();
  const $menu = useRef();
  const { track } = useAnalytics();

  const [visibleTooltip, setVisibleTooltip] = useState(false);

  const [visible, toggle] = useToggle(initial);

  useClickOutside([$button, $menu], visible && toggle);

  const handleClick = () => {
    setVisibleTooltip(false);
    track('Builder view', {
      name: 'Opened Preview Menu',
      component: 'PreviewManager',
    });
    toggle();
  };

  return (
    <>
      <Tooltip
        label="Preview"
        onMouseOut={() => setVisibleTooltip(false)}
        onMouseOver={() => !visible && setVisibleTooltip(true)}
        visible={visibleTooltip}
      >
        <SquareButton
          ref={$button}
          aria-label="Open preview menu"
          onClick={handleClick}
        >
          <FontIcon icon="eye" />
        </SquareButton>
      </Tooltip>

      <BottomBarMenu ref={$menu}>
        <Fade from="bottom" visible={visible}>
          {children}
        </Fade>
      </BottomBarMenu>
    </>
  );
}

PreviewManager.propTypes = {
  children: PropTypes.node,
  visible: PropTypes.bool,
};

export default PreviewManager;
