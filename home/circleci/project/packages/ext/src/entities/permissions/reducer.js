import { HIDE_DOMAIN_PERMISSION_MODAL } from './actions';

const initialState = {
  hidePermissionModal: true,
};

export default function permissions(state = initialState, action) {
  switch (action.type) {
    case HIDE_DOMAIN_PERMISSION_MODAL:
      return {
        ...state,
        hidePermissionModal: true,
      };
    default:
      return state;
  }
}

export const selectHidePermissionModal = state => {
  return state.permissions.hidePermissionModal;
};
