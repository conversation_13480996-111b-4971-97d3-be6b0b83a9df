import { put, getContext, takeEvery, call } from 'redux-saga/effects';
import { INITIALIZE } from 'ext/root/root-actions';
import { resolve, reject, REFRESH_ENTITLEMENTS } from './actions';

export const normalize = results => {
  return results.reduce((acc, next) => {
    acc[next.name] = next;
    return acc;
  }, {});
};

function* fetchEntitlements() {
  try {
    const api = yield getContext('api');
    const results = yield call(api.getEntitlements);
    yield put(resolve(normalize(results)));
  } catch (error) {
    yield put(reject(error));
  }
}

export default function* saga() {
  yield takeEvery(INITIALIZE, fetchEntitlements);
  yield takeEvery(REFRESH_ENTITLEMENTS, fetchEntitlements);
}
