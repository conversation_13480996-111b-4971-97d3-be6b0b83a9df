import { call, getContext, put, takeEvery, all } from 'redux-saga/effects';
import { FETCH_BOOTSTRAP } from 'ext/lib/track/interaction-types';
import { INITIALIZE } from 'ext/root/root-actions';
import { completeInteraction } from 'ext/lib/track';
import { resolve, reject } from './actions';

/**
 * Prune entities with id and allowed fields
 *
 * @param {Entity[]} list - list of entities
 * @returns {Collection} pruned collection
 */
const prune = (list, fields) =>
  list.reduce((accumulator, { id, ...rest }) => {
    if (id) {
      accumulator[id] = fields.reduce(
        (acc, field) => {
          acc[field] = rest[field];
          return acc;
        },
        { id }
      );
    }
    return accumulator;
  }, {});

/**
 * Remap styles/themes into SDK injectable format
 *
 * @param {Collection} styles - Styles collection
 * @return {Collection} Remapped styles collection
 */
const remapStyles = styles => {
  return Object.entries(styles)
    .sort(([, a], [, b]) => a.name.localeCompare(b.name))
    .reduce((acc, [, style]) => {
      acc[style.id] = {
        id: style.id,
        name: style.name,
        isDefault: style.isDefault,
        globalStyling: style.attributes.globalCss,
        typekitId: style.attributes.typekitId,
        globalBeaconColor: style.attributes.beaconColor,
        globalHotspotAnimation: style.attributes.hotspotAnimation,
        globalBeaconStyle: style.attributes.beaconStyle,
        theme: style.attributes.theme,
      };
      return acc;
    }, {});
};

/**
 * Parse fields from boootstrap response data to (minimal required) collections
 *
 * @param {Bootstrap[]} response - original bootstrap response
 * @returns {object<string,Collection>} response as pruned collections
 */
const parse = ({ tags, templates, styles }, bootstrapProperties) => {
  const parsedResponse = {
    tags: prune(Object.values(tags), ['name']),
    templates: prune(templates, ['name', 'step', 'stepType']),
    themes: remapStyles(styles),
  };

  // This only includes the properties that are need for each builder
  // that pass on the initialize() action call on the inject file.
  if (bootstrapProperties) {
    return Object.entries(parsedResponse).reduce((acc, [key, value]) => {
      if (bootstrapProperties.includes(key)) acc[key] = value;
      return acc;
    }, {});
  }

  return parsedResponse;
};

export function* fetchBootstrap(action) {
  try {
    const { bootstrapProperties } = action.payload;
    const { getTags, getTemplates, getThemes } = yield getContext('api');
    const [tags, templates, styles] = yield all([
      call(getTags),
      call(getTemplates),
      call(getThemes),
    ]);

    yield put(resolve(parse({ tags, templates, styles }, bootstrapProperties)));
    yield put(completeInteraction(FETCH_BOOTSTRAP));
  } catch (error) {
    yield put(reject(error));
  }
}

export default function* bootstrapSaga() {
  yield takeEvery(INITIALIZE, fetchBootstrap);
}
