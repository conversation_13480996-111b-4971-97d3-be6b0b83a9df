import {
  CHILD_MOVED,
  <PERSON>EM_CREATED,
  ITEM_FLUSHED,
  ITEM_INSERTED,
  ITEM_REMOVED,
  ITEM_REPLACED,
  ITEM_UPDATED,
  REQUEST_REJECTED,
  REQUEST_RESOLVED,
} from 'ext/lib/collections';

export default function status(state = null, action) {
  switch (action.type) {
    case ITEM_CREATED:
    case ITEM_UPDATED:
    case CHILD_MOVED:
      return 'saving';

    case REQUEST_RESOLVED:
    case ITEM_INSERTED:
    case ITEM_REPLACED:
    case ITEM_FLUSHED:
    case ITEM_REMOVED:
      return 'saved';

    case REQUEST_REJECTED:
      return 'error';

    default:
      return state;
  }
}

export const selectStatus = state => state.status;
