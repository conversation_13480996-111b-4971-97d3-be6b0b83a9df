import { createCollectionReducer, resolveSiblings } from 'ext/lib/collections';

const TYPE = 'themes';

const reducer = createCollectionReducer(TYPE);

export default resolveSiblings(reducer, TYPE);

export const selectThemes = state => state.themes;
export const selectTheme = (state, id) => selectThemes(state)[id];
export const selectDefaultTheme = state =>
  Object.values(selectThemes(state)).find(({ isDefault }) => isDefault);
export const selectThemeOrDefault = (state, id) =>
  selectTheme(state, id) || selectDefaultTheme(state);
