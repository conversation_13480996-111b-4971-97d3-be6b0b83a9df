import { call, getContext, put, takeLatest } from 'redux-saga/effects';
import { FETCH_FLOWS_SUMMARY } from 'ext/lib/track/interaction-types';
import { INITIALIZE } from 'ext/root/root-actions';
import { completeInteraction } from 'ext/lib/track';
import { reject, resolve } from './actions';

const VALID_STATES = new Set(['DRAFT', 'PUBLISHED']);

/**
 * Parse flows summary response data to (minimal required) collection
 *
 * @param {FlowSummary[]} response - original flows summary response
 * @returns {Collection} response as pruned collection
 */
const parse = response =>
  response.reduce((acc, { flow }) => {
    const { id, name, state, testVersionOf, previewUrl } = flow;

    if (id && !testVersionOf && VALID_STATES.has(state)) {
      acc[flow.id] = { id, name, previewUrl, state };
    }

    return acc;
  }, {});

export function* fetchFlowsSummary() {
  try {
    const { getFlowsSummary } = yield getContext('api');
    const response = yield call(getFlowsSummary);
    yield put(resolve(parse(response)));
    yield put(completeInteraction(FETCH_FLOWS_SUMMARY));
  } catch (error) {
    yield put(reject(error));
  }
}

export default function* flowsSummarySaga() {
  yield takeLatest(INITIALIZE, fetchFlowsSummary);
}
