import {
  createCollectionReducer,
  composeReducers,
  ITEM_INSERTED,
} from 'ext/lib/collections';

const TYPE = 'flowsSummary';
const addFlowSummaryReducer = (state, action) => {
  const { payload } = action;
  if (action?.meta?.type === 'flow' && action.type === ITEM_INSERTED) {
    const { flow } = payload;
    return { ...state, [flow?.id]: flow };
  }
  return state;
};
/**
 * Reducer
 */

export default composeReducers(
  addFlowSummaryReducer,
  createCollectionReducer(TYPE)
);

/**
 * SELECTORS
 */
export const selectFlowsSummary = state => state[TYPE];

export const selectFlowSummary = (state, id) => state[TYPE][id];
