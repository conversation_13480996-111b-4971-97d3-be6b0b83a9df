/**
 * Abridged and modified version of crx-ui/utils/location
 */

import normalizeUrl from 'normalize-url';

const PLACEHOLDER_PATTERN = /(%7B%7B.*?%7D%7D|{{.*?}})/g;
const WILDCARD_PATTERN = /\*/g;
const TRAILING_SLASH_AND_WILDCARD_PATTERN = /(\/\.\*)$/;
const REGEX_SYMBOLS_PATTERN = /[$()+.?[\\\]^{|}]/g;

const ESCAPE_TOKEN = '\\$&';
const ANY_CHAR_TOKEN = '.*';
const ANY_PATH_TOKEN = '/.+';

function toRegExp(url) {
  const regexUrl = url
    .replace(REGEX_SYMBOLS_PATTERN, ESCAPE_TOKEN)
    .replace(WILDCARD_PATTERN, ANY_CHAR_TOKEN)
    .replace(PLACEHOLDER_PATTERN, ANY_CHAR_TOKEN)
    .replace(TRAILING_SLASH_AND_WILDCARD_PATTERN, ANY_PATH_TOKEN);
  return new RegExp(`^${regexUrl}$`);
}

function cleanup(raw) {
  const url = raw.startsWith('/')
    ? `${window.location.hostname}${
        window.location.port && `:${window.location.port}`
      }${raw}`
    : raw;
  return normalizeUrl(url, { forceHttps: true });
}

export const checkForWildcards = url =>
  new RegExp(WILDCARD_PATTERN, '').test(url);

export const checkForPlaceholders = url =>
  new RegExp(PLACEHOLDER_PATTERN, '').test(url);

export function doesLocationMatch(target, current = window.location.href) {
  // It seems the target URL and/or the build URL could be blank e.g. an empty
  // string, so we need to default to a single slash path to ensure that
  // `normalize-url` does not throw an exception. Since an empty string is falsy
  // but not undefined, we have to test it directly using OR rather than using
  // default arguments...
  const targetUrl = cleanup(target || '/');
  const currentUrl = cleanup(current || '/');

  const retarget = toRegExp(targetUrl);

  return targetUrl === currentUrl || retarget.test(currentUrl);
}
