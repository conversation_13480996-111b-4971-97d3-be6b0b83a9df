/* globals STANDALONE */

import ReactDOM from 'react-dom';

const cancelMouseEvent = e => {
  e.stopPropagation();
};

/*
 * Builder web-component encapsulating application within Shadow DOM
 *
 * credit: https://github.com/facebook/react/issues/9242#issuecomment-534096832
 */
export default class AppcuesBuilder extends HTMLElement {
  static observedAttributes = [
    'auth-token',
    'experienceId',
    'theme',
    'builder-pattern',
  ];

  constructor() {
    super();
    this.theme = 'dark';

    const shadowRoot = this.attachShadow({ mode: 'open' });

    shadowRoot.innerHTML = `
      <div id="react-root" data-theme=${this.theme} dir="ltr"></div>
      <div id="portal-root" dir="ltr">
      </div>
      <div data-theme="light" id="portal-root-light" dir="ltr"
      style="position: relative;isolation: isolate;z-index: ${Number.MAX_SAFE_INTEGER}">
      </div>
    `;

    shadowRoot.createElement = (...args) => document.createElement(...args);
    shadowRoot.createElementNS = (...args) => document.createElementNS(...args);
    shadowRoot.createTextNode = (...args) => document.createTextNode(...args);
  }

  connectedCallback() {
    const $reactRoot = this.shadowRoot.querySelector('#react-root');
    Object.defineProperty($reactRoot, 'ownerDocument', {
      value: this.shadowRoot,
    });

    const event = new CustomEvent('builder:connected', {
      detail: {
        rootContainer: $reactRoot,
        container: this,
        shadowRoot: this.shadowRoot,
      },
    });

    // When the builder is standalone, we need to dispatch the event
    // on the window so that the builder can be injected into the page.
    // This is needed,  because the listener is added only once to the window
    // and in a SPA application the listener is not initialized again
    (STANDALONE ? window : this).dispatchEvent(event);

    const $portalRoot = this.shadowRoot.querySelector('#portal-root');
    // onClick and onPointerDown are
    // used to generally close elements
    // that may be the pin's target.
    // In this case closing an element will
    // force the pin to unmount since the target can't be found
    // to avoid this problem, we cancel bubbling those events.
    // In the case we are cancelling anything that comes from portal
    // like the minibar which is rendered here even if the editor is inline
    // and also anything that comes from reactRoot, like a click in the sidebar etc..
    $portalRoot.addEventListener('click', cancelMouseEvent);
    $portalRoot.addEventListener('pointerdown', cancelMouseEvent);

    $reactRoot.addEventListener('click', cancelMouseEvent);
    $reactRoot.addEventListener('pointerdown', cancelMouseEvent);
  }

  disconnectedCallback() {
    const $reactRoot = this.shadowRoot.querySelector('#react-root');
    ReactDOM.unmountComponentAtNode($reactRoot);
  }

  setTheme(newTheme) {
    const $reactRoot = this.shadowRoot.querySelector('#react-root');
    $reactRoot.dataset.theme = newTheme;
  }

  emitChangeEvent(name, newValue, oldValue = null) {
    const event = new CustomEvent('builder:change', {
      detail: {
        name,
        newValue,
        oldValue,
      },
    });
    this.dispatchEvent(event);
  }

  attributeChangedCallback(name, oldValue, newValue) {
    if (name === 'theme') {
      this.setTheme(newValue);
    }

    this.emitChangeEvent(name, newValue, oldValue);
  }
}
