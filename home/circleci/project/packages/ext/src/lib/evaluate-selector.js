export const SHADOW_ROOT_TOKEN = '|shadow-root|';
export const IFRAME_ROOT_TOKEN = '|iframe|';

export const evaluateSelectorAll = (selector, context = document) => {
  if (
    selector.includes(SHADOW_ROOT_TOKEN) ||
    selector.includes(IFRAME_ROOT_TOKEN)
  ) {
    return (
      selector
        .split(SHADOW_ROOT_TOKEN)
        .flatMap(sel => sel.split(IFRAME_ROOT_TOKEN))
        .reduce((els, selectorSegment) => {
          if (els === null) {
            return [...context.querySelectorAll(selectorSegment)];
          }
          return els.flatMap(el => {
            if (el.shadowRoot)
              return [...el.shadowRoot.querySelectorAll(selectorSegment)];
            if (el.tagName === 'IFRAME')
              return [...el.contentDocument.querySelectorAll(selectorSegment)];
            return [];
          });
        }, null) ?? []
    );
  }
  return context.querySelectorAll(selector);
};

export const evaluateSelector = (selector, context = document) => {
  const results = evaluateSelectorAll(selector, context);
  return results.length > 0 ? results[0] : null;
};
