// Matches spaces separated strings in a selector (and not including spaces in between quoted attributes [data-single='single quote'])
const PATTERN = /(?:[^\s"']+|["'][^"']*["'])+/g;

/**
 * Format selector attribute based on type
 *
 * @typedef {Object<string[]>} SelectorOptions
 *
 * @param {keyof SelectorOptions} type - Attribute type
 * @param {string} value - Attribute value
 * @return {string} Formated attribute
 */
const formatAttributes = (type, value) => {
  switch (type) {
    case 'tag':
      return value;
    case 'id':
      return `#${value}`;
    case 'class':
      return `.${value}`;
    default:
      return `[${type}="${value}"]`;
  }
};

/**
 * Build selector string based on checked selector attributes and current selector string
 *
 * @typedef {Object<Object<boolean>>} CheckedAttributes
 *
 * @param {CheckedAttributes} attributes - Checked attributes
 * @param {array} parents - Array of parent selectors derived from selector string
 * @return {string} Derived selector
 */
export const buildSelector = (parents, attributes) => {
  const updatedChild = Object.values(attributes).reduce(
    (acc, values) =>
      Object.entries(values).reduce(
        (memo, [value, checked]) => (checked ? `${memo}${value}` : memo),
        acc
      ),
    ''
  );

  // If there are no parents, return the updated child
  if (parents.length === 0) {
    return updatedChild;
  }

  // Otherwise, if there are parents, join them and append updatedChild
  return `${parents.join(' ')} ${updatedChild}`;
};

/**
 * Sort selector options so order is: tagname, ids, classes, rest
 *
 * @typedef {Object<string[]>} SelectorOptions
 *
 * @param {SelectorOptions} options - Selector options
 * @return {SelectorOptions} Sorted selector options
 */
export const sortSelectorOptions = ({
  tag,
  tagName,
  id,
  class: className,
  ...rest
}) => ({
  ...(tag && { tag }),
  ...(tagName && { tag: tagName }),
  ...(id && { id }),
  ...(className && { class: className }),
  ...rest,
});

/**
 * Process options with selector to determine which options are currently used
 * in the provided selector
 *
 * @typedef {Object<string[]>} SelectorOptions
 * @typedef {Object<Object<boolean>>} CheckedOptions
 *
 * @param {SelectorOptions} options - Selector options
 * @param {string} selector - Selector
 * @return {CheckedOptions} Sorted selector options
 */
export const processSelectorOptions = (options, selector) =>
  Object.entries(options).reduce((accumulator, [type, values]) => {
    const list = Array.isArray(values) ? values : [values];
    accumulator[type] = list.reduce((acc, value) => {
      const formatted = formatAttributes(type, value);

      // Tags are first in selectors and we wanna make sure we're checking
      // the right spot in selector to make sure it exists as part of that string
      // (also should be case insensitive)
      if (type === 'tag') {
        const [child = ''] = (selector.match(PATTERN) || []).reverse();
        acc[formatted] = child.toLowerCase().startsWith(value.toLowerCase());
      } else {
        acc[formatted] = selector.includes(formatted);
      }
      return acc;
    }, {});
    return accumulator;
  }, {});

/**
 * Create order options based on total count of matched elements
 *
 * @typedef {Object<(string|number)>} Options
 *
 * @param {count} count - Count of matched elements
 * @return {Options} Order options
 */
export const createOrderOptions = count =>
  Array.from({ length: count }).map((_, index) => ({
    value: index,
    label: `${index + 1} of ${count}`,
  }));
