/* globals CUSTOMER_API_URL */
import jwtDecode from 'jwt-decode';

// Auth endpoints
export const LOGIN_URL = `${CUSTOMER_API_URL}/v1/login`;
const REFRESH_URL = `${CUSTOMER_API_URL}/v1/web/refresh`;

export const getAccountDetailsFromToken = token => {
  const {
    claims,
    account_id: accountId,
    user_id: userId,
    uid,
  } = jwtDecode(token);

  return {
    accountId: accountId ?? claims?.account_id,
    uid: userId ?? uid,
    ...claims,
  };
};

export const getAuth = token => {
  const { accountId } = getAccountDetailsFromToken(token);
  return { token, accountId };
};

// Options config as per auth client
export const refreshToken = async token => {
  const body = { jwt_token: token };
  const response = await fetch(REFRESH_URL, {
    method: 'POST',
    credentials: 'include',
    mode: 'cors',
    cache: 'no-store',
    headers: {
      'Content-type': 'application/json',
      Authorization: `Bearer ${body.jwt_token}`,
    },
    body: JSON.stringify(body),
  });

  if (response.status === 401) {
    throw new Error('Unauthorized response from auth service');
  } else if (response.status !== 200) {
    throw new Error(
      `Unknown error occurred with request to auth service: ${response.status}`
    );
  }

  const responseJson = await response.json();
  return responseJson.payload.firebase;
};

export const isSpoofingToken = token => {
  const {
    spoofing,
    original_user_id: originalUserId,
    original_account_id: originalAccountId,
  } = getAccountDetailsFromToken(token);

  if (
    !spoofing ||
    typeof spoofing !== 'boolean' ||
    !originalUserId ||
    typeof originalUserId !== 'string' ||
    !originalAccountId ||
    typeof originalAccountId !== 'string'
  ) {
    return false;
  }

  return spoofing;
};
