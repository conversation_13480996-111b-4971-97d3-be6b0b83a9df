import {
  getCustomerApiEndpoints,
  getDashboardApiEndpoints,
  getAnalyticsApiEndpoints,
} from './endpoints';
import { getAuth } from './auth';

const getApiEndpoints = auth => {
  return {
    ...getCustomerApiEndpoints(auth),
    ...getDashboardApiEndpoints(auth),
    ...getAnalyticsApiEndpoints(auth),
  };
};

const apiClient = token => {
  let endpoints = {};

  // This functions is used to create new instance of API endpoints with a new token
  const create = async () => {
    const auth = getAuth(token);
    endpoints = getApiEndpoints(auth);
  };

  // This function is used to refresh the token of the current instance of API endpoints
  // Could be used to update auth data when the token is refreshed
  // Or maybe when using spoofing
  const refreshClient = async () => {
    await create();
  };

  // This function is used to get the current instance of API endpoints
  const getEndpoints = async () => {
    await create();

    return endpoints;
  };

  return {
    refreshClient,
    getEndpoints,
  };
};

export default apiClient;
