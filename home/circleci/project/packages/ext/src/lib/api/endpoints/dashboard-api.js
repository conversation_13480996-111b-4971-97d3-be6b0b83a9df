/* globals APPCUES_API_URL */

import { createFetch } from 'ext/lib/api/fetch-client';

export const getDashboardApiEndpoints = auth => {
  const fetchDashboardAPI = createFetch(`${APPCUES_API_URL}/v1`, auth);

  return {
    getFlowPreview: id => fetchDashboardAPI(`flow_preview/${id}`),

    getUserProperties: () => fetchDashboardAPI('profile_attributes'),

    trackMetric: metric =>
      fetchDashboardAPI(
        'metrics',
        {
          method: 'POST',
          body: JSON.stringify(metric),
        },
        '/'
      ),
  };
};
