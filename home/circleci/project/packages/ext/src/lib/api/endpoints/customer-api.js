/* globals CUSTOMER_API_URL */
import { base64ToFile } from 'ext/lib/files';
import { createFetch } from 'ext/lib/api/fetch-client';

export const getCustomerApiEndpoints = auth => {
  const fetchCustomerAPI = createFetch(`${CUSTOMER_API_URL}/v1`, auth);
  const fetchCustomerAPIv2 = createFetch(`${CUSTOMER_API_URL}/v2`, auth);

  return {
    // gates
    getAccountGates: () =>
      fetchCustomerAPI('gates').then(gates =>
        gates.reduce((all, gate) => {
          return {
            ...all,
            ...gate,
          };
        }, {})
      ),
    // user endpoint
    getUser: userId => fetchCustomerAPI(`users/${userId}`),
    updateUser: (userId, patch) =>
      fetchCustomerAPI(
        `users/${userId}`,
        {
          method: 'PATCH',
          body: JSON.stringify(patch),
        },
        '/'
      ),
    // Flow endpoints
    getFlowsSummary: () => fetchCustomerAPI('flows?summary=true'),
    getFlow: id => fetchCustomerAPI(`flows/${id}`),
    getLaunchpadEligibleFlows: () =>
      fetchCustomerAPI(
        'flows?published=true&widget_filter[]=include&widget_filter[]=page'
      ),
    createFlow: flow =>
      fetchCustomerAPI('flows', {
        method: 'POST',
        body: JSON.stringify(flow),
      }),
    replaceFlow: (id, flow) =>
      fetchCustomerAPI(`flows/${id}/replace-draft`, {
        method: 'POST',
        body: JSON.stringify(flow),
      }),
    updateFlow: (id, patch) =>
      fetchCustomerAPI(`flows/${id}`, {
        method: 'PATCH',
        body: JSON.stringify(patch),
      }),
    createStepGroup: ({ flowId, group }) =>
      fetchCustomerAPI(`flows/${flowId}/stepGroups`, {
        method: 'POST',
        body: JSON.stringify(group),
      }),
    updateStepGroup: ({ flowId, stepType, stepGroupId }, data) =>
      fetchCustomerAPI(
        `flows/${flowId}/stepGroups/${stepType}/${stepGroupId}`,
        {
          method: 'PATCH',
          body: JSON.stringify(data),
        }
      ),
    updateStepChild: ({ flowId, stepType, stepGroupId, stepChildId }, data) =>
      fetchCustomerAPI(
        `flows/${flowId}/stepGroups/${stepType}/${stepGroupId}/stepChildren/${stepChildId}`,
        {
          method: 'PATCH',
          body: JSON.stringify(data),
        }
      ),
    moveStepGroup: ({ flowId, stepType, stepGroupId }, toIndex) =>
      fetchCustomerAPI(
        `flows/${flowId}/stepGroups/${stepType}/${stepGroupId}/move`,
        {
          method: 'PATCH',
          body: JSON.stringify({ toIndex }),
        }
      ),
    createStepChild: ({ flowId, stepType, stepGroupId, step }) =>
      fetchCustomerAPI(
        `flows/${flowId}/stepGroups/${stepType}/${stepGroupId}/stepChildren`,
        {
          method: 'POST',
          body: JSON.stringify(step),
        }
      ),
    moveStepChild: ({ flowId, stepType, stepGroupId, stepChildId }, toIndex) =>
      fetchCustomerAPI(
        `flows/${flowId}/stepGroups/${stepType}/${stepGroupId}/stepChildren/${stepChildId}/move`,
        {
          method: 'PATCH',
          body: JSON.stringify({ toIndex }),
        }
      ),
    deleteStepGroup: ({ flowId, stepType, stepGroupId }) =>
      fetchCustomerAPI(
        `flows/${flowId}/stepGroups/${stepType}/${stepGroupId}`,
        {
          method: 'DELETE',
        }
      ),
    deleteStepChild: ({ flowId, stepType, stepGroupId, stepChildId }) =>
      fetchCustomerAPI(
        `flows/${flowId}/stepGroups/${stepType}/${stepGroupId}/stepChildren/${stepChildId}`,
        {
          method: 'DELETE',
        }
      ),
    getLayouts: () => fetchCustomerAPI('flows/template'),

    // Experience endpoints
    getExperiencesSummary: (type = 'persistent') =>
      fetchCustomerAPI(`experiences?platform=web&type=${type}&summary=true`),
    getExperience: id => fetchCustomerAPI(`experiences/${id}`),
    createStep: (experienceId, step) =>
      fetchCustomerAPI(`experiences/${experienceId}/steps`, {
        method: 'POST',
        body: JSON.stringify(step),
      }),
    createExperience: experience =>
      fetchCustomerAPI('experiences', {
        method: 'POST',
        body: JSON.stringify(experience),
      }),
    updateExperience: (id, patch, replace = false) =>
      fetchCustomerAPI(
        `experiences/${id}${replace ? '?replace_steps=true' : ''}`,
        {
          method: 'PATCH',
          body: JSON.stringify(patch),
        }
      ),
    updateStep: ({ experienceId, stepId }, delta) =>
      fetchCustomerAPI(`experiences/${experienceId}/steps/${stepId}`, {
        method: 'PATCH',
        body: JSON.stringify(delta),
      }),
    removeStep: (experienceId, stepId) =>
      fetchCustomerAPI(`experiences/${experienceId}/steps/${stepId}`, {
        method: 'DELETE',
      }),

    getCustomEvents: () => fetchCustomerAPI('custom_events'),
    createCustomEvent: event =>
      fetchCustomerAPI('custom_events', {
        method: 'POST',
        body: JSON.stringify(event),
      }),
    updateCustomEvent: (id, delta) =>
      fetchCustomerAPI(`custom_events/${id}`, {
        method: 'PATCH',
        body: JSON.stringify(delta),
      }),
    getUserAccounts: userId =>
      fetchCustomerAPI(`/users/${userId}/accounts`, {}, ''),
    despoof: token =>
      fetchCustomerAPI(
        `/web/despoof`,
        {
          method: 'POST',
          body: JSON.stringify({ jwt_token: token }),
        },
        ''
      ),
    getTemplates: () => fetchCustomerAPI(`templates`),
    createTemplate: template =>
      fetchCustomerAPI(`templates`, {
        method: 'POST',
        body: JSON.stringify(template),
      }),
    updateTemplate: ({ templateId, template }) =>
      fetchCustomerAPI(`templates/${templateId}`, {
        method: 'PATCH',
        body: JSON.stringify(template),
      }),
    deleteTemplate: templateId => {
      return fetchCustomerAPI(`templates/${templateId}`, {
        method: 'DELETE',
      });
    },
    getTags: () => fetchCustomerAPIv2(`tags`),
    getThemes: () => fetchCustomerAPI(`themes`),
    updateUsageProperties: (userId, usageProperties) =>
      fetchCustomerAPI(
        `users/${userId}`,
        {
          method: 'PATCH',
          body: JSON.stringify({ usageProperties }),
        },
        '/'
      ),

    getUserPropertiesLabels: () =>
      fetchCustomerAPI('profile_attributes/labels'),
    getUserAccountsV2: () => fetchCustomerAPIv2(''),
    // Doc site integrations (knowledge base) endpoints
    getDocSiteIntegrations: () => fetchCustomerAPI('doc_site_integrations'),
    // Entitlements
    getEntitlements: () => fetchCustomerAPIv2('entitlements'),

    // Locales API methods
    getLocales: () => fetchCustomerAPI('locales'),
    createLocale: body =>
      fetchCustomerAPI('locales', {
        method: 'POST',
        body: JSON.stringify(body),
      }),

    removeLocale: localeId =>
      fetchCustomerAPI(`locales/${localeId}`, {
        method: 'DELETE',
      }),

    updateLocale: (localeId, body) =>
      fetchCustomerAPI(`locales/${localeId}`, {
        method: 'PATCH',
        body: JSON.stringify(body),
      }),

    autoTranslate: xliff =>
      fetchCustomerAPI('assistant/translate-xliff', {
        method: 'POST',
        body: xliff,
        account: true,
        contentType: 'application/xml',
        parse: false,
      }),
    fetchImages: ({ number = 1, size = 50 }) =>
      fetchCustomerAPI(
        `images?sort_key=created_at&sort_direction=desc&page_number=${number}&page_size=${size}`
      ),
    uploadImage: message => {
      const file = base64ToFile(message);

      const formData = new FormData();
      formData.append('file', file);

      return fetchCustomerAPI(`images/upload`, {
        method: 'POST',
        body: formData,
        ignoreContentType: true,
      });
    },

    // Rule
    getRule: id => fetchCustomerAPI(`rules/${id}`),

    updateRule: (id, delta) =>
      fetchCustomerAPI(`rules/${id}`, {
        method: 'PATCH',
        body: JSON.stringify(delta),
      }),

    // surveys
    getSurveyTypes: () =>
      fetchCustomerAPI(
        'graphql',
        {
          method: 'POST',
          body: JSON.stringify({
            query: `
            query getSurveyTypes {
              surveyTypes {
                id
                name
                surveyFieldTypes {
                  id
                  name
                }
              }
            }
          `,
          }),
        },
        '/'
      ),
    createSurveyEntity: input =>
      fetchCustomerAPI(
        'graphql',
        {
          method: 'POST',
          body: JSON.stringify({
            query: `
              mutation createSurveyEntity($input: CreateSurveyEntityInput!) {
                createSurveyEntity(input: $input) {
                  surveyEntity {
                    id
                    name
                  }
                }
              }
          `,
            variables: {
              input,
            },
          }),
        },
        '/'
      ),
    createSurveyField: input =>
      fetchCustomerAPI(
        'graphql',
        {
          method: 'POST',
          body: JSON.stringify({
            query: `
             mutation createSurveyField($input: CreateSurveyFieldInput!) {
              createSurveyField(input: $input) {
                surveyField {
                  id
                  name
                }
              }
            }
          `,
            variables: {
              input,
            },
          }),
        },
        '/'
      ),
  };
};
