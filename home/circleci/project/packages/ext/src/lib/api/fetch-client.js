import HTTPError from 'ext/lib/api/http-error';

export const createFetch =
  (baseUrl, { accountId, token }) =>
  async (path, options = {}, root = `/accounts/${accountId}/`) => {
    const {
      contentType = 'application/json',
      parse = true,
      ignoreContentType = false,
      headers = {},
    } = options;

    const response = await fetch(`${baseUrl}${root}${path}`, {
      ...options,
      headers: {
        ...headers,
        ...(ignoreContentType ? {} : { 'Content-Type': contentType }),
        Authorization: `Bearer ${token}`,
      },
    });

    if (!response.ok) {
      const error = await response.json();
      throw new HTTPError(JSON.stringify(error));
    }

    const text = await response.text();

    return text && text.length > 0 ? (parse ? JSON.parse(text) : text) : null;
  };
