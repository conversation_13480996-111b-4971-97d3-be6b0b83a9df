/* globals ANALYTICS_API_URL */

import { createFetch } from 'ext/lib/api/fetch-client';

const createAnalyticsApiQuery = ({ accountId, token }) => {
  const fetchAnalyticsAPI = createFetch(ANALYTICS_API_URL, {
    accountId,
    token,
  });

  return query =>
    fetchAnalyticsAPI(
      `query?account_id=${accountId}`,
      {
        method: 'POST',
        body: JSON.stringify({
          account_id: accountId,
          ...query,
        }),
      },
      '/v2/'
    );
};

export const getAnalyticsApiEndpoints = auth => {
  const queryAnalyticsAPI = createAnalyticsApiQuery(auth);

  return {
    getEventCounts: (eventNames, { start = '7 days ago', end = Date.now() }) =>
      queryAnalyticsAPI({
        metrics: ['events'],
        start_time: start,
        end_time: end,
        dimensions: ['event'],
        conditions: [['event', 'in', eventNames]],
      }),
  };
};
