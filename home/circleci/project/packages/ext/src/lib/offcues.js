/* globals NEW_EXTENSION, chrome */
/**
 * In order to interact with the host DOM, we need to inject a script that
 * runs in that context. This script hides any Appcues elements and resets
 * the SDK on the client.
 *
 * We filter content without .injectable class, the injectable SDK can be too fast
 * creating a racing condition and avoiding the content in preview mode to be displayed.
 */
const scriptContent = `
(function (){if (window.Appcues) {
[...document.querySelectorAll('.appcues:not(.injectable)')].forEach(el => el.style.display = 'none');
window.Appcues.reset();}
}());`;

/**
 * In order to interact with the host DOM, we need to inject a script that
 * runs in that context. This script pause the live experience.
 */

export const getPauseLiveExperienceScript = id => `
  (function (){
    if (window.Appcues) {
      window.Appcues.experience.pause('${id}');
    }
  }());`;

/**
 * prevent Appcues content from showing when using the Builder
 *
 * @param {Document} $doc - host window Document element
 */

export default function appcuesOff($doc, content = scriptContent) {
  // this can't be done by inserting a dynamic script
  // using the new manifest v3
  // so we need to ignore for now, otherwise the extension can't be opened
  // a ticket will be created so we can address this issue.
  if (NEW_EXTENSION) {
    chrome.runtime.sendMessage({
      action: 'appcues:deactivate-flows',
    });
  } else {
    const script = $doc.createElement('script');
    script.async = true;
    script.innerHTML = content;
    $doc.head.append(script);
  }
}
