export const getFileHTML = file => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();

    reader.addEventListener('load', () => {
      const fileContent = reader.result;

      resolve(fileContent);
    });

    reader.addEventListener('error', reject);

    reader.readAsText(file);
  });
};

export const base64ToFile = ({ fileData, fileName, fileType }) => {
  const base64Data = fileData.includes(',') ? fileData.split(',')[1] : fileData;

  // Convert Base64 to Blob
  const byteCharacters = atob(base64Data);
  const byteArrays = new Uint8Array(
    // eslint-disable-next-line unicorn/prefer-code-point
    [...byteCharacters].map(char => char.charCodeAt(0))
  );

  const blob = new Blob([byteArrays], { type: fileType });

  return new File([blob], fileName, { type: fileType });
};
