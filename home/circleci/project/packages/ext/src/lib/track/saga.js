/* global APPCUES_ENV  */

import { take, takeEvery, race, getContext, fork } from 'redux-saga/effects';
import { INTERACTION_ATTEMPTED, INTERACTION_COMPLETED } from './actions';

function* trackInteraction({ payload: interaction }) {
  const start = Date.now();
  const { trackMetric } = yield getContext('api');

  yield fork(trackMetric, {
    metrics: [
      {
        type: 'count',
        name: `${APPCUES_ENV}.${interaction}.attempted`,
        value: 1,
      },
    ],
  });

  const [, cancel] = yield race([
    take(
      ({ type, payload }) =>
        type === INTERACTION_COMPLETED && payload === interaction
    ),
    take(
      ({ type, payload }) =>
        type === INTERACTION_ATTEMPTED && payload === interaction
    ),
  ]);

  if (cancel) {
    return;
  }

  yield fork(trackMetric, {
    metrics: [
      {
        type: 'count',
        name: `${APPCUES_ENV}.${interaction}.succeeded`,
        value: 1,
      },
      {
        type: 'time',
        name: `${APPCUES_ENV}.${interaction}.time`,
        value: Date.now() - start,
      },
    ],
  });
}

export default function* trackSaga() {
  yield takeEvery(INTERACTION_ATTEMPTED, trackInteraction);
}
