import { get } from 'ext/lib/local-storage';
import { enabled } from './enabled';

// Localstorage key for storing overridden feature flags
export const FLAGS_KEY = 'appcues:devtools:flags';

export const enhance = data => {
  // If DevTools are disabled, do not enhance and passthrough payload
  if (!enabled()) {
    return data;
  }

  // Check localstorage for any feature flag overrides and merge into payload
  const flags = get(FLAGS_KEY, {});

  return { ...data, gates: { ...data.gates, ...flags } };
};
