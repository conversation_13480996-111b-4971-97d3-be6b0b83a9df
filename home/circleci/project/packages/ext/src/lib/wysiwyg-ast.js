import { v4 as uuid } from 'uuid';
import { objectToCssString } from './style';

export const createDismissLinkZone = ({ skipText, persistedState }) => {
  return {
    id: uuid(),
    // not importing from DISMISS_LINK type since it leaves in the vendor of each editor
    type: 'DismissLink',
    persistedState: {
      marginTop: 10,
      ...persistedState,
      content: `⊘ ${skipText}`,
    },
  };
};

export const convertNodeTagToText = child => {
  // htmlParser transforms the `<% node %>` into a node tag
  // so as we call stringify the AST, it will
  // create a <node /> and the SDK will stop working
  // the fix is to make sure if we find a node tag
  // we convert it to a type Text and the value as <% node %>
  if (child.name === 'node') {
    // eslint-disable-next-line no-param-reassign
    child.type = 'text';
    // eslint-disable-next-line no-param-reassign
    child.content = '<% node %>';
  }
};

export const generateNewZoneAST = elementAST => {
  return {
    type: 'tag',
    name: 'div',
    voidElement: false,
    attrs: {
      class: 'zone-container',
      style: 'flex: 1;',
    },
    children: [
      {
        type: 'tag',
        name: 'div',
        voidElement: false,
        attrs: {
          class: 'zone',
        },
        children: [
          {
            type: 'tag',
            name: 'div',
            voidElement: false,
            attrs: {
              class: 'zone-content',
            },
            children: [...elementAST],
          },
        ],
      },
    ],
  };
};

export const generateNewRowAST = (zonesAST, rowStyles) => {
  return {
    type: 'tag',
    name: 'div',
    voidElement: false,
    attrs: {
      class: 'row-container',
      ...(rowStyles && { style: objectToCssString(rowStyles) }),
    },
    children: [
      {
        type: 'tag',
        name: 'div',
        voidElement: false,
        attrs: {
          class: 'row',
          style: 'display: flex;flex-wrap: wrap;width: 100%;',
        },
        children: [...zonesAST],
      },
    ],
  };
};

export const traverseTree = (vnode, callback) => {
  if (!vnode) return;

  callback(vnode);

  if (vnode.children) {
    vnode.children.forEach(child => traverseTree(child, callback));
  }
};
