// small abstraction to easy create listeners
// and make sure they all have unsubscribe
const createListener = eventType => callback => {
  window.addEventListener(eventType, callback);

  return () => {
    window.removeEventListener(eventType, callback);
  };
};

// list of custom events actions
const TRIGGER_FLOW = 'builder:trigger-flow';
export const CLOSE_WIZARD = 'builder:close-wizard';
const OPEN_DOC_SITE_FLAYOUT = 'builder:open-doc-site-flyout';
const REFRESH_DOC_SITE_DATA = 'builder:refresh-doc-site-data';
const EXPERIENCE_CREATED = 'builder:experience-created';

const dispatchCustomEvent = (eventType, detail) => {
  window.dispatchEvent(
    new CustomEvent(eventType, {
      detail,
    })
  );
};

// list of actions used by the builder application
export const closeWizard = () => dispatchCustomEvent(CLOSE_WIZARD);
export const triggerFlow = detail => dispatchCustomEvent(TRIGGER_FLOW, detail);
export const openDocSiteFlyout = detail =>
  dispatchCustomEvent(OPEN_DOC_SITE_FLAYOUT, detail);
export const refreshDocSiteData = () =>
  dispatchCustomEvent(REFRESH_DOC_SITE_DATA);
export const experienceCreated = detail =>
  dispatchCustomEvent(EXPERIENCE_CREATED, detail);

// list of listeners
export const onCloseWizard = createListener(CLOSE_WIZARD);
export const onTriggerFlow = createListener(TRIGGER_FLOW);
export const onOpenDocSiteFlyout = createListener(OPEN_DOC_SITE_FLAYOUT);
export const onRefreshDocSiteData = createListener(REFRESH_DOC_SITE_DATA);
export const onExperienceCreated = createListener(EXPERIENCE_CREATED);

// Expose the custom listeners to the window
// these listeners are used in studio, make sure when removing them
// you are not breaking Studio.
window.AppcuesBuilder = {
  onCloseWizard,
  onTriggerFlow,
  onOpenDocSiteFlyout,
  onRefreshDocSiteData,
  onExperienceCreated,
  refreshDocSiteData,
};
