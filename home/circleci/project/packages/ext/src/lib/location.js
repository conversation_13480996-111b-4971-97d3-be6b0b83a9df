import normalizeUrl from 'normalize-url';

/**
 * Compare normalized URLs so things like www and trailing slashes are ignored.
 * One of the reasons this is required is that `window.location.href` will add
 * a trailing slash to any URL without a path so if the preview URL is set to
 * e.g. `https://appcues.com`, it will not match `window.location.href`
 * without normalization
 *
 * @param {string} first - First URL to compare
 * @param {string} second - First URL to compare
 * @return {boolean} Whether the normalized URLs are the same
 */
export const compareLocations = (first, second) =>
  normalizeUrl(first, { forceHttps: true }) ===
  normalizeUrl(second, { forceHttps: true });
