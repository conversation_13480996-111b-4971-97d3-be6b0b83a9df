import { createContext, useContext } from 'react';
import PropTypes from 'prop-types';

// Flags from gates service
export const EVENT_BUILDER_BETA = 'eventBuilderBeta';
export const ONLY_CUSTOM_BUTTONS = 'onlyCustomButtons';
export const ENABLE_PINS = 'enablePins';
export const ENABLE_BUTTON_PINS = 'enableButtonPins';
export const ENABLE_BANNERS = 'enableBanners';
export const ENABLE_LAUNCHPAD = 'enableLaunchpad';
export const ENABLE_KNOWLEDGE_BASE = 'enableKnowledgeBase';
export const INLINE_LAUNCHPAD_V2 = 'inlineLaunchpadV2';

export const shape = PropTypes.shape({
  [EVENT_BUILDER_BETA]: PropTypes.bool,
  [ONLY_CUSTOM_BUTTONS]: PropTypes.bool,
  [ENABLE_PINS]: PropTypes.bool,
  [ENABLE_BUTTON_PINS]: PropTypes.bool,
  [ENABLE_BANNERS]: PropTypes.bool,
  [ENABLE_LAUNCHPAD]: PropTypes.bool,
  [ENABLE_KNOWLEDGE_BASE]: PropTypes.bool,
  [INLINE_LAUNCHPAD_V2]: PropTypes.bool,
});

const GatesContext = createContext({});

export const useGates = () => useContext(GatesContext);

export default GatesContext;
