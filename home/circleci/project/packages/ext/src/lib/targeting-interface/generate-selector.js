import { select } from 'optimal-select';
import {
  SHADOW_ROOT_TOKEN,
  IFRAME_ROOT_TOKEN,
} from 'ext/lib/evaluate-selector';

const DIGITS = /\d{2,}/; // many digits in a class/attribute is often brittle
const EMBER = /(ember(\d+))/; // generated IDs/attributes for ember components
const APPCUES = /(appcues)/; // don't capture anything to do with appcues
const ANGULAR = /(ng-.+)/; // angular class/attribute names are often dynamic
const GCP = /GCP/; // GCP class names are often dynamic
const STYLED_COMPONENT = /(sc-.+)/; // Styled-component classes change often
const CLASS_ATTR = /^(class)$/; // don't treat the whole class string as an attr
const BRACKET = /{/; // {'s often mean dynamic values being inserted/replaced
const EMPTY = /^\s+$/;

const ID_PATTERNS = [DIGITS, EMBER];

const CLASS_PATTERNS = [DIGITS, APPCUES, ANGULAR, GCP, STYLED_COMPONENT, EMPTY];

const ATTRIBUTE_NAME_PATTERNS = [ANGULAR, CLASS_ATTR];

const ATTRIBUTE_VALUE_PATTERNS = [DIGITS, APPCUES, EMBER, BRACKET];

function selectElement(element, opts = {}) {
  return select(element, {
    ignore: {
      id: (_name, id) => {
        return ID_PATTERNS.some(p => p.test(id));
      },
      class: (_name, className) => {
        return CLASS_PATTERNS.some(p => p.test(className));
      },
      attribute: (name, value, defaultPredicate) => {
        return (
          ATTRIBUTE_NAME_PATTERNS.some(p => p.test(name)) ||
          ATTRIBUTE_VALUE_PATTERNS.some(p => p.test(value)) ||
          defaultPredicate(name, value)
        );
      },
    },
    ...opts,
  });
}

export function generateSelector(stack, selector = '') {
  const [$target] = stack;

  if (!$target) return selector;

  const $root = $target.getRootNode();

  if ($root === document) {
    return `${selectElement($target)} ${selector}`;
  }

  if ($root instanceof ShadowRoot) {
    const shadowRootIndex = stack.indexOf($root);
    return generateSelector(
      stack.slice(shadowRootIndex + 1),
      `${SHADOW_ROOT_TOKEN} ${selectElement($target, {
        root: stack[shadowRootIndex - 1],
      })} ${selector}`
    );
  }

  const currentWindow = $root.parentWindow || $root.defaultView;
  const isIframe = currentWindow !== window;

  if (isIframe) {
    const iframeRootIndex = stack.indexOf($root);

    return generateSelector(
      stack.slice(iframeRootIndex + 1),
      `${selectElement(
        currentWindow.frameElement
      )} ${IFRAME_ROOT_TOKEN} ${selectElement($target, {
        root: stack[iframeRootIndex - 1],
      })} ${selector}`
    );
  }

  return selector;
}

function allowedAttributes(element) {
  return [
    'href',
    'src',
    'alt',
    'title',
    'type',
    'name',
    'placeholder',
    'id',
  ].reduce((acc, attrName) => {
    const val = element.getAttribute(attrName);
    if (val) {
      acc[attrName] = val;
    }
    return acc;
  }, {});
}

export function getElementAttributes(element) {
  return {
    tagName: element.tagName,
    ...(element.classList.length > 0 ? { class: [...element.classList] } : {}),
    ...allowedAttributes(element),
  };
}

export function getParentPadding(element) {
  const $root = element.getRootNode();
  const currentWindow = $root.parentWindow || $root.defaultView;
  const isIframe = currentWindow !== window;

  if (isIframe && currentWindow) {
    const { frameElement } = currentWindow;
    const { top, left } = frameElement.getBoundingClientRect();
    return { top, left };
  }

  return {
    top: 0,
    left: 0,
  };
}
