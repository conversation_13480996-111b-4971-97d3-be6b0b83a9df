export const SELECTOR_REQUESTED = 'SELECTOR_REQUESTED';
export const SELECTOR_REQUEST_CANCELED = 'SELECTOR_REQUEST_CANCELED';
export const SELECTOR_REQUEST_COMPLETED = 'SELECTOR_REQUEST_COMPLETED';

export const requestSelector = () => ({
  type: SELECTOR_REQUESTED,
});

export const cancelSelectorRequest = () => ({
  type: SELECTOR_REQUEST_CANCELED,
});

export const completeSelectorRequest = ({
  selector,
  elementAttributes,
  element,
  clientX,
  clientY,
}) => ({
  type: SELECTOR_REQUEST_COMPLETED,
  payload: { selector, elementAttributes, element, clientX, clientY },
});
