export const objectToCssString = (objectStyles, important = false) => {
  return Object.entries(objectStyles).reduce((prev, [key, value]) => {
    // this regex replaces all Capital letters adding a dash
    // in front of it, and then we make the whole string lowercase
    // e.g flexWrap => flex-Wrap => flex-wrap
    const cssKey = key.replace(/([A-Z])/g, '-$1').toLowerCase();

    return `${prev}${cssKey}: ${value}${important ? ' !important' : ''};`;
  }, '');
};

function parseProps(str) {
  return str
    .split(';')
    .map(rule => rule.trim())
    .filter(rule => rule)
    .reduce((style, rule) => {
      const colonIndex = rule.indexOf(':');
      if (colonIndex === -1) return style;
      const prop = rule.slice(0, colonIndex).trim();
      const val = rule.slice(colonIndex + 1).trim();
      if (prop && val) {
        const camelProp = prop.replace(/-([a-z])/g, (_, c) => c.toUpperCase());
        // eslint-disable-next-line no-param-reassign
        style[camelProp] = val;
      }
      return style;
    }, {});
}

export const cssStringToObject = cssString => {
  // Remove comments and normalize whitespace
  const cleaned = cssString.replace(/\/\*.*?\*\//gs, '').replace(/\s+/g, ' ');
  const result = {};
  const state = {};

  // Regex to match blocks: root or pseudo-class, supporting multi-line selectors
  // Matches: { selector, block }
  const blockRegex = /((?:&:[\w-]+(?:\s*,\s*&:[\w-]+)*)?)\s*{([^}]*)}/g;
  let match;

  // Only parse root-level properties that are not selectors or block headers
  // Find the first block and only parse properties before any '&:'
  const firstBlockIdx = cleaned.search(/&:[\w-]+\s*{/);
  const rootProps =
    firstBlockIdx === -1 ? cleaned : cleaned.slice(0, firstBlockIdx);
  Object.assign(result, parseProps(rootProps));

  // Now, extract blocks
  // eslint-disable-next-line no-cond-assign
  while ((match = blockRegex.exec(cleaned)) !== null) {
    const selectors = match[1]; // e.g., '&:hover' or '&:focus-visible, &:focus-within'
    const block = match[2];
    if (selectors) {
      selectors.split(',').forEach(sel => {
        const stateKey = sel.trim().replace(/^&:/, '');
        state[stateKey] = parseProps(block);
      });
    }
  }

  if (Object.keys(state).length > 0) {
    result.state = state;
  }
  return result;
};
