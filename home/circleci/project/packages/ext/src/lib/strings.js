export const capitalize = string =>
  `${string.charAt(0).toUpperCase()}${string.slice(1).toLowerCase()}`;

export const removePx = value => {
  if (typeof value !== 'string') return value;
  return Number.parseInt(value?.replace('px', ''), 10);
};

export const toRadius = (originalValue, size = 'L') => {
  if (size === 'M') return `calc(${originalValue} / 4)`;
  if (size === 'S') return `calc(${originalValue} / 8)`;

  return `calc(${originalValue} / 2)`;
};

export const addSuffix = (value, suffix) => `${value}${suffix}`;
