import { useEffect, useLayoutEffect, useRef } from 'react';

/**
 * NOTE: Adapted from https://overreacted.io/making-setinterval-declarative-with-react-hooks/
 */

const createIntervalHook = effectHook => (callback, interval) => {
  const ref = useRef();

  effectHook(() => {
    ref.current = callback;
  }, [callback]);

  // eslint-disable-next-line consistent-return
  effectHook(() => {
    if (interval != null) {
      const timer = setInterval(() => {
        ref.current();
      }, interval);
      return () => {
        clearInterval(timer);
      };
    }
  }, [interval]);
};

export const useInterval = createIntervalHook(useEffect);
export const useLayoutInterval = createIntervalHook(useLayoutEffect);
