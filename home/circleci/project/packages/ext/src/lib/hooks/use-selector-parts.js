import { useState, useEffect } from 'react';

// Matches spaces separated strings in a selector (and not including spaces in between quoted attributes [data-single='single quote'])
const PATTERN = /(?:[^\s"']+|["'][^"']*["'])+/g;

export default function useSelectorParts(selector = '') {
  const [parts, setParts] = useState({});
  useEffect(() => {
    const selectorParts = selector.match(PATTERN) || [];
    const child = selectorParts.pop();
    setParts({ child, parents: selectorParts });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);
  return parts;
}
