import { useEffect } from 'react';

// NOTE: We don't hijack event listeners which were added using capture before the hook was registered
// However, if they were added with capture after the hook was registered, the listeners are hijacked

export default function useHijackInputHotkeys() {
  useEffect(() => {
    const triggers = ['keydown', 'keyup', 'keypress'];

    const handler = event => {
      const [{ isContentEditable, tagName }] = event.composedPath();

      if (tagName === 'INPUT' || tagName === 'TEXTAREA' || isContentEditable) {
        event.stopImmediatePropagation();
      }
    };

    triggers.forEach(trigger => {
      window.addEventListener(trigger, handler, true);
    });
    return () => {
      triggers.forEach(trigger => {
        window.removeEventListener(trigger, handler, true);
      });
    };
  }, []);
}
