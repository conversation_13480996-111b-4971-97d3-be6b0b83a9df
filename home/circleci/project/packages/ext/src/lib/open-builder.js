/* globals NEW_EXTENSION, chrome */

export const openBuilderFactory = () => {
  let openedBuilder;

  return {
    getCurrentBuilder: () => openedBuilder,
    setBuilder: type => {
      openedBuilder = type;
    },

    /**
     * @param {('flow'|'event'|'pin'|'experience')} type - Builder type to open
     * @param {object} builderProperties - For any extra builder properties
     * @return {void}
     */
    openBuilder: (type, builderProperties) => {
      const typeToRemove = openedBuilder
        ? `${openedBuilder}-builder`
        : 'welcome-modal';
      openedBuilder = type;
      document.querySelector(`#${typeToRemove}`)?.remove();

      const messagePayload = {
        action: 'appcues:open-builder',
        value: { type, builderProperties },
      };

      if (NEW_EXTENSION) {
        // Since the open builder is a async command, we need to pass an callback to the sendMessage
        // in order to close the connection after the command is done.
        chrome.runtime.sendMessage(messagePayload).then();
      } else {
        // For backwards compatibility, we need to keep the old way of opening the builder.
        // For the deprecated way, we need to send the message and not await any message back.
        chrome.runtime.sendMessage(messagePayload);
      }
    },
  };
};

export const { openBuilder, setBuilder, getCurrentBuilder } =
  openBuilderFactory();
