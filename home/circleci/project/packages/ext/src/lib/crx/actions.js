export const STATE_PERSISTED = 'STATE_PERSISTED';
export const STATE_PURGED = 'STATE_PURGED';
export const DESPOOFED = 'DESPOOFED';
export const BUILDER_RELOADED = 'BUILDER_RELOADED';
export const BUILDER_UNLOADED = 'BUILDER_UNLOADED';
export const WELCOME_EXITED = 'WELCOME_EXITED';
export const REQUEST_DOMAIN_PERMISSION = 'REQUEST_DOMAIN_PERMISSION';

export const persist = () => ({
  type: STATE_PERSISTED,
});

export const despoof = () => ({
  type: DESPOOFED,
});

export const reload = () => ({
  type: BUILDER_RELOADED,
});

export const unload = () => ({
  type: BUILDER_UNLOADED,
});

export const purge = () => ({
  type: STATE_PURGED,
});

export const requestDomainPermission = () => ({
  type: REQUEST_DOMAIN_PERMISSION,
});
