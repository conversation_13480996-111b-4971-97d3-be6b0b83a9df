/* globals NEW_EXTENSION, chrome, STANDALONE */

export const BUILDER_CONNECT_NAME = 'BUILDER_CONNECT_NAME';

/**
 * Attach CRX and execute callback with connected port and auth
 *
 * @param {function} callback - Callback to use CRX port and auth
 * @param {string} name - name of the application being connected
 * @returns {Port} Return port instance, mostly for testing
 */
const attach = (callback, name = BUILDER_CONNECT_NAME) => {
  if (STANDALONE) return false;

  if (NEW_EXTENSION) {
    chrome.runtime.sendMessage({ action: name }).then(payload => {
      if (payload.action === 'appcues:auth') {
        callback(null, payload.data);
      }
    });

    // port in this case is null
    // after GA we will be able to remove everything port related so this return
    // will also become unnecessary.
    return null;
  }

  const port = chrome.runtime.connect({ name });

  port.onMessage.addListener(({ action, data }) => {
    if (action === 'appcues:auth') {
      callback(port, data);
    }
  });

  return port;
};

/**
 * Initialize CRX factory with type and additional handlers
 *
 * @param {('flow'|'event')} type - Builder type
 * @param {function} generate - Callback to generate additional handlers
 * @returns {object} CRX Client factory
 */
export const initialize = (type, generate) => {
  /**
   * Create CRX client with connected port
   *
   * @param {Port} port - Connected port
   * @returns {CRXClient}
   */
  const create = port => {
    const post = (msg, value = {}) => {
      if (NEW_EXTENSION) {
        return chrome.runtime.sendMessage({ action: `appcues:${msg}`, value });
      }

      return port.postMessage({ action: `appcues:${msg}`, data: value });
    };

    // If there are builder specific CRX callbacks, generate the handlers and
    // add them to the created client
    const handlers = typeof generate === 'function' ? generate(post) : {};

    return {
      port: () => port,

      loaded: () => post('editor-loaded', { type }),

      loadSdk: () => post('load-sdk', { type }),

      persist: state => {
        const payload = NEW_EXTENSION
          ? { ...state, meta: { builderType: type } }
          : state;

        return post('persist-state', payload);
      },

      reload: () => post('reload-builder'),

      unload: () => post('deactivate-editor'),

      closeTab: () => post('close-tab'),

      despoof: () => {
        if (NEW_EXTENSION) {
          post('despoof');
          return;
        }

        // This message is used on the old extension, so we need to keep it to maintain backwards compatibility.
        post('set-is-spoofing-account', {
          isSpoofingAccount: false,
          spoofAccountId: null,
        });
      },
      requestDomainPermission: url =>
        post('request-domain-permission', { url }),
      ...handlers,
    };
  };

  return { attach, create };
};
