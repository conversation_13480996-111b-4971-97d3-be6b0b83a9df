import { call, getContext, takeEvery, put } from 'redux-saga/effects';
import { INITIALIZE, SDK_LOADED } from 'ext/root/root-actions';
import { hideDomainPermissionModal } from 'ext/entities/permissions';
import {
  DESPOOFED,
  BUILDER_RELOADED,
  BUILDER_UNLOADED,
  WELCOME_EXITED,
  STATE_PURGED,
  REQUEST_DOMAIN_PERMISSION,
} from './actions';

function* loadSDK() {
  const crx = yield getContext('crx');
  crx.loadSdk();
}

function* editorLoaded() {
  const crx = yield getContext('crx');

  crx.loaded();
}

function* reload() {
  try {
    const crx = yield getContext('crx');
    crx.reload();
  } catch {
    // TODO: how would we even handle this...
  }
}

function* requestDomainPermission() {
  try {
    const crx = yield getContext('crx');
    const { action } = yield call(crx.requestDomainPermission);

    if (action === 'appcues:permission-flow-ended') {
      yield put(hideDomainPermissionModal());
    }
  } catch {
    // TODO: how would we even handle this...
  }
}

function* unload() {
  try {
    const crx = yield getContext('crx');
    crx.unload();
  } catch {
    // TODO: how would we even handle this...
  }
}

function* despoof() {
  try {
    const crx = yield getContext('crx');
    crx.despoof();
    window.location.reload();
  } catch {
    // TODO: how would we even handle this...
  }
}

function* purge() {
  try {
    const crx = yield getContext('crx');
    crx.persist({});
  } catch {
    // TODO: how would we even handle this...
  }
}

export default function* saga() {
  yield takeEvery(INITIALIZE, editorLoaded);
  yield takeEvery([SDK_LOADED, WELCOME_EXITED], loadSDK);
  yield takeEvery(BUILDER_RELOADED, reload);
  yield takeEvery(BUILDER_UNLOADED, unload);
  yield takeEvery(DESPOOFED, despoof);
  yield takeEvery(STATE_PURGED, purge);
  yield takeEvery(REQUEST_DOMAIN_PERMISSION, requestDomainPermission);
}
