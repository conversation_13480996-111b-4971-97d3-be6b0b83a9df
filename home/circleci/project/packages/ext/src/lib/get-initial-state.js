import apiClient from 'ext/lib/api/client';
import { getAccountDetailsFromToken, isSpoofingToken } from 'ext/lib/api/auth';

export const getStandAloneState = async ({ token, builderProperties = {} }) => {
  const { accountId, uid } = getAccountDetailsFromToken(token);
  const isSpoofing = isSpoofingToken(token);

  const api = apiClient(token);
  const endpoints = await api.getEndpoints();

  const [
    { value: gates },
    {
      value: { user },
    },
  ] = await Promise.allSettled([
    endpoints.getAccountGates(),
    endpoints.getUser(uid),
  ]);

  return {
    accountId,
    builderProperties,
    email: user.email ?? null,
    fullName: user.fullname ?? null,
    gates,
    isSpoofing,
    jwt: token,
    persisted: {},
    userId: user.id,
  };
};
