/* eslint-disable no-use-before-define */
/* eslint-disable no-param-reassign */
import { evaluateSelectorAll } from 'ext/lib/evaluate-selector';
import { getParentPadding } from 'ext/lib/targeting-interface';

/**
 * Slightly modified version of javascript-sdk/utils/dom
 */

export const MIN_SCROLL_DURATION = 300;
export const MAX_SCROLL_DURATION = 700;

export const getIframeParent = el => {
  const $root = el.getRootNode(el);
  const currentWindow = $root.parentWindow || $root.defaultView;
  return currentWindow && currentWindow !== window
    ? currentWindow.frameElement
    : null;
};

const checkInView = (container, element, partial) => {
  const cTop = container.scrollTop;
  const cBottom = cTop + container.clientHeight;

  const eTop = element.offsetTop;
  const eBottom = eTop + element.clientHeight;

  const isTotal = eTop >= cTop && eBottom <= cBottom;
  const isPartial =
    partial &&
    ((eTop < cTop && eBottom > cTop) || (eBottom > cBottom && eTop < cBottom));

  return isTotal || isPartial;
};

/**
 * Calculates a set of position-related details about the element identified by
 * the passed selector. It calculates the following proeprties:
 *   - fixed - whether the element or a parent is fixed.
 *   - zIndex - 1 higher than the zIndex of the targeted element's highest level
 *              stacking context.
 *   - element - the targeted element.
 *   - boundingRect - with values for the `top`, `left`, `bottom`, `right` of
 *                    the element (from getBoundingClientRect()), adjusted for
 *                    page offsets and scrolling. This value is the equivalent
 *                    of the position as if the element had a position of
 *                    of 'absolute', unless it is fixed or has a fixed parent.
 *   - relativeBoundingRect - with values for the `top`, `left`, `bottom`,
 *                            `right` of the element relative to the viewport.
 *   - viewport - with values for the `height` and `width` of the viewport.
 *
 * If no element is found or multiple are found an error object is generated.
 *
 * @param  {Element} el The element to calculate details for, or null if unknown.
 *
 * @param  {string|object[]} selector Selector of element to calculate position
 *  details for, or an array of objects that have a "selector" key that is a
 *  string
 *
 * @return {object} Position details object of the form:
 *   {
 *       fixed: true,
 *       zIndex: 1,
 *       element: $domNode,
 *       boundingRect: {
 *           top: 0,
 *           left: 0,
 *           bottom: 100,
 *           right: 30
 *       }
 *       relativeBoundingRect: {
 *           top: 10,
 *           left: 0,
 *           bottom: 110,
 *           right: 30
 *       },
 *       viewport: {
 *           height: 1000,
 *           width: 1200
 *       }
 *   }
 * Or in the case of an error:
 *   {
 *      error: true,
 *      errorMessage: 'This is the error message.'
 *   }
 */
export function calculatePositionDetails(
  el,
  selector,
  context = window.document
) {
  // We should always re-select the element, since the element may still exist
  // but no longer match the given selector.
  const result = findUniqueElement(selector, context);
  if (result.error) {
    return result;
  }
  el = result;

  const position = { element: el };
  const iframeParent = getIframeParent(el);
  const doc = el.ownerDocument || document;
  const win = doc.defaultView || window;

  // Calculate positions of the element.
  const { left, top, right, bottom } = el.getBoundingClientRect();
  const padding = getParentPadding(el);
  const relativeBoundingRect = { left, top, right, bottom };
  let boundingRect = { left, top, right, bottom };

  // Get a number of values based on traversing parent nodes:
  //   - position: 'fixed' (this affects children)
  //   - z-index
  //   - opacity (this cascades to children)
  //   - hiddenOverflow (if a parent has a zero size and has overflow: hidden)
  const computedStyle = win.getComputedStyle(el);
  const { fixed, zIndex, opacity, hiddenOverflow, hasScrollableParent } =
    calculateParentValues(el, computedStyle, doc, win);

  // If fixed, we just want the boundingRect relative to the viewport, so
  // don't offset it. The same applies if the element is in an iframe.
  if (!fixed && !iframeParent) {
    const bodyEl = doc.body;
    // Account for any window/document offsets.
    const offsets = computeOffsets(doc, win);
    boundingRect = offsetBoundingRect(boundingRect, offsets);
    // Calculate a body offset if the body is 'positioned'.
    if (isPositionedValue(win.getComputedStyle(bodyEl).position)) {
      const docEl = doc.documentElement;
      const { overflowY: docElOverflowY } = win.getComputedStyle(docEl);
      const { overflowY: bodyElOverflowY } = win.getComputedStyle(bodyEl);

      if (
        isScrollable(bodyEl, bodyElOverflowY) &&
        docElOverflowY !== 'visible'
      ) {
        // If the document element (<html>) is scrollable, the <body> may have
        // a scrollTop that we need to account for.
        offsets.yOffset -= bodyEl.scrollTop;
      }

      const bodyRect = offsetBoundingRect(
        bodyEl.getBoundingClientRect(),
        offsets
      );
      boundingRect = offsetBoundingRect(boundingRect, {
        xOffset: -1 * bodyRect.left,
        yOffset: -1 * bodyRect.top,
      });
    }
  }

  // Check if the element is visible. If not, return an error.
  const isHiddenAndCannotBeScrolledTo = iframeParent
    ? !checkInView(iframeParent.contentDocument.body, el, false)
    : (boundingRect.right < 0 || boundingRect.bottom < 0) &&
      !hasScrollableParent;

  if (
    !hasSize(el) ||
    computedStyle.visibility === 'hidden' ||
    opacity === 0 ||
    hiddenOverflow ||
    isHiddenAndCannotBeScrolledTo
  ) {
    return errorObject('Targeted element is present but not visible.');
  }

  return Object.assign(position, {
    boundingRect,
    fixed,
    zIndex,
    relativeBoundingRect,
    viewport: {
      width: doc.documentElement.clientWidth,
      height: doc.documentElement.clientHeight,
    },
    iframeParent,
    padding,
  });
}

/**
 * Calculates the region of the rectangle (specified by the `height` and `width`
 * params) that these coordinates fall within. The passed coordinates should be
 * relative to the rectangle. The return value has two properties `xRegion` and
 * `yRegion` that range from 0 to 3, depending on which quadrants of the
 * rectangle the coordinates fall within:
 *
 *  +---+---+---+---+
 *  |0,0|1,0|2,0|3,0|
 *  +---+---+---+---+
 *  |0,1|1,1|2,1|3,1|
 *  +---+---+---+---+
 *  |0,2|1,2|2,2|3,2|
 *  +---+---+---+---+
 *  |0,3|1,3|2,3|3,3|
 *  +---+---+---+---+
 *
 * @param  {number} x X-coordinate relative to the rectangle.
 * @param  {number} y Y-coordinate relative to the rectangle.
 * @param  {number} width Width of the rectagle.
 * @param  {number} height Height of the rectagle.
 *
 * @return {Object} Has two properties indicating x-region and y-region of the
 *                  passed coordinates.
 */
export function calculateRegion(x, y, width, height) {
  return {
    // Divide the page into a 4x4 grid.
    // We make sure to bound the numerator within the bounds of the
    // rectangle, such that our region values are always 0, 1, 2 or 3.
    xRegion: Math.floor(Math.min(Math.max(x, 0), width - 1) / (width / 4)),
    yRegion: Math.floor(Math.min(Math.max(y, 0), height - 1) / (height / 4)),
  };
}

export function getScrollDuration(target, current) {
  // Scroll duration (ms) is calculated as scroll distance divided by 2
  // with a minimum of 300ms and a maximum of 700ms.
  return Math.min(
    MAX_SCROLL_DURATION,
    Math.max(MIN_SCROLL_DURATION, (target - current) / 2)
  );
}

export function getScrollablePageElement(doc) {
  // The default scrollable element should be the documentElement as this
  // follows the spec. Some browsers (Chrome specifically) don't follow the
  // spec and scroll the document.body element.
  const docEl = doc.documentElement;

  // If the documentElement's scrollTop is greater than zero, then it's the
  // scrollable el.
  if (docEl.scrollTop > 0) {
    return docEl;
  }

  // TODO: can this be done without changing the scrollTop? This has unwanted side effects.
  // If it's zero, it may just be at the top. Try setting its scrollTop
  // to 1. If it sticks, then the documentElement's the scrollable el.
  docEl.scrollTop = 1;
  if (docEl.scrollTop > 0) {
    docEl.scrollTop = 0;
    return docEl;
  }

  docEl.scrollTop = 0;
  // Otherwise fallback to scrolling the document.body.
  return doc.body;
}

function calculateScrollTop(
  containerScrollTop,
  containerScrollHeight,
  containerBottom,
  containerHeight,
  targetBottom,
  targetHeight,
  targetOffsetPercentageFromBottom
) {
  // The calculations here are a bit tricky -- the diagram included in this
  // PR should help make things clearer:
  // https://github.com/appcues/javascript-sdk/pull/444

  // The "distanceFromTop" is an absolute distance measuring from the actual
  // top of the container down to our target position.
  //
  // The value of `containerHeight + containerScrollTop` gives us the distance
  // from the top of the container down to the lowest currently visible part
  // of its children (as you scroll down in that container, this distance
  // grows and you see the further down parts of the container's children).
  //
  // The value of `targetBottom - containerBottom -
  // targetOffsetPercentageFromBottom * targetHeight` gives us the distance
  // from bottom of the container to the target.
  //
  // These two values combined should tell us our "distanceFromTop".
  const distanceFromTop =
    containerHeight +
    containerScrollTop +
    targetBottom -
    containerBottom -
    targetOffsetPercentageFromBottom * targetHeight;

  // We don't need to scroll the container if the target element is within the
  // bounds of the scrollTop and scrollTop + containerHeight, because this
  // means that the element is in the currently visible part of the container.
  // It's possible that this scrollable container is within another that needs
  // to be scrolled in order to make this one visible though. We use this
  // `visibleInContainer` value later to determine if any scrolling is
  // actually necessary (see performScroll() in the annotations-sagas module).
  let visibleInContainer = false;
  if (
    distanceFromTop > containerScrollTop &&
    distanceFromTop < containerScrollTop + containerHeight
  ) {
    visibleInContainer = true;
  }

  // Center the target (accounting for offset within the target element)
  // within the container.
  const idealScrollTop = distanceFromTop - containerHeight / 2;

  // Normalize scrollTop value, so that it is never less than zero and never
  // greater than its max.
  const maxScrollTop = containerScrollHeight - containerHeight;
  return {
    idealScrollTop: Math.max(0, Math.min(idealScrollTop, maxScrollTop)),
    visibleInContainer,
  };
}

function isScrollable(el, overflowY) {
  return overflowY !== 'visible' && el.scrollHeight > el.clientHeight;
}

export function findScrollableParentElements(el, context) {
  const els = [];
  let parentEl = el.parentElement;
  const doc = context || document;
  const { documentElement, body } = doc;

  // If there's a 'fixed' element between the target element and the container
  // we're trying to scroll, then there's no point in scrolling the container,
  // since the target will not move. Don't include any parent elements beyond
  // that point then.
  let hasFixedAncestor = window.getComputedStyle(el).position === 'fixed';
  while (!hasFixedAncestor && parentEl && parentEl !== body) {
    const { overflowY, position } = window.getComputedStyle(parentEl);
    if (isScrollable(parentEl, overflowY)) {
      els.push(parentEl);
    }
    hasFixedAncestor = position === 'fixed';
    parentEl = parentEl.parentElement;
  }

  // As long as there's no fixed ancestor, we still can potentially scroll the
  // main "scrollable element" of the page. Check if it actually overflows,
  // and if so include it in the list of scrollable parent elements. To handle
  // a funny issue with Safari, also check if the `document.documentElement`
  // is overflowing and scroll in that case. It's possible for the
  // rootScrollableEl to be the `document.body` with a clientHeight ===
  // scrollHeight, but the `document.documentElement` to be overflowing. In
  // this case we still want to add the rootScrollableEl to the list of
  // scrollable elements, so that we'll set the scrollTop of the body
  // correctly.
  const rootScrollableEl = getScrollablePageElement(doc);
  if (
    !hasFixedAncestor &&
    (documentElement.scrollHeight > documentElement.clientHeight ||
      rootScrollableEl.scrollHeight > rootScrollableEl.clientHeight)
  ) {
    els.push(rootScrollableEl);
  }
  return els;
}

export function calculateParentScrollTops(
  target,
  offsetFromBottomPercentage,
  parentElements,
  context
) {
  const doc = context || document;
  const rect = target.getBoundingClientRect();

  const currentTargetHeight = rect.height;
  let currentTargetBottom = rect.bottom;

  return parentElements.map(container => {
    let { bottom: containerBottom } = container.getBoundingClientRect();

    const { scrollTop, scrollHeight } = container;
    let { clientHeight: containerHeight } = container;

    // As part of our workaround for Safari scrolling (mentioned above in
    // the comments for `findScrollableParentElements`), we also need to use
    // the smaller clientHeight of either the documentElement or body. This
    // will ensure we'll calculate the correct scrollTop value; otherwise if
    // we're using the larger clientHeight (of the body, for example), it
    // will match the scrollHeight. In this case, it will appear that we do
    // not need to change the scrollTop because the element is in the
    // "viewable" area indiciated by the larget clientHeight.
    // If we're dealing with the root scrollable container of the page, the
    // bottom of the container can be computed by taking how much we've
    // scrolled down in that container and adding it to the clientHeight of
    // the root scrollable element.
    if (container === doc.documentElement || container === doc.body) {
      containerHeight = Math.min(
        // We use Infinity here to catch the case where the
        // documentElement or body doesn't have a clientHeight (due to
        // lacking a sized inline element).
        doc.documentElement.clientHeight || Number.POSITIVE_INFINITY,
        doc.body.clientHeight || Number.POSITIVE_INFINITY
      );
      // If we somehow end up with Infinity for both values above, fall
      // back to the window.innerHeight.
      containerHeight =
        containerHeight === Number.POSITIVE_INFINITY
          ? window.innerHeight
          : containerHeight;
      containerBottom = containerHeight;
    }

    const { idealScrollTop, visibleInContainer } = calculateScrollTop(
      scrollTop,
      scrollHeight,
      containerBottom,
      containerHeight,
      currentTargetBottom,
      currentTargetHeight,
      offsetFromBottomPercentage
    );
    currentTargetBottom += scrollTop - idealScrollTop;
    return { el: container, scrollTop: idealScrollTop, visibleInContainer };
  });
}
/**
 * Checks if two position details objects, as returned from
 * `calculatePositionDetails` are the same.
 *
 * @param  {object} a The first set of position details.
 * @param  {object} b The second set of position details.
 *
 * @return {boolean} Whether the position details are the same.
 */
export function positionDetailsEqual(a, b) {
  if (a.error || b.error) {
    return a.error && b.error && a.errorMessage === b.errorMessage;
  }

  return (
    a.fixed === b.fixed &&
    a.zIndex === b.zIndex &&
    a.element === b.element &&
    objectsEqual(a.boundingRect || {}, b.boundingRect || {}) &&
    objectsEqual(a.relativeBoundingRect || {}, b.relativeBoundingRect || {}) &&
    objectsEqual(a.viewport || {}, b.viewport || {})
  );
}

/*
 * Iterate and select the first available unique element from the list of
 * selectors.
 */
function findUniqueElementFromSelectors(selectors, context) {
  if (selectors.length === 0) {
    return errorObject('Missing selector.');
  }

  let el;
  Object.values(selectors).forEach(selector => {
    if (!el) {
      const uniqEl = findUniqueElementFromSelector(selector, context);
      if (uniqEl && !uniqEl.error) {
        el = uniqEl;
      }
    }
  });
  if (!el) {
    return errorObject('Could not find an element for list of selectors.');
  }
  return el;
}

/*
 * Given a selector that is either an
 *   - object with a key 'selector'
 *   - string
 * Finds a unique element using either that selector key or the string for the
 * selector.
 */
function findUniqueElementFromSelector(selector, context) {
  const cssSelector =
    (typeof selector === 'string' && selector) ||
    (typeof selector === 'object' && selector.selector);
  if (!cssSelector) {
    return errorObject('Missing selector.');
  }

  let matchingElements = [];
  try {
    matchingElements = [...evaluateSelectorAll(cssSelector, context)];
  } catch {} // eslint-disable-line no-empty

  const textFilterValue = selector.text_filter || selector.textFilter;
  const containsTextFilter = !!textFilterValue;
  const containsOrderFilter =
    typeof selector === 'object' &&
    Object.prototype.hasOwnProperty.call(selector, 'order_filter');
  const orderFilterValue = selector.order_filter;

  if (containsTextFilter && textFilterValue.length >= 0) {
    matchingElements = Array.prototype.slice
      .call(matchingElements)
      .filter(element => {
        return (
          element.textContent
            .replace(/\r\n|\r|\n/g, ' ')
            .toLowerCase()
            .trim() === textFilterValue.toString().toLowerCase().trim()
        );
      });
  }
  if (containsOrderFilter && orderFilterValue >= 0) {
    matchingElements = matchingElements[orderFilterValue]
      ? [matchingElements[orderFilterValue]]
      : [];
  }

  if (matchingElements.length === 0) {
    let textErrorMessage = '';
    let orderErrorMessage = '';
    if (containsTextFilter) {
      textErrorMessage = ` with text filter "${textFilterValue}"`;
    }
    if (containsOrderFilter) {
      orderErrorMessage = ` with order value ${orderFilterValue}`;
    }
    return errorObject(
      `Could not find element for selector "${cssSelector}"${
        containsTextFilter ? textErrorMessage : ''
      }${containsTextFilter && containsOrderFilter ? ' and' : ''}${
        containsOrderFilter ? orderErrorMessage : ''
      }.`
    );
  }

  if (matchingElements.length > 1) {
    return errorObject(
      `Found ${matchingElements.length} elements for selector ${selector.selector}.`
    );
  }

  return matchingElements[0];
}

export function findUniqueElement(selector, context = window.document) {
  // Check if the selector is an array
  if (selector && typeof selector === 'object' && selector.length > 0) {
    if (selector.length > 1) {
      return findUniqueElementFromSelectors(selector, context);
    }
    // Since we wrap all our selectors as arrays now, unwrap a single
    // selector so that we can get the correct error message out.
    [selector] = selector;
  }
  return findUniqueElementFromSelector(selector, context);
}

// Calculates zIndex and fixed values for this el and its parents.
function calculateParentValues(el, computedStyle, doc, win, values = {}) {
  let z = computedStyle.zIndex;
  const pos = computedStyle.position;
  const parent = el.parentElement;
  const parentComputedStyle = parent ? win.getComputedStyle(parent) : {};

  // If none of the previous nodes were fixed, check if this one is.
  if (!values.fixed) {
    values.fixed = pos === 'fixed';
  }

  // If none of the previous nodes were absolute, check if this one is.
  if (!values.absolute) {
    values.absolute = pos === 'absolute';
  }

  // Opacities apply on top of each other down the tree, so multiply them,
  // starting with an initial value of 1.
  values.opacity =
    Number.parseFloat(values.opacity != null ? values.opacity : 1) *
    Number.parseFloat(computedStyle.opacity);

  // If none of the previous nodes had height/width of zero, a 'hidden'
  // value for the overflow property, and no previous nodes break context
  // with a positioning of 'absolute' or 'fixed', then check for this element.
  if (!values.hiddenOverflow) {
    values.hiddenOverflow =
      !hasSize(el) &&
      computedStyle.overflow === 'hidden' &&
      !values.fixed &&
      !values.absolute;
  }

  // If none of the previous nodes indicated that they were scrollable, check
  // this one.
  if (!values.hasScrollableParent) {
    values.hasScrollableParent = isScrollable(el, computedStyle.overflowY);
  }

  // In Firefox, getComputedStyle always returns a z-index.
  // In Chrome, getComputedStyle only returns set z-index if the element is not
  // set to `static` postion.
  // This normalizes that behavior.
  if (
    z !== '' &&
    z !== 'auto' &&
    !isPositionedValue(pos) &&
    !isFlexContainer(parentComputedStyle)
  ) {
    z = 'auto';
  }

  // Initialize with first el's zIndex value, or if it has a value... if the
  // el has a z-index set to something other than '' or 'auto' and is
  // 'positioned' or is a "flex item" (i.e. its parent has 'display: flex',
  // then this will create a new, higher-level stacking context which we need
  // to match.
  if (
    values.zIndex == null ||
    (z !== '' &&
      z !== 'auto' &&
      (isPositionedValue(pos) || isFlexContainer(parentComputedStyle)))
  ) {
    values.zIndex = z;
  }
  // We want to stop these calculations before we get to the body or documentElement.
  if (parent && parent !== doc.body && parent !== doc.documentElement) {
    return calculateParentValues(parent, parentComputedStyle, doc, win, values);
  }

  // We want the zIndex to be one higher, as long as its a number.
  if (/^\d+$/.test(values.zIndex)) {
    let zIndex = Number.parseInt(values.zIndex, 10);
    zIndex += 1;
    values.zIndex = zIndex;
  }
  return values;
}

export function isElementAttached(el) {
  const doc = el.ownerDocument;
  return doc && doc.documentElement.contains(el);
}

function errorObject(errorMessage) {
  return { error: true, errorMessage };
}

export function hasSize(el) {
  return (
    el &&
    (el.offsetWidth || el.offsetHeight) &&
    el.offsetWidth > 0 &&
    el.offsetHeight > 0
  );
}

function isPositionedValue(pos) {
  return (
    pos === 'fixed' ||
    pos === 'relative' ||
    pos === 'absolute' ||
    pos === 'sticky'
  );
}

function isFlexContainer(style) {
  return style.display === 'flex' || style.display === 'inline-flex';
}

function computeOffsets(doc, win) {
  const docEl = doc.documentElement;
  return {
    xOffset: (win.pageXOffset || 0) - docEl.clientLeft,
    yOffset: (win.pageYOffset || 0) - docEl.clientTop,
  };
}

function offsetBoundingRect(
  { left, top, right, bottom },
  { xOffset, yOffset }
) {
  return {
    left: left + xOffset,
    top: top + yOffset,
    right: right + xOffset,
    bottom: bottom + yOffset,
  };
}

function objectsEqual(a, b) {
  if (a && b) {
    return Object.keys(a).every(key => {
      return a[key] === b[key];
    });
  }
  return false;
}

export function addClass(el, className) {
  const classes = el.className.split(' ');
  if (!classes.includes(className)) {
    el.className = [...classes, className]
      .filter(i => {
        return i;
      })
      .join(' ');
  }
}

export function removeClass(el, className) {
  const classes = el.className.split(' ');
  const idx = classes.indexOf(className);
  if (idx > -1) {
    classes.splice(idx, 1);
    el.className = classes.join(' ');
  }
}

export function isBodyReady(doc) {
  return (
    (doc.readyState === 'interactive' || doc.readyState === 'complete') &&
    doc.body
  );
}

export function onCSSReady(doc, url, callback) {
  if (
    Array.prototype.some.call([...doc.styleSheets], sheet => sheet.href === url)
  ) {
    // We don't want to call the callback synchronously because the actions
    // that are dispatched might cause nested renders.
    setTimeout(() => callback(), 5);
  } else {
    setTimeout(() => onCSSReady(doc, url, callback), 5);
  }
}

export function afterNextPaint(win, callback) {
  win.requestAnimationFrame(() => {
    win.requestAnimationFrame(() => {
      callback();
    });
  });
}

export function eventListenerPromise(el, event) {
  let listener;
  const promise = new Promise(resolve => {
    listener = () => {
      el.removeEventListener(event, listener);
      resolve(true);
    };
    el.addEventListener(event, listener);
  });
  return { listener, promise };
}
