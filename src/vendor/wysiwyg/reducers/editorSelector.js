import { fromJS } from 'immutable';
import Actions from '../helpers/actionConstants';

const initialState = fromJS({
  isOpen: false,
  menuPosition: {},
  addButtonPosition: {},
});

export default function editorSelector(state = initialState, action) {
  let newState = state;

  if (!action || !action.type) {
    return newState;
  }

  switch (action.type) {
    case Actions.ROWS_ADD_ONE:
    case Actions.ROWS_ADD_MANY:
    case Actions.UNSHIFT_ROW:
      newState = newState.set('isOpen', false);
      break;
    case Actions.EDITOR_SELECTOR_SHOW:
      newState = newState
        .set('isOpen', true)
        .set('addButtonPosition', action.addButtonPosition);
      break;
    case Actions.EDITOR_SELECTOR_HIDE:
      newState = newState.set('isOpen', false);
      break;
    default:
      break;
  }

  return newState;
}
