import React from 'react';
import ReactDOMServer from 'react-dom/server';
import PropTypes from 'prop-types';
import { connect } from 'react-redux';

import HTMLParser from 'html-parse-stringify2';
import { v4 as uuid } from 'uuid';
import { Map, List, fromJS, is } from 'immutable';
import isEmpty from 'lodash.isempty';

import { objectToCssString } from 'ext/lib/style';

import { convertBoundingBox } from '../helpers/domHelpers';
import * as rowActions from '../actions/rowActions';
import * as zoneActions from '../actions/zoneActions';
import * as editorActions from '../actions/editorActions';
import AddButtonArea from './AddButtonArea';
import FullAddElement from './FullAddElement';
import RowContainer from './RowContainer';
import { rowStyles, zoneContainerStyles } from '../helpers/styles/editor';

/**
 * Filter rows with invalid zones - carry over from `crx-ui`
 *
 * @param {Row[]} rows - Step child rows
 * @return {Row[]} Filtered step child rows with valid zones
 */
const sanitizeRows = (rows = []) =>
  rows.filter(({ zones }) =>
    zones.some(({ persistedState }) => !isEmpty(persistedState))
  );

/**
 * A React component that acts as the main
 * wrapper around the other components of the WYSIWYG editor
 * @class
 */
export class Canvas extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      rowsLoaded: false,
    };
  }

  componentDidMount() {
    const {
      dispatch,
      cloudinary,
      rows,
      startEditable,
      userProperties,
      allowedEditorTypes,
      sanitizeHtml,
      disableAddButton,
      basePadding,
      shouldDisableXSS,
    } = this.props;

    window.editorShadowRoot = this.wrapper.getRootNode();

    this.setBoundingBox();
    if (rows && !rows.isEmpty()) {
      const activeZoneId = startEditable
        ? rows.get(0).get('zones').get(0)
        : null;
      dispatch(rowActions.replaceRows(rows, activeZoneId));
    }
    if (cloudinary) {
      dispatch(editorActions.setCloudinarySettings(cloudinary));
    }
    if (basePadding) {
      dispatch(editorActions.setBasePadding(basePadding));
    }
    if (userProperties && !userProperties.isEmpty()) {
      this.props.dispatch(editorActions.setUserProperties(userProperties));
    }
    if (allowedEditorTypes && !allowedEditorTypes.isEmpty()) {
      dispatch(editorActions.setAllowedEditorTypes(allowedEditorTypes));
    }
    if (disableAddButton !== undefined) {
      dispatch(editorActions.setDisableAddButton(disableAddButton));
    }
    if (sanitizeHtml && !sanitizeHtml.isEmpty()) {
      dispatch(editorActions.setSanitizeHtmlConfig(sanitizeHtml));
    }

    dispatch(editorActions.setShouldDisableXSS(shouldDisableXSS === true));

    this.setState({
      rowsLoaded: true,
    });
  }

  UNSAFE_componentWillReceiveProps(nextProps) {
    const { dispatch, isInEditMode, disabled } = this.props;
    const { rowsLoaded } = this.state;

    if (
      rowsLoaded &&
      (!is(nextProps.internalRows, this.props.internalRows) ||
        !is(nextProps.internalZones, this.props.internalZones))
    ) {
      !disabled && this.save(nextProps.internalRows, nextProps.internalZones);
    }
    if (!is(nextProps.cloudinary, this.props.cloudinary)) {
      dispatch(editorActions.setCloudinarySettings(nextProps.cloudinary));
    }
    if (!is(nextProps.userProperties, this.props.userProperties)) {
      dispatch(editorActions.setUserProperties(nextProps.userProperties));
    }
    if (!is(nextProps.allowedEditorTypes, this.props.allowedEditorTypes)) {
      dispatch(
        editorActions.setAllowedEditorTypes(nextProps.allowedEditorTypes)
      );
    }
    if (nextProps.disableAddButton !== this.props.disableAddButton) {
      dispatch(editorActions.setDisableAddButton(nextProps.disableAddButton));
    }
    if (
      !nextProps.sanitizeHtml.isEmpty() &&
      !is(nextProps.sanitizeHtml, this.props.sanitizeHtml)
    ) {
      dispatch(editorActions.setSanitizeHtmlConfig(nextProps.sanitizeHtml));
    }
    if (
      nextProps.isInEditMode !== isInEditMode &&
      !!nextProps.onEditStart &&
      !!nextProps.onEditEnd
    ) {
      nextProps.isInEditMode ? nextProps.onEditStart() : nextProps.onEditEnd();
    }
  }

  renderDraftJSStyles() {
    return (
      <style>
        {`
          .public-DraftEditorPlaceholder-root {
            position: absolute;
            z-index: 0;
            opacity: 0.8;
            transition: opacity 0.15s ease-out;
          }

          .public-DraftEditorPlaceholder-hasFocus {
            opacity: 0.5;
          }

          `}
      </style>
    );
  }

  renderKeyframeStyles() {
    return (
      <style>
        {`@-webkit-keyframes editor-slide-in-bottom {
              0% {-webkit-transform:translate3d(0, -15px, 0); opacity: 0}
              100% {-webkit-transform:translate3d(0, 0px, 0); opacity: 1)}
          }

          @-webkit-keyframes editor-slide-out-bottom {
              0% {-webkit-transform:translate3d(0, 0px, 0); opacity: 1)}
              100% {-webkit-transform:translate3d(0, -15px, 0); opacity: 0}
          }

          @-webkit-keyframes editor-slide-in-top {
              0% {-webkit-transform:translate3d(0, 15px, 0); opacity: 0}
              100% {-webkit-transform:translate3d(0, 0px, 0); opacity: 1)}
          }

          @-webkit-keyframes editor-slide-out-top {
              0% {-webkit-transform:translate3d(0, 0px, 0); opacity: 1)}
              100% {-webkit-transform:translate3d(0, 15px, 0); opacity: 0}
          }
          `}
      </style>
    );
  }

  setBoundingBox() {
    const { dispatch, canvasPosition } = this.props;
    if (this.wrapper) {
      const position = convertBoundingBox(this.wrapper.getBoundingClientRect());
      if (!position.equals(canvasPosition)) {
        dispatch(editorActions.setCanvasPosition(position));
      }
    }
  }

  handleAddImage(imageDetails) {
    // Trim what we save from Cloudinary down to just these fields
    const { url, width } = imageDetails;
    const { canvasPosition } = this.props;

    const urlWithoutProtocol = url.replace(/^https?:\/\//i, '//');

    // Make sure the uploaded image does not have a larger size than the canvas
    const widthOverride =
      width > canvasPosition.get('width') ? canvasPosition.get('width') : null;

    const rowsToAdd = fromJS([
      {
        id: uuid(),
        zones: [
          {
            id: uuid(),
            type: 'Image',
            persistedState: {
              url: urlWithoutProtocol,
              width,
              widthOverride,
            },
          },
        ],
      },
    ]);

    this.addRow('Image', rowsToAdd);
  }

  insertZone(row, zone, columnIndex) {
    this.props.dispatch(rowActions.insertZone(row, zone, columnIndex));
  }

  addNewZone(type, row, defaultAction, existingProps = {}) {
    const { dispatch } = this.props;
    const zoneToAdd = fromJS({
      id: uuid(),
      type,
      persistedState: {
        ...existingProps,
      },
    });

    dispatch(rowActions.insertZone(row, zoneToAdd));

    // start editing immediately, if zone is new
    if (Object.keys(existingProps).length === 0) {
      dispatch(editorActions.startEditing(zoneToAdd));
    }
    if (defaultAction) {
      dispatch(editorActions.toggleEditorAction(defaultAction, true));
    }
  }

  removeZone(row, zone, confirmDelete) {
    const { dispatch } = this.props;

    if (row.get('zones').size === 1) {
      this.removeRow(row.get('id'));
    } else {
      if (confirmDelete && !confirm('Are you sure you want to delete this?')) {
        return;
      }
      dispatch(editorActions.cancelEditing(zone));
      dispatch(rowActions.removeZone(row, zone));
      dispatch(zoneActions.removeZone(zone.get('id')));
    }
  }

  moveZone(zoneId, rowId, columnIndex) {
    const { dispatch } = this.props;

    dispatch(zoneActions.moveZone({ zoneId, rowId, columnIndex }));
  }

  moveZoneToNewRow(zoneId, rowIndex) {
    const { dispatch } = this.props;

    dispatch(zoneActions.moveZoneToNewRow({ zoneId, rowIndex }));
  }

  addRow(type, maybeRowsToAdd, defaultAction, shouldUnshiftRow = false) {
    const { dispatch } = this.props;

    const rowsToAdd =
      maybeRowsToAdd ||
      fromJS([
        {
          id: uuid(),
          zones: [
            {
              id: uuid(),
              type,
              persistedState: {},
            },
          ],
        },
      ]);

    dispatch(
      shouldUnshiftRow
        ? rowActions.unshiftRow(rowsToAdd)
        : rowActions.addRows(rowsToAdd)
    );

    // If only one element is added, let's start editing immediately
    if (rowsToAdd.size === 1 && rowsToAdd.get(0).get('zones').size === 1) {
      const activeZone = rowsToAdd.get(0).get('zones').get(0);
      dispatch(editorActions.startEditing(activeZone));
      if (defaultAction) {
        dispatch(editorActions.toggleEditorAction(defaultAction, true));
      }
    }
  }

  removeRow(id) {
    this.props.dispatch(rowActions.removeRow(id));
  }

  moveRows(sourceIndex, targetIndex) {
    if (sourceIndex === targetIndex) {
      return;
    }
    this.props.dispatch(editorActions.moveRows(sourceIndex, targetIndex));
  }

  buildHtml(rows, zonesWithHtml) {
    let html = '';
    rows.forEach(row => {
      const zones = row.get('zones');
      let zoneBlocks = [];
      if (zones && zones.size) {
        zoneBlocks = zones.map(zone => {
          const zoneId = zone.get('id');
          const zoneHTML = zonesWithHtml.has(zoneId)
            ? zonesWithHtml.get(zoneId).get('html')
            : '';
          return `
            <div class="zone-container" style="${objectToCssString(
              zoneContainerStyles
            )}">
              <div class="zone">
                <div class="zone-content">
                  ${zoneHTML}
                </div>
              </div>
            </div>
          `;
        });
      }
      html += `
        <div class="row-container">
          <div class="row" style="${objectToCssString(rowStyles)}">
            ${zoneBlocks.join('\n')}
          </div>
        </div>
      `;
    });
    return html;
  }

  save(internalRows, internalZones) {
    const { onSave, style } = this.props;

    if (onSave) {
      // Build the final HTML
      const rowsHtml = this.buildHtml(internalRows, internalZones);

      let html = '';

      if (rowsHtml) {
        // Rendering here with ReactDOMServer to convert the optional style object to CSS
        html = ReactDOMServer.renderToStaticMarkup(
          <div className="canvas" style={style}>
            |ROWS|
          </div>
        ).replace('|ROWS|', rowsHtml);
      }

      const ast = HTMLParser.parse(html);

      // Normalize data to match what the API expects
      onSave({
        rows: sanitizeRows(internalRows.toJS()),
        // NOTE: We actually already have the HTML string above as `html` so
        // this HTML -> AST -> HTML conversion is mostly unnecessary. That said,
        // one potential benefit this currently provides - other than keeping
        // behavior the same as before - is that it minifies the HTML so that
        // unnecessary whitespace in the HTML is removed. That in and of itself
        // isn't _that_ useful, but we could save a few bytes here and there
        // through this process. If we decide the additional CPU cycles to
        // preform this is not worth the extra bytes, we can remove this
        // conversion altogether.
        content: HTMLParser.stringify(ast) || '<div></div>',
      });
    }
  }

  render() {
    const {
      basePadding,
      internalRows,
      showAddButton,
      style,
      internalAllowedEditorTypes,
      allowedEditorTypes,
      height,
      isHoveringOverContainer,
      onEditorMenuOpen,
      onEditorMenuClose,
      shouldCloseMenu,
      resetShouldCloseMenu,
      numPages,
      isInEditMode,
      patternType,
      theme,
      track,
      disabled,
    } = this.props;

    const rowNodes = internalRows.size
      ? internalRows.map((row, i) => {
          return row.get('zones') && row.get('zones').size ? (
            <RowContainer
              numPages={numPages}
              key={row.get('id')}
              basePadding={basePadding}
              row={row}
              rowIndex={i}
              totalRows={internalRows.size}
              addZone={(type, defaultAction, existingProps) =>
                this.addNewZone(type, row, defaultAction, existingProps)
              }
              removeZone={this.removeZone.bind(this)}
              insertZone={this.insertZone.bind(this)}
              moveZone={this.moveZone.bind(this)}
              isInEditMode={isInEditMode}
              onDrop={(sourceRowIndex, targetRowIndex) =>
                this.moveRows(sourceRowIndex, targetRowIndex)
              }
              internalAllowedEditorTypes={internalAllowedEditorTypes}
              onEditorMenuOpen={onEditorMenuOpen}
              onEditorMenuClose={onEditorMenuClose}
              shouldCloseMenu={shouldCloseMenu}
              resetShouldCloseMenu={resetShouldCloseMenu}
              patternType={patternType}
              theme={theme}
              track={track}
              disabled={disabled}
            />
          ) : (
            <FullAddElement
              baseHeight={height}
              key={row.get('id')}
              allowedEditorTypes={allowedEditorTypes}
              onUpload={imageDetails => this.handleAddImage(imageDetails)}
              onSelectEditorType={(type, rowsToAdd, defaultAction) => {
                this.addRow(type, rowsToAdd, defaultAction);
                track('Builder interaction', {
                  name: 'Added content block',
                  componentName: type,
                });
              }}
              internalAllowedEditorTypes={internalAllowedEditorTypes}
              patternType={patternType}
            />
          );
        })
      : null;

    const fullScreenAddNode = !internalRows.size ? (
      <FullAddElement
        baseHeight={height}
        allowedEditorTypes={allowedEditorTypes}
        onUpload={imageDetails => this.handleAddImage(imageDetails)}
        onSelectEditorType={(type, rowsToAdd, defaultAction) => {
          this.addRow(type, rowsToAdd, defaultAction);
          track('Builder interaction', {
            name: 'Added content block',
            componentName: type,
          });
        }}
        internalAllowedEditorTypes={internalAllowedEditorTypes}
        patternType={patternType}
      />
    ) : null;

    const addButtonNode = ({ shouldUnshiftRow, customContainerStyle }) =>
      showAddButton ? (
        <AddButtonArea
          customContainerStyle={customContainerStyle}
          basePadding={basePadding}
          isHoveringOverContainer={isHoveringOverContainer}
          onSelectEditorType={(type, rowsToAdd, defaultAction) => {
            this.addRow(type, rowsToAdd, defaultAction, shouldUnshiftRow);
            onEditorMenuClose && onEditorMenuClose();
            track('Builder interaction', {
              name: 'Added content block',
              componentName: type,
            });
          }}
          moveZoneToNewRow={zoneId =>
            this.moveZoneToNewRow(
              zoneId,
              shouldUnshiftRow ? 0 : internalRows.size
            )
          }
          internalAllowedEditorTypes={internalAllowedEditorTypes}
          onEditorMenuOpen={onEditorMenuOpen}
          onEditorMenuClose={onEditorMenuClose}
          shouldCloseMenu={shouldCloseMenu}
          resetShouldCloseMenu={resetShouldCloseMenu}
          patternType={patternType}
        />
      ) : null;

    return (
      <div className="canvas" style={style} ref={el => (this.wrapper = el)}>
        {this.renderKeyframeStyles()}
        {this.renderDraftJSStyles()}
        {addButtonNode({
          shouldUnshiftRow: true,
          customContainerStyle: { top: -basePadding / 2 },
        })}
        {rowNodes}
        {fullScreenAddNode}
        {addButtonNode({ shouldUnshiftRow: false })}
      </div>
    );
  }
}

Canvas.propTypes = {
  style: PropTypes.object,
  onSave: PropTypes.func,
  dispatch: PropTypes.func,
  shouldDisableXSS: PropTypes.bool,
  rows: PropTypes.instanceOf(List),
  internalRows: PropTypes.instanceOf(List),
  internalZones: PropTypes.instanceOf(Map),
  cloudinary: PropTypes.instanceOf(Map),
  userProperties: PropTypes.instanceOf(List),
  sanitizeHtml: PropTypes.instanceOf(Map),
  allowedEditorTypes: PropTypes.instanceOf(List),
  internalAllowedEditorTypes: PropTypes.instanceOf(List),
  canvasPosition: PropTypes.instanceOf(Map),
  showAddButton: PropTypes.bool,
  startEditable: PropTypes.bool,
  disableAddButton: PropTypes.bool,
  maxRows: PropTypes.number,
  height: PropTypes.string,
  basePadding: PropTypes.number,
  isHoveringOverContainer: PropTypes.bool,
  isInEditMode: PropTypes.bool,
  onEditStart: PropTypes.func,
  onEditEnd: PropTypes.func,
  onEditorMenuOpen: PropTypes.func,
  onEditorMenuClose: PropTypes.func,
  shouldCloseMenu: PropTypes.bool,
  resetShouldCloseMenu: PropTypes.func,
  numPages: PropTypes.number,
  patternType: PropTypes.string,
  theme: PropTypes.object,
  track: PropTypes.func,
  disabled: PropTypes.bool,
};

function mapStateToProps(state, ownProps) {
  return {
    // Convert these to immutable if they're passed in from the public API
    rows: ownProps.rows ? fromJS(ownProps.rows) : List(),
    cloudinary: ownProps.cloudinary ? fromJS(ownProps.cloudinary) : Map(),
    userProperties:
      ownProps.userProperties && ownProps.userProperties.length
        ? fromJS(ownProps.userProperties)
        : List(),
    sanitizeHtml: ownProps.sanitizeHtml ? fromJS(ownProps.sanitizeHtml) : Map(),
    allowedEditorTypes: ownProps.allowedEditorTypes
      ? fromJS(ownProps.allowedEditorTypes)
      : List(),
    isInEditMode: state.editor.get('isCanvasInEditMode'),

    // Internal mappings some of the above properties

    internalRows: state.rows,
    internalZones: state.zones,
    internalAllowedEditorTypes: state.editor.get('allowedEditorTypes'),

    canvasPosition: state.editor.get('canvasPosition'),

    showAddButton: !!(
      !ownProps.disabled &&
      (!ownProps.maxRows || ownProps.maxRows < state.rows.size) &&
      !state.editor.get('disableAddButton') &&
      !state.editor.get('isCanvasInEditMode') &&
      state.rows.size &&
      state.rows.every(row => row.get('zones') && row.get('zones').size)
    ),
  };
}

export default connect(mapStateToProps)(Canvas);
