import React, { Component } from 'react';
import PropTypes from 'prop-types';
import tinyColor from 'tinycolor2';
import { Map, List } from 'immutable';
import { connect } from 'react-redux';
import { DropTarget, DragSource } from 'react-dnd';

import { convertBoundingBox } from '../helpers/domHelpers';
import { DRAGABLE_ITEMS, MAX_ZONES, EDITOR_TYPES } from '../helpers/constants';
import {
  colors,
  draggingOverlayStyle,
  zoneContainerStyles,
} from '../helpers/styles/editor';

import * as editorActions from '../actions/editorActions';
import * as zoneActions from '../actions/zoneActions';

import RichTextEditor from '../editors/richtext/RichTextEditor';
import RichTextToolbar from '../editors/richtext/RichTextToolbar';
import RichTextInlineActions from '../editors/richtext/RichTextInlineActions';

import ImageEditor from '../editors/image/ImageEditor';
import ImageToolbar from '../editors/image/ImageToolbar';

import EmojiEditor from '../editors/emoji/EmojiEditor';
import EmojiToolbar from '../editors/emoji/EmojiToolbar';

import ButtonEditor from '../editors/button/ButtonEditor';
import ButtonToolbar from '../editors/button/ButtonToolbar';

import HtmlEditor from '../editors/html/HtmlEditor';
import HtmlEditorToolbar from '../editors/html/HtmlEditorToolbar';

import VideoEditor from '../editors/video/VideoEditor';
import VideoEditorToolbar from '../editors/video/VideoEditorToolbar';

import HeroEditor from '../editors/hero/HeroEditor';
import HeroToolbar from '../editors/hero/HeroToolbar';

import TextInputEditor from '../editors/form-text/TextInputEditor';
import TextInputToolbar from '../editors/form-text/TextInputToolbar';

import TextAreaInputEditor from '../editors/form-textarea/TextAreaInputEditor';
import TextAreaInputToolbar from '../editors/form-textarea/TextAreaInputToolbar';

import SelectionEditor from '../editors/form-select/SelectionEditor';
import SelectionToolbar from '../editors/form-select/SelectionToolbar';

import RatingEditor from '../editors/rating/RatingEditor';
import RatingToolbar from '../editors/rating/RatingToolbar';
import HyperlinkInline from '../editor-actions/HyperlinkInline';

import EditorWrapper from './EditorWrapper';
import DismissLinkEditor from '../editors/richtext/DismissLinkEditor';

const zoneBarStyle = {
  background: colors.informationalBlue,
  pointerEvents: 'none',
  position: 'absolute',
  height: '100%',
  opacity: 0,
  width: 4,
  left: 0,
  right: null,
  top: 0,
  transition: 'opacity 0.15s ease-out',
};

/**
 * A React component that holds the rendered content
 * or an editable area if the Zone is active
 * @class
 */
class Zone extends Component {
  constructor(props) {
    super(props);

    this.state = {
      position: Map(),
      isHover: false,
    };

    this.baseHoverStateStyle = {
      outlineColor: colors.informationalBlue,
      backgroundColor: tinyColor(colors.informationalBlue)
        .setAlpha(0.14)
        .toRgbString(),
    };

    this.baseActiveStateStyle = {
      boxShadow: `0 0 0 100vw rgba(78,77,76,0.83), 
          rgba(0, 0, 0, 0.12) 0px 2px 10px, 
          rgba(0, 0, 0, 0.16) 0px 2px 5px
        `,
      cursor: 'inherit',
    };

    this.zoneStyle = {
      outlineStyle: 'dotted',
      outlineWidth: '2px',
      outlineColor: 'transparent',
      display: 'inline-block',
      position: 'relative',
      margin: 0,
      padding: 0,
      width: '100%',
      height: '100%',
      transition:
        'background-color 0.15s ease-out, box-shadow 0.15s ease-out, outline-color 0.15s ease-out',
      cursor: !props.disabled ? '-webkit-grab' : '',
    };

    this.cancelEditing = this.cancelEditing.bind(this);
    this.removeZone = this.removeZone.bind(this);
  }

  componentDidMount() {
    const { dispatch, zone, persistedState } = this.props;
    // Force the underlying editor component to regenerate the HTML for this zone
    if (this.activeEditor) {
      dispatch(
        zoneActions.updateZoneHtml(
          zone.get('id'),
          this.activeEditor.generateHTML(persistedState)
        )
      );
    }
    this.setBoundingBox();
    this.setFocus();
  }

  componentDidUpdate() {
    this.setFocus();
  }

  setFocus() {
    // The idea here is to reset the focus to the Draft Editor in most cases
    // except when the user has clicked on an input element, which needs to
    // maintain the focus in order to be usable.

    const isFocusableElement =
      this.wrapper.getRootNode().activeElement &&
      ['SELECT', 'INPUT'].includes(
        this.wrapper.getRootNode().activeElement.tagName
      );
    if (this.activeEditor && !isFocusableElement) {
      this.activeEditor.focus();
    }
  }

  removeZone() {
    const { row, zone, removeZone } = this.props;
    removeZone(row, zone, true);
  }

  toggleHover(isHover) {
    this.setState({
      isHover,
    });
  }

  save() {
    const { dispatch, zone, persistedState, row } = this.props;
    let { html } = this.props;

    // Handles a zone that for some reason has no generated HTML
    if (this.activeEditor && (!html || !html.length)) {
      html = this.activeEditor.generateHTML(persistedState);
    }

    const updatedZone = zone.set('persistedState', persistedState);

    dispatch(zoneActions.updateZoneHtml(zone.get('id'), html));
    dispatch(editorActions.updateZone(row, updatedZone));

    dispatch(editorActions.cancelEditing(zone));
    this.toggleHover(false);
  }

  startEditing() {
    const { dispatch, zone } = this.props;
    dispatch(editorActions.startEditing(zone));
  }

  cancelEditing() {
    const { dispatch, row, zone, removeZone } = this.props;
    if (zone.get('persistedState').isEmpty()) {
      removeZone(row, zone, false);
    }
    dispatch(editorActions.cancelEditing(zone));
    this.toggleHover(false);
  }

  setBoundingBox() {
    if (!this.wrapper) {
      return;
    }
    const position = convertBoundingBox(this.wrapper.getBoundingClientRect());
    if (!position.equals(this.state.position)) {
      this.setState({ position });
    }
  }

  handleEditorChange = update => {
    const { isEditing, dispatch } = this.props;

    // This onchange is being fired everytime we enter a zone.
    // When we go to update a new button, it pulls the last
    // draft state from the recent zone. Adding the flag will prevent
    // the draft state to be updated while editing.

    if (isEditing) {
      dispatch(
        editorActions.updateDraft({
          localState: update.localState,
          draftPersistedState: update.persistedState,
          html: update.html,
        })
      );
    }
  };

  render() {
    const { position } = this.state;
    const {
      connectDragSource,
      connectDragPreview,
      connectDropTarget,
      isDragging,
      isMovable,
      dispatch,
      columnIndex,
      row,
      zone,
      canvasPosition,
      rowPosition,
      isEditing,
      isEditingAny,
      localState,
      disableAddButton,
      persistedState,
      cloudinary,
      userProperties,
      isOver,
      numPages,
      canDrop,
      theme,
      track,
      disabled,
      patternType,
    } = this.props;
    const { isHover } = this.state;

    const hoverStateStyle =
      isHover && !isDragging && !isEditingAny ? this.baseHoverStateStyle : null;
    const activeStateStyle = isEditing ? this.baseActiveStateStyle : null;
    const moveZoneBarStyle = {
      ...zoneBarStyle,
      ...(isOver && !isHover ? { opacity: 1 } : {}),
    };
    const isDraggingStyle = isDragging ? { opacity: 0 } : {};

    const containerStyle =
      isEditing || (isHover && !isEditingAny)
        ? { ...zoneContainerStyles, position: 'relative', zIndex: 10 }
        : zoneContainerStyles;
    const zoneStyle = {
      ...this.zoneStyle,
      ...hoverStateStyle,
      ...activeStateStyle,
      ...isDraggingStyle,
    };

    // Common props across all editors
    const editorProps = {
      persistedState,
      localState,
      canvasPosition,
      zone,
      zonePosition: position,
      isEditing,
      cloudinary,
      userProperties,
      onChange: this.handleEditorChange,
      ref: editor => {
        this.activeEditor = editor;
      },
    };

    const updateDraftWithChanges = update => {
      if (!isEditing) {
        return;
      }

      if (this.activeEditor) {
        const html = this.activeEditor.generateHTML(update.persistedState);

        dispatch(
          editorActions.updateDraft({
            localState: update.localState,
            draftPersistedState: update.persistedState,
            html,
          })
        );
      }
    };

    // Common props across all toolbars
    const toolbarProps = {
      persistedState,
      localState,
      canvasPosition,
      zone,
      zonePosition: position,
      cloudinary,
      userProperties,
      onChange: updateDraftWithChanges,
      focusEditor: () => {
        const ed = this.activeEditor;
        ed && ed.focus && ed.focus();
      },
    };

    const inlineActionsProps = {
      persistedState,
      localState,
      onChange: updateDraftWithChanges,
    };

    let editorNode;
    let toolbarNode;
    let inlineActionsNode = null;
    const type = zone.get('type');

    switch (type) {
      case EDITOR_TYPES.TEXT:
        editorNode = <RichTextEditor {...editorProps} />;
        toolbarNode = <RichTextToolbar {...toolbarProps} />;
        inlineActionsNode = <RichTextInlineActions {...inlineActionsProps} />;
        break;
      case EDITOR_TYPES.IMAGE:
        editorNode = <ImageEditor {...editorProps} />;
        toolbarNode = <ImageToolbar {...toolbarProps} />;
        break;
      case EDITOR_TYPES.EMOJI:
        editorNode = (
          <EmojiEditor
            {...editorProps}
            onClickEmptyState={() =>
              dispatch(editorActions.toggleEditorAction('emoji-selector', true))
            }
          />
        );
        toolbarNode = <EmojiToolbar {...toolbarProps} />;
        break;
      case EDITOR_TYPES.BUTTON:
        editorNode = (
          <ButtonEditor
            {...editorProps}
            theme={theme}
            patternType={patternType}
          />
        );
        toolbarNode = (
          <ButtonToolbar
            numPages={numPages}
            track={track}
            patternType={patternType}
            {...toolbarProps}
          />
        );
        break;
      case EDITOR_TYPES.HTML:
        editorNode = <HtmlEditor {...editorProps} />;
        toolbarNode = <HtmlEditorToolbar {...toolbarProps} />;
        break;
      case EDITOR_TYPES.VIDEO:
        editorNode = <VideoEditor {...editorProps} />;
        toolbarNode = <VideoEditorToolbar {...toolbarProps} />;
        break;
      case EDITOR_TYPES.HERO:
        editorNode = <HeroEditor {...editorProps} />;
        toolbarNode = <HeroToolbar {...toolbarProps} />;
        break;
      case EDITOR_TYPES.TEXTINPUT:
        editorNode = <TextInputEditor {...editorProps} />;
        toolbarNode = <TextInputToolbar {...toolbarProps} />;
        break;
      case EDITOR_TYPES.TEXTAREAINPUT:
        editorNode = <TextAreaInputEditor {...editorProps} />;
        toolbarNode = <TextAreaInputToolbar {...toolbarProps} />;
        break;
      case EDITOR_TYPES.RADIO:
        editorNode = <SelectionEditor {...editorProps} />;
        toolbarNode = <SelectionToolbar {...toolbarProps} />;
        break;
      case EDITOR_TYPES.MULTI_SELECT:
        editorNode = <SelectionEditor {...editorProps} type="checkbox" />;
        toolbarNode = <SelectionToolbar {...toolbarProps} type="checkbox" />;
        break;
      case EDITOR_TYPES.RATING:
        editorNode = <RatingEditor {...editorProps} />;
        toolbarNode = <RatingToolbar {...toolbarProps} />;
        break;
      case EDITOR_TYPES.DISMISS_LINK:
        editorNode = <DismissLinkEditor {...editorProps} />;
        toolbarNode = (
          <RichTextToolbar
            {...toolbarProps}
            filterActions={actions =>
              actions.filter(it => it.name !== HyperlinkInline.actionName)
            }
          />
        );
        break;
      default:
        break;
    }

    const DragIfPossible = children =>
      isMovable ? connectDragSource(children) : children;

    const isDismissLink = type === EDITOR_TYPES.DISMISS_LINK;

    return DragIfPossible(
      connectDropTarget(
        <div
          className={`zone-container zone-${columnIndex}`}
          style={containerStyle}
          onMouseEnter={() => !disabled && this.toggleHover(true)}
          onMouseLeave={() => !disabled && this.toggleHover(false)}
          onClick={() => {
            if (!isEditingAny && !disabled) {
              this.startEditing();
            }
          }}
          onDoubleClick={() => {
            if (!isEditingAny && !disabled) {
              this.startEditing();
            }
          }}
          ref={el => (this.wrapper = el)}
        >
          {connectDragPreview(
            <div className="zone" style={zoneStyle}>
              <EditorWrapper
                disableCancelButton={zone.get('persistedState')?.isEmpty()}
                rowPosition={rowPosition}
                zonePosition={position}
                isEditing={isEditing}
                isHover={isHover}
                disableDeleteButton={disableAddButton || isDismissLink}
                onSave={() => {
                  this.save();
                }}
                onCancel={this.cancelEditing}
                onRemove={this.removeZone}
                onMoveRowStart={() => {
                  dispatch(editorActions.startMoving(row));
                }}
                onMoveRowEnd={() => {
                  dispatch(editorActions.endMoving(row));
                }}
                toolbarNode={toolbarNode}
                inlineActionsNode={inlineActionsNode}
              >
                {editorNode}
              </EditorWrapper>
              {canDrop && <div style={moveZoneBarStyle} />}
              {!canDrop && (
                <span
                  style={{
                    zIndex: 101,
                    width: 200,
                    position: 'absolute',
                    background: '#e2e2e2',
                    borderRadius: 4,
                    pointerEvents: 'none',
                    boxShadow: '0 0 12px rgba(30, 30, 70, 0.3)',
                    transition: 'opacity 0.15s ease-out',
                    opacity: isOver ? 1 : 0,
                    padding: 4,
                  }}
                >
                  Cannot exceed {MAX_ZONES} items per row
                </span>
              )}
            </div>
          )}
          {isDragging && <div style={draggingOverlayStyle} />}
        </div>
      )
    );
  }
}

Zone.propTypes = {
  connectDropTarget: PropTypes.func,
  connectDragPreview: PropTypes.func,
  connectDragSource: PropTypes.func,
  dispatch: PropTypes.func,
  zone: PropTypes.instanceOf(Map),
  row: PropTypes.instanceOf(Map),
  columnIndex: PropTypes.number,
  canvasPosition: PropTypes.instanceOf(Map),
  rowPosition: PropTypes.instanceOf(Map),
  localState: PropTypes.instanceOf(Map),
  persistedState: PropTypes.instanceOf(Map),
  html: PropTypes.string,
  isMovable: PropTypes.bool,
  isDragging: PropTypes.bool,
  isEditing: PropTypes.bool,
  isEditingAny: PropTypes.bool,
  // isHover: PropTypes.bool,
  isHover: PropTypes.bool,
  disableAddButton: PropTypes.bool,
  userProperties: PropTypes.instanceOf(List),
  cloudinary: PropTypes.instanceOf(Map),
  basePadding: PropTypes.number,
  isOver: PropTypes.bool,
  numPages: PropTypes.number,
  removeZone: PropTypes.func,
  insertZone: PropTypes.func,
  moveZone: PropTypes.func,
  canDrop: PropTypes.bool,
  theme: PropTypes.object,
  track: PropTypes.func,
  disabled: PropTypes.bool,
  patternType: PropTypes.string,
};

function mapStateToProps(state, ownProps) {
  // PersistedState is either a draft if we're actively editing
  // or the persistedState from the zone data if we're not in edit mode
  const zoneId = ownProps.zone.get('id');
  const isEditing = !!(
    state.editor.get('isCanvasInEditMode') &&
    state.editor.get('activeZoneId') === zoneId
  );
  const isEditingAny = state.editor.get('isCanvasInEditMode');
  const persistedState = isEditing
    ? state.editor.get('draftPersistedState')
    : ownProps.zone.get('persistedState') ?? new Map();
  const html = isEditing
    ? state.editor.get('draftHtml')
    : state.zones.has(zoneId)
    ? state.zones.get(zoneId).get('html')
    : null;

  return {
    localState: state.editor.get('localState'),
    persistedState,
    html,
    isEditing,
    isEditingAny,
    basePadding: state.editor.get('basePadding'),
    disableAddButton: state.editor.get('disableAddButton'),
    canvasPosition: state.editor.get('canvasPosition'),
    userProperties: state.editor.get('userProperties'),
    cloudinary: state.editor.get('cloudinary'),
    isMovable: !!(
      !ownProps.disabled &&
      !state.editor.get('isCanvasInEditMode') &&
      (state.rows.size > 1 || state.zones.size > 1)
    ),
  };
}

const zoneSource = {
  isDragging(props, monitor) {
    return props.zone.get('id') === monitor.getItem().zone.get('id');
  },
  beginDrag(props) {
    props.setIsHoveringOverRowContainer(false);
    return {
      row: props.row,
      isInLastRow:
        props.row.get('zones').size === 1 &&
        props.rowIndex === props.totalRows - 1,
      zone: props.zone,
      columnIndex: props.columnIndex,
    };
  },
};

function collectSource(connect, monitor) {
  return {
    connectDragSource: connect.dragSource(),
    connectDragPreview: connect.dragPreview(),
    isDragging: monitor.isDragging(),
  };
}

const zoneTarget = {
  canDrop(props, monitor) {
    const sourceProps = monitor.getItem();
    return (
      sourceProps.row.get('id') === props.row.get('id') ||
      props.row.get('zones').size < MAX_ZONES
    );
  },
  hover(targetProps, monitor) {
    const sourceProps = monitor.getItem();
    if (
      targetProps.row.get('id') === sourceProps.row.get('id') &&
      sourceProps.columnIndex < targetProps.columnIndex
    ) {
      zoneBarStyle.right = 0;
      zoneBarStyle.left = null;
    } else {
      zoneBarStyle.left = 0;
      zoneBarStyle.right = null;
    }
  },
  drop(targetProps, monitor) {
    const sourceProps = monitor.getItem();
    if (targetProps.zone.get('id') === sourceProps.zone.get('id')) return;
    targetProps.moveZone(
      sourceProps.zone.get('id'),
      targetProps.row.get('id'),
      targetProps.columnIndex
    );
  },
};

function collectTarget(connect, monitor) {
  return {
    connectDropTarget: connect.dropTarget(),
    isOver: monitor.isOver(),
    canDrop: monitor.canDrop(),
  };
}

export default DropTarget(
  DRAGABLE_ITEMS.ZONE,
  zoneTarget,
  collectTarget
)(
  DragSource(
    DRAGABLE_ITEMS.ZONE,
    zoneSource,
    collectSource
  )(connect(mapStateToProps)(Zone))
);
