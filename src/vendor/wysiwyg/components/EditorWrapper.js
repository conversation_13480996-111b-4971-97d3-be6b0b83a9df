import React from 'react';
import PropTypes from 'prop-types';
import { Map } from 'immutable';
import styled from 'styled-components';
import Tether from 'ext/components/Tether';
import ConfirmButton from '../icons/ConfirmButton';

const EditorActions = styled.div`
  align-items: center;
  display: flex;
  justify-content: center;
  white-space: nowrap;
`;

const OkButton = styled.div`
  circle {
    fill: #acf3f0;
  }

  &:hover circle {
    fill: #c2f6f4;
  }
`;

const CancelButton = styled.div`
  circle {
    fill: #e7ecf3;
  }

  &:hover circle {
    fill: #f8f9fb;
  }
`;

const DeleteButton = styled.div`
  circle {
    fill: #ffdfe2;
  }

  &:hover circle {
    fill: #fff8f9;
  }
`;

const Toolbar = styled.div`
  margin-right: 20px;
  z-index: 100;
`;

const ToolbarAndActionsContainer = styled.div`
  display: flex;
  flex-direction: column-reverse;
  align-items: flex-start;
  gap: 6px;
  position: absolute;
`;

/**
 * A React component that wraps a Zone component when
 * editing is active. It provides the toolbars for Save/Cancel/etc
 * @class
 */

const EditorWrapper = ({
  isEditing,
  isHover,
  children,
  toolbarNode,
  inlineActionsNode,
  onSave,
  onCancel,
  onRemove,
  disableDeleteButton,
  disableCancelButton,
}) => {
  const renderToolbarAndActions = ({ ref }) => (
    <div aria-label="WYSIWYG Toolbar and Actions" data-theme="light">
      <ToolbarAndActionsContainer ref={ref}>
        {toolbarNode && <Toolbar data-wysiwyg="toolbar">{toolbarNode}</Toolbar>}

        <EditorActions>
          <OkButton>
            <ConfirmButton
              onClick={onSave}
              ariaLabel="save"
              pathNode={
                <svg
                  width="40"
                  height="40"
                  viewBox="0 0 40 40"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <circle cx="20" cy="20" r="20" />
                  <path
                    d="M17.1527 26.3544L11.3541 20.5905C11.1921 20.4285 11.1111 20.2201 11.1111 19.9655C11.1111 19.7109 11.1921 19.5025 11.3541 19.3405L12.6389 18.0905C12.8009 17.9053 13.0034 17.8127 13.2465 17.8127C13.4895 17.8127 13.7037 17.9053 13.8889 18.0905L17.7777 21.9794L26.111 13.6461C26.2962 13.4609 26.5103 13.3683 26.7534 13.3683C26.9965 13.3683 27.199 13.4609 27.361 13.6461L28.6458 14.8961C28.8078 15.0581 28.8888 15.2664 28.8888 15.5211C28.8888 15.7757 28.8078 15.984 28.6458 16.1461L18.4027 26.3544C18.2407 26.5395 18.0324 26.6321 17.7777 26.6321C17.5231 26.6321 17.3148 26.5395 17.1527 26.3544Z"
                    fill="#1CA4B0"
                  />
                </svg>
              }
              viewBox="0 0 40 40"
            />
          </OkButton>

          {!disableCancelButton && (
            <CancelButton>
              <ConfirmButton
                onClick={onCancel}
                ariaLabel="cancel"
                pathNode={
                  <svg
                    width="40"
                    height="40"
                    viewBox="0 0 40 40"
                    fill="none"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <circle cx="20" cy="20" r="20" />
                    <path
                      d="M22.8551 19.9999L27.1165 24.2613C27.3722 24.5171 27.5 24.8296 27.5 25.1988C27.5 25.5681 27.3722 25.8948 27.1165 26.179L26.179 27.1165C25.895 27.3722 25.5681 27.5 25.1989 27.5C24.8296 27.5 24.5171 27.3722 24.2613 27.1165L20.0001 22.8551L15.7387 27.1165C15.4829 27.3722 15.1704 27.5 14.8012 27.5C14.4319 27.5 14.1052 27.3722 13.8211 27.1165L12.8835 26.179C12.6278 25.8948 12.5 25.5681 12.5 25.1988C12.5 24.8296 12.6278 24.5171 12.8835 24.2613L17.1449 19.9999L12.8835 15.7387C12.6278 15.4829 12.5 15.1704 12.5 14.8011C12.5 14.4319 12.6278 14.1052 12.8835 13.821L13.8211 12.8835C14.1052 12.6278 14.4319 12.5 14.8012 12.5C15.1704 12.5 15.4829 12.6278 15.7387 12.8835L20.0001 17.1449L24.2613 12.8835C24.5171 12.6278 24.8296 12.5 25.1989 12.5C25.5681 12.5 25.895 12.6278 26.179 12.8835L27.1165 13.821C27.3722 14.1052 27.5 14.4319 27.5 14.8011C27.5 15.1704 27.3722 15.4829 27.1165 15.7387L22.8551 19.9999Z"
                      fill="#7E89A9"
                    />
                  </svg>
                }
                viewBox="0 0 40 40"
              />
            </CancelButton>
          )}

          {!disableDeleteButton && (
            <DeleteButton>
              <ConfirmButton
                onClick={onRemove}
                ariaLabel="delete"
                pathNode={
                  <svg
                    width="40"
                    height="40"
                    viewBox="0 0 40 40"
                    fill="none"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <circle cx="20" cy="20" r="20" fill="#FFEDF3" />
                    <path
                      d="M12 13.2812V12.1875C12 11.668 12.3821 11.25 12.8571 11.25H16.8571L17.1929 10.5195C17.3357 10.1992 17.6321 10 17.9571 10H22.0393C22.3643 10 22.6607 10.1992 22.8071 10.5195L23.1429 11.25H27.1429C27.6178 11.25 28 11.668 28 12.1875V13.2812C28 13.5391 27.8071 13.75 27.5715 13.75H12.4286C12.1929 13.75 12 13.5391 12 13.2812ZM26.8571 15.4687V28.125C26.8571 29.1602 26.0893 30 25.1429 30H14.8571C13.9107 30 13.1429 29.1602 13.1429 28.125V15.4687C13.1429 15.2109 13.3357 15 13.5714 15H26.4286C26.6642 15 26.8571 15.2109 26.8571 15.4687ZM17.1429 18.125C17.1429 17.7812 16.8857 17.5 16.5714 17.5C16.2571 17.5 16 17.7812 16 18.125V26.875C16 27.2188 16.2571 27.5 16.5714 27.5C16.8857 27.5 17.1429 27.2188 17.1429 26.875V18.125ZM20.5714 18.125C20.5714 17.7812 20.3143 17.5 20 17.5C19.6857 17.5 19.4286 17.7812 19.4286 18.125V26.875C19.4286 27.2188 19.6857 27.5 20 27.5C20.3143 27.5 20.5714 27.2188 20.5714 26.875V18.125ZM24 18.125C24 17.7812 23.7429 17.5 23.4286 17.5C23.1143 17.5 22.8571 17.7812 22.8571 18.125V26.875C22.8571 27.2188 23.1143 27.5 23.4286 27.5C23.7429 27.5 24 27.2188 24 26.875V18.125Z"
                      fill="#D6216B"
                    />
                  </svg>
                }
                viewBox="0 0 40 40"
              />
            </DeleteButton>
          )}
        </EditorActions>
      </ToolbarAndActionsContainer>

      {inlineActionsNode && <div>{inlineActionsNode}</div>}
    </div>
  );

  return (
    <div className="zone-content">
      <div className={isEditing ? 'editing' : isHover ? 'hover' : ''}>
        <Tether
          attachment={renderToolbarAndActions}
          offset={{ y: 16 }}
          placement="bottom"
          visible={isEditing}
          wrapped
        >
          {children}
        </Tether>
      </div>
    </div>
  );
};

EditorWrapper.propTypes = {
  isEditing: PropTypes.bool,
  isHover: PropTypes.bool,
  children: PropTypes.element,
  onSave: PropTypes.func,
  onCancel: PropTypes.func,
  onRemove: PropTypes.func,
  rowPosition: PropTypes.instanceOf(Map),
  zonePosition: PropTypes.instanceOf(Map),
  toolbarNode: PropTypes.node,
  inlineActionsNode: PropTypes.node,
  disableDeleteButton: PropTypes.bool,
  disableCancelButton: PropTypes.bool,
};

export default EditorWrapper;
