import React, { forwardRef, useEffect } from 'react';
import PropTypes from 'prop-types';

const contentEditableInputStyles = {
  display: 'block',
  background: 'transparent',
  border: 'none',
  outline: 'none',
  padding: '0',
  margin: '0',
  color: 'currentColor',
  fieldSizing: 'content',
  fontSize: 'inherit',
  fontWeight: 'inherit',
  fontFamily: 'inherit',
  lineHeight: 'inherit',
  textAlign: 'inherit',
};

const ContentEditable = forwardRef(
  (
    { defaultValue, className, id, style, onTextChange, onBlur, onClick },
    ref
  ) => {
    const handleValue = e => {
      ref.current.textContent = e.target.value;
      onTextChange && onTextChange(e.target.value);
    };

    useEffect(() => {
      if (defaultValue) {
        ref.current.value = defaultValue;
      }
    }, [defaultValue, ref]);

    return (
      <input
        defaultValue={defaultValue}
        tabIndex={0}
        className={className}
        id={id}
        style={{ ...contentEditableInputStyles, ...style }}
        ref={ref}
        onChange={handleValue}
        onBlur={onBlur}
        onClick={onClick}
      />
    );
  }
);

ContentEditable.propTypes = {
  defaultValue: PropTypes.string,
  onTextChange: PropTypes.func,
  className: PropTypes.string,
  id: PropTypes.string,
  style: PropTypes.object,
  onBlur: PropTypes.func,
  onClick: PropTypes.func,
};

export default ContentEditable;
