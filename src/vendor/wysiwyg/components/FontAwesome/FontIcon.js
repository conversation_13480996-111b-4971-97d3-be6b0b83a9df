import React from 'react';
import reactCSS from 'reactcss';

import { library } from '@fortawesome/fontawesome-svg-core';
import { fas } from '@fortawesome/free-solid-svg-icons';
import { far } from '@fortawesome/free-regular-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';

library.add(fas, far);

const FontIcon = ({ icon, color, svgStyle, ...rest }) => {
  const styles = reactCSS({
    default: {
      main: svgStyle,
    },
  });
  return (
    <FontAwesomeIcon style={styles.main} icon={icon} color={color} {...rest} />
  );
};

export default FontIcon;
