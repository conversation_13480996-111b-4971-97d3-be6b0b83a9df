import React from 'react';
import PropTypes from 'prop-types';
import { List } from 'immutable';
import { connect } from 'react-redux';

import TextButton from '../icons/TextButton';
import EmojiBlockButton from '../icons/EmojiBlockButton';
import ImageBlockButton from '../icons/ImageBlockButton';
import VideoButton from '../icons/VideoButton';
import CodeButton from '../icons/CodeButton';
import HeroButton from '../icons/HeroButton';
import ButtonButton from '../icons/ButtonButton';
import FormRadioButton from '../icons/FormRadioButton';
import FormMultiSelectButton from '../icons/FormMultiSelectButton';
import FormLargeInput from '../icons/FormLargeInput';
import FormSmallInput from '../icons/FormSmallInput';
import FormRatingButton from '../icons/FormRatingButton';

import { colors } from '../helpers/styles/editor';
import { EDITOR_TYPES, PATTERN_TYPES, categories } from '../helpers/constants';
import FontIcon from './FontAwesome/FontIcon';
import Menu from './Menu';

const COMPONENT_REQUEST_LINK = 'https://appcues.typeform.com/to/d6NW69';

const MENU_DIMENSIONS = {
  [PATTERN_TYPES.TOOLTIP_GROUP]: {
    height: 270,
    width: 256,
  },
  [PATTERN_TYPES.HOTSPOT_GROUP]: {
    height: 270,
    width: 256,
  },
  [PATTERN_TYPES.MODAL]: {
    height: 494,
    width: 377,
  },
  [PATTERN_TYPES.SLIDEOUT]: {
    height: 494,
    width: 377,
  },
};

const MENU_DIMENSIONS_FORMAT_VERSION_2 = {
  ...MENU_DIMENSIONS,
  [PATTERN_TYPES.TOOLTIP_GROUP]: {
    height: 388,
    width: 256,
  },
  [PATTERN_TYPES.HOTSPOT_GROUP]: {
    height: 388,
    width: 256,
  },
};

const editors = {
  [EDITOR_TYPES.TEXT]: {
    Button: TextButton,
    text: 'Text',
    type: 'RichText',
    category: 'Text',
  },
  [EDITOR_TYPES.IMAGE]: {
    Button: ImageBlockButton,
    text: 'Image / GIF',
    type: 'Image',
    category: 'Media',
  },
  [EDITOR_TYPES.HERO]: {
    Button: HeroButton,
    text: 'Hero / Header',
    type: 'Hero',
    category: 'Media',
  },
  [EDITOR_TYPES.EMOJI]: {
    Button: EmojiBlockButton,
    text: 'Emoji',
    type: 'Emoji',
    category: 'Media',
  },
  [EDITOR_TYPES.VIDEO]: {
    Button: VideoButton,
    text: 'Video',
    type: 'Video',
    defaultAction: 'code',
    category: 'Media',
  },
  [EDITOR_TYPES.HTML]: {
    Button: CodeButton,
    text: 'HTML',
    type: 'HTML',
    defaultAction: 'code',
    category: 'Advanced',
  },
  [EDITOR_TYPES.TEXTINPUT]: {
    Button: FormSmallInput,
    text: 'Small Input',
    type: 'TextInput',
    category: 'Forms',
  },
  [EDITOR_TYPES.TEXTAREAINPUT]: {
    Button: FormLargeInput,
    text: 'Large Input',
    type: 'TextAreaInput',
    category: 'Forms',
  },
  [EDITOR_TYPES.RADIO]: {
    Button: FormRadioButton,
    text: 'Single Select',
    type: 'SelectionField',
    category: 'Forms',
  },
  [EDITOR_TYPES.MULTI_SELECT]: {
    Button: FormMultiSelectButton,
    text: 'Multi-Select',
    type: 'MultiSelect',
    category: 'Forms',
    isNew: true,
  },
  [EDITOR_TYPES.RATING]: {
    Button: FormRatingButton,
    text: 'Rating',
    type: 'Rating',
    category: 'Forms',
  },
  [EDITOR_TYPES.BUTTON]: {
    Button: ButtonButton,
    text: 'Button',
    type: 'Button',
    category: 'Advanced',
  },
};

/**
 * A React component that displays the dropdown menu
 * for selecting which editor type to add to the Canvas
 * @class
 */
class EditorSelector extends React.Component {
  constructor(props) {
    super(props);

    this.state = {
      primaryHoverMenu: '',
      isHoveringRequestBlock: false,
    };
  }

  renderMenuItems(categoryContent) {
    const { onSelect, patternType } = this.props;
    const { primaryHoverMenu } = this.state;

    const contents = categoryContent.map(editor => {
      const isHover = editors[editor].type === primaryHoverMenu;
      const style = {
        backgroundColor: '#FFFFFF',
        width: '100%',
        height: '100%',
        borderRadius: '6px',
        border: '1px solid #E7ECF3',
        userSelect: 'none',
      };
      const editorElement = editors[editor];
      const isTooltipOrHotspot =
        patternType === PATTERN_TYPES.TOOLTIP_GROUP ||
        patternType === PATTERN_TYPES.HOTSPOT_GROUP;

      if (isHover) {
        style.boxShadow = '0px 4px 16px rgba(71, 88, 114, 0.16)';
      }

      const linkStyle = {
        textDecoration: 'none',
        display: 'inline-flex',
        borderRadius: '6px',
        margin: '8px',
        flex: isTooltipOrHotspot
          ? '0 0 calc(50% - 16px)'
          : '0 0 calc(33% - 16px)',
      };

      const NewTagStyle = {
        position: 'absolute',
        marginTop: '-8px',
        color: 'rgb(255, 255, 255)',
        background: 'rgb(92, 92, 255)',
        padding: '3px 6px',
        borderRadius: '2px',
        fontSize: '12px',
        boxShadow: 'rgba(71, 88, 114, 0.16) 0px 4px 16px',
        translate: 'calc(50% + 12px)',
      };

      return (
        <a
          href="#"
          key={editorElement.text}
          style={linkStyle}
          onClick={e => {
            e.preventDefault();
            onSelect(
              editorElement.type,
              editorElement.rows,
              editorElement.defaultAction
            );
          }}
        >
          <div
            className="menuItem"
            style={style}
            onMouseOver={() => this.setHover(editorElement.type, true)}
            onMouseOut={() => this.setHover('', false)}
          >
            {editorElement.isNew && <span style={NewTagStyle}>New</span>}

            <editorElement.Button
              isHovering={isHover}
              text={editorElement.text}
            />
          </div>
        </a>
      );
    });

    return contents;
  }

  onRequestBlockMouseOver() {
    this.setState({ isHoveringRequestBlock: true });
  }

  onRequestBlockMouseOut() {
    this.setState({ isHoveringRequestBlock: false });
  }

  setHover(primaryHoverMenu, isOver) {
    const update = {};
    if (primaryHoverMenu !== this.state.primaryHoverMenu) {
      update.primaryHoverMenu = isOver && primaryHoverMenu;
    }

    if (Object.keys(update).length) {
      this.setState(update);
    }
  }

  render() {
    const { allowedEditorTypes, forwardRef, patternType, formatVersion } =
      this.props;
    const { isHoveringRequestBlock } = this.state;

    const { height, width } =
      formatVersion === 2
        ? MENU_DIMENSIONS_FORMAT_VERSION_2[patternType]
        : MENU_DIMENSIONS[patternType];

    const menuStyle = {
      display: 'flex',
      height,
      width,
    };

    const editorMenu = categories.map(category => {
      return {
        name: category.name,
        icon: category.icon,
        items: category.content.filter(editor => {
          return allowedEditorTypes.includes(editor);
        }),
      };
    });

    const menuComponentStyle = {
      overflow: 'auto',
      display: 'flex',
      flexFlow: 'row wrap',
      padding: '8px',
      justifyContent: 'flex-start',
      backgroundColor: colors.apc_gray_1,
      borderRadius: '6px',
      boxShadow: '0px 12px 34px rgba(71, 88, 114, 0.35)',
    };

    const componentRequestBlockStyle = {
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      width: '100%',
      backgroundColor: colors.apc_gray_2,
      textAlign: 'center',
      margin: '8px 8px 0',
      height: '30px',
      color: isHoveringRequestBlock ? colors.apc_purple : colors.apc_gray_7,
      textDecoration: 'none',
      cursor: 'pointer',
      borderRadius: '6px',
    };

    const commentIconStyle = {
      fill: 'currentColor',
      color: 'currentColor',
      flex: '0 1 20px',
      marginRight: '4px',
    };

    return (
      <div ref={forwardRef} style={menuStyle}>
        <Menu style={menuComponentStyle}>
          {editorMenu.map(category => {
            return this.renderMenuItems(category.items);
          })}
          <a
            style={componentRequestBlockStyle}
            href={COMPONENT_REQUEST_LINK}
            target="_blank"
            rel="noopener noreferrer"
            onMouseOver={() => this.onRequestBlockMouseOver()}
            onMouseOut={() => this.onRequestBlockMouseOut()}
          >
            <FontIcon icon="comments" style={commentIconStyle} />
            <span>Request a block type</span>
          </a>
        </Menu>
      </div>
    );
  }
}

EditorSelector.propTypes = {
  allowedEditorTypes: PropTypes.instanceOf(List),
  forwardRef: PropTypes.shape({
    current: PropTypes.object,
  }),
  onSelect: PropTypes.func,
  patternType: PropTypes.string,
  formatVersion: PropTypes.number,
};

const mapStateToProps = state => {
  const { formatVersion } = state.flows.flow;

  return {
    formatVersion,
  };
};

export default connect(mapStateToProps, null)(EditorSelector);
