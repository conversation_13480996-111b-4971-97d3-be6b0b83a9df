import React from 'react';
import PropTypes from 'prop-types';
import styled from 'styled-components';
import { getEditorDocument } from 'ext/lib/document';
import Tether from 'ext/components/Tether';
import DownButton from '../icons/DownButton';
import { But<PERSON>, DropDownMenuList } from '../helpers/styles/editor';

const DEFAULT_CONTAINER_WIDTH = 280;

const DropDownMenuContainer = styled.div`
  flex-grow: 1;
`;

const DropdownOptionLabel = styled.span`
  color: inherit;
  font-family: ${props => (props.option.fontFamily ? props.option.value : '')};
  font-weight: ${props => (props.option.fontWeight ? props.option.value : '')};

  ${({ selected }) =>
    selected &&
    `
    color: var(--secondary);
    font-weight: var(--bold);
  `}
`;

const DropDownMenuOption = styled.div`
  cursor: pointer;
  width: inherit;
  display: flex;
  flex-shrink: 0;
  align-items: flex-start;
  text-align: left;
  padding: 10px 15px;

  &:hover {
    background-color: var(--light-text-color);
  }
`;

const OptionTextColumn = styled.div`
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  text-align: left;
  width: 100%;
`;

const OptionLabel = styled.div`
  font-size: var(--regular);
  width: 100%;
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
`;

const SearchBar = styled.div`
  display: flex;
  align-items: center;
  min-height: 36px;
  background-color: var(--light-text-color);
`;

const SearchBarInput = styled.input`
  display: flex;
  flex-grow: 1;
  height: 30px;
  background: none;
  border: none;
  opacity: 0.8;
  outline: none;
  cursor: text;
  font-size: var(--regular);
  color: #666;
`;

const TypeaheadInput = styled(SearchBarInput)`
  min-width: 252px;
`;

const DefaultValueContainer = styled.div`
  text-align: left;
  display: flex;
  justify-content: space-between;
  align-items: center;
  pointer-events: none;
`;

const ButtonIconContainer = styled.span`
  display: inline-flex;
  cursor: pointer;
  transform: scale(0.8);
  margin-bottom: 5px;
`;

export default class DropDownMenu extends React.Component {
  constructor(props) {
    super(props);
    this.wrapper = React.createRef();
    this.container = React.createRef();
    this.search = React.createRef();
    this.state = {
      selectedItem: props.selectedValue,
      isMenuOpen: false,
      searchTerm: '',
    };
  }

  getScrollableParent(node) {
    const overflowRegex = /(auto|scroll)/;

    // eslint-disable-next-line valid-typeof
    if (!node || node === document.body || typeof node !== 'Element')
      return document.body;

    const { overflow, overflowX, overflowY, position } =
      window.getComputedStyle(node);

    if (position === 'static') {
      return this.getScrollableParent(node.parentNode);
    }

    return overflowRegex.test(`${overflow}${overflowX}${overflowY}`)
      ? node
      : this.getScrollableParent(node.parentNode);
  }

  observeOnScrollParent() {
    const parent = this.getScrollableParent(this.container.current);

    if (!parent) return;

    const onScroll = () => {
      this.setState(
        {
          isMenuOpen: false,
        },
        () => parent.removeEventListener('scroll', onScroll)
      );
    };

    parent.addEventListener('scroll', onScroll);
  }

  componentDidMount() {
    const $editor = getEditorDocument();
    if ($editor) {
      $editor.addEventListener('mousedown', this.handleClickOutside);
    }
  }

  componentDidUpdate(prevProps, prevState) {
    const { isMenuOpen } = this.state;
    const { selectedValue } = this.props;

    const isMenuNowOpen = isMenuOpen && !prevState.isMenuOpen;

    if (prevProps.selectedValue !== selectedValue) {
      this.setState({ selectedItem: selectedValue });
    }

    // Need to use setTimeout when focusing the search input since
    // the search input component isn't rendered in time by the time
    // we reach this lifecycle hook. This is a side effect from passing
    // in the DropDownMenuList as an attachment to Tether
    setTimeout(() => {
      if (isMenuNowOpen && this.search && this.search.current) {
        this.search.current.focus();
      }
    }, 0);
  }

  componentWillUnmount() {
    const $editor = getEditorDocument();
    if ($editor) {
      $editor.removeEventListener('mousedown', this.handleClickOutside);
    }
  }

  /**
   * Alert if clicked on outside of element
   */
  handleClickOutside = event => {
    const { isMenuOpen } = this.state;

    if (
      this.wrapper &&
      !this.wrapper.current.contains(event.target) &&
      isMenuOpen
    ) {
      this.toggleDropDownMenu();
    }
  };

  toggleDropDownMenu = () => {
    const { onClick, disabled } = this.props;
    const { isMenuOpen, searchTerm } = this.state;
    const willOpenMenu = !isMenuOpen;

    if (disabled) return;

    this.setState({
      isMenuOpen: willOpenMenu,
      searchTerm: willOpenMenu ? '' : searchTerm,
    });

    if (willOpenMenu) {
      this.observeOnScrollParent();
    }

    if (onClick) {
      onClick();
    }
  };

  selectOptionByIndex = index => {
    const { options, onSelect } = this.props;

    if (options[index].optionDisabled) {
      this.setState({ isMenuOpen: false });
      return;
    }

    this.setState({
      selectedItem: options[index].value,
      isMenuOpen: false,
    });

    onSelect(options[index].value);
  };

  onSearchBarMouseUp(event) {
    event.nativeEvent.stopImmediatePropagation();
    event.stopPropagation();

    // Slightly different from above (see comment in componentDidUpdate)
    // since the input is currently on screen, but this setTimeout was also
    // needed to fix an issue where clicking on the input would shift focus
    // to text content in the WYSIWYG rather than to the search input. I believe
    // the issue is Tether related as well, but unsure what's going on here
    // exactly.
    setTimeout(() => {
      if (this.search && this.search.current) {
        this.search.current.focus();
      }
    }, 0);
  }

  onSearchBarChange(event) {
    const { value } = event.target;

    this.setState({
      searchTerm: value,
    });
  }

  getContainerWidth() {
    const container = this.container.current;
    return container
      ? container.getBoundingClientRect().width
      : DEFAULT_CONTAINER_WIDTH;
  }

  render() {
    const {
      title,
      label,
      options,
      renderOptionSubtextNode,
      defaultValue,
      unsearchable,
      searchPlaceholder,
      typeahead,
      disabled,
    } = this.props;
    const { selectedItem, isMenuOpen, searchTerm } = this.state;

    const selectedOption =
      selectedItem !== undefined
        ? options.find(option => option.value === selectedItem)
        : null;

    // eslint-disable-next-line react/no-unstable-nested-components
    const DefaultValue = () => (
      <DefaultValueContainer>
        <span>
          {label && `${label}: `}
          <span title={selectedOption ? selectedOption.label : defaultValue}>
            {selectedOption ? selectedOption.label : defaultValue}
          </span>
        </span>
        <ButtonIconContainer>
          {disabled ? null : <DownButton iconStyle={{ color: 'grey' }} />}
        </ButtonIconContainer>
      </DefaultValueContainer>
    );

    const attachment = ({ ref }) => (
      <DropDownMenuList
        isMenuOpen={isMenuOpen}
        ref={ref}
        style={{
          width: `${this.getContainerWidth()}px`,
        }}
      >
        {options.length > 5 && !unsearchable && (
          <SearchBar onMouseUpCapture={e => this.onSearchBarMouseUp(e)}>
            <SearchBarInput
              ref={this.search}
              type="text"
              onChange={e => this.onSearchBarChange(e)}
              placeholder={searchPlaceholder || 'Search'}
              value={searchTerm}
            />
          </SearchBar>
        )}
        {options
          .map((option, i) => ({ ...option, __originalIndex: i }))
          .filter(
            option =>
              !searchTerm ||
              option.label.toLowerCase().includes(searchTerm.toLowerCase())
          )
          .map(option => (
            <DropDownMenuOption
              data-testid={`dropdownmenu-option-${label}`}
              key={`dropdownmenu-option-${label}-${option.__originalIndex}`}
              onClick={() => this.selectOptionByIndex(option.__originalIndex)}
            >
              <OptionTextColumn>
                <OptionLabel>
                  <DropdownOptionLabel
                    option={option}
                    selected={option.value === selectedItem}
                    title={option.label}
                  >
                    {option.label}
                  </DropdownOptionLabel>
                  {option.optionDisabled && renderOptionSubtextNode(option)}
                </OptionLabel>
                {option.description && <div>{option.description}</div>}
              </OptionTextColumn>
            </DropDownMenuOption>
          ))}
      </DropDownMenuList>
    );

    return (
      <Tether
        allowedPlacements={['bottom', 'top']}
        attachment={attachment}
        offset={{ y: 0 }}
        placement="bottom"
        visible={isMenuOpen}
        wrapped
      >
        <Button
          title={title}
          onClick={this.toggleDropDownMenu}
          forceHover={isMenuOpen}
          ref={this.container}
        >
          {((typeahead && !selectedItem) || (typeahead && isMenuOpen)) && (
            <SearchBar onMouseUpCapture={e => this.onSearchBarMouseUp(e)}>
              <TypeaheadInput
                ref={this.search}
                type="text"
                onChange={e => this.onSearchBarChange(e)}
                placeholder={searchPlaceholder || 'Search'}
                value={searchTerm}
              />
            </SearchBar>
          )}

          <DropDownMenuContainer ref={this.wrapper}>
            {typeahead && isMenuOpen ? null : <DefaultValue />}
          </DropDownMenuContainer>
        </Button>
      </Tether>
    );
  }
}

DropDownMenu.propTypes = {
  className: PropTypes.string,
  title: PropTypes.string,
  label: PropTypes.string,
  onSelect: PropTypes.func,
  onClick: PropTypes.func,
  options: PropTypes.array,
  renderOptionSubtextNode: PropTypes.func,
  selectedValue: PropTypes.any,
  defaultValue: PropTypes.string,
  disabled: PropTypes.bool,
  left: PropTypes.bool,
  actionable: PropTypes.bool,
  unsearchable: PropTypes.bool,
  typeahead: PropTypes.bool,
  searchPlaceholder: PropTypes.string,
};
