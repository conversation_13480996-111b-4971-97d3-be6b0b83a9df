import React from 'react';
import PropTypes from 'prop-types';
import ReactSimpleRange from 'react-simple-range';
import { labelStyle, colors } from '../helpers/styles/editor';

const ZoomSlider = ({ handleChange, zoom }) => {
  return (
    <div>
      <label style={labelStyle}>Zoom:</label>
      <ReactSimpleRange
        label
        sliderSize={6}
        trackColor={colors.informationalBlue}
        thumbColor={colors.informationalBlue}
        max={200}
        min={100}
        onChange={handleChange}
        value={Number(zoom)}
      />
    </div>
  );
};

ZoomSlider.propTypes = {
  handleChange: PropTypes.func,
  zoom: PropTypes.oneOfType([PropTypes.number, PropTypes.string]),
};

export default ZoomSlider;
