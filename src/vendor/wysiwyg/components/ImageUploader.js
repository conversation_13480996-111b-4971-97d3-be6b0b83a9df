import React, { useState } from 'react';
import PropTypes from 'prop-types';
import styled from 'styled-components';
import Dropzone from 'react-dropzone';
import { connect } from 'react-redux';
import { Map } from 'immutable';
import { useImagery } from '@appcues/imagery-kit';
import { Button } from 'ext/components/ui';
import { parseSvgFile } from '../helpers/imageUpload';

const ErrorButton = styled(Button)`
  color: rgb(215 80 80);
  border-color: rgb(215 80 80);
  margin-top: 8px;

  &:hover:not([disabled]) {
    color: rgba(219, 16, 16, 0.8);
    background-color: rgba(219, 16, 16, 0.1);
    border-color: rgba(219, 16, 16, 0.5);
  }
`;

export function ImageUploader({
  onError,
  onBeforeUpload,
  onUpload,
  children,
  disableClick,
  disableText,
  localState,
  preventDropOnDocument,
  style,
}) {
  const [isUploading, setIsUploading] = useState(false);
  const { uploadImage } = useImagery();

  const handleDrop = files => {
    if (files?.length > 0) {
      uploadImageFile(files[0]);
    }
  };

  const uploadImageFile = async originalFile => {
    setIsUploading(true);

    if (originalFile.size > 5 * 1024 * 1024) {
      setIsUploading(false);
      onError('File size exceeds 5MB limit');
      return;
    }

    onBeforeUpload && onBeforeUpload();

    const file = await parseSvgFile(originalFile);

    const data = new FormData();
    data.append('file', file);

    const image = await uploadImage(file);

    const urlWithoutProtocol = image.url.replace(/^https?:\/\//i, '//');
    image.url = urlWithoutProtocol;
    onUpload(image);

    onError(null);
    setIsUploading(false);
  };

  const { isUploading: localUploading, isError } = localState.toJS();

  const baseStyle = {
    border: 'none',
    flexGrow: 1,
    display: 'flex',
    flexDirection: 'column',
  };
  const combinedStyle = { ...baseStyle, ...style };

  const STATUS_STYLES = {
    error: {
      color: 'rgb(215 80 80)',
      backgroundColor: 'rgb(238 163 163)',
      fontWeight: 600,
    },
    uploading: {
      color: 'rgb(161 138 15)',
      backgroundColor: 'rgb(229 208 110)',
    },
  };

  const statusContainerStyle = (status = 'uploading') => ({
    ...combinedStyle,
    ...STATUS_STYLES[status],
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: 4,
  });

  if (isError && !disableText) {
    return (
      <div style={statusContainerStyle('error')}>
        {isError}
        <ErrorButton kind="tertiary" onClick={() => onError(null)}>
          Try again
        </ErrorButton>
      </div>
    );
  }

  return (localUploading || isUploading) && !disableText ? (
    <div style={statusContainerStyle('uploading')}>Uploading...</div>
  ) : (
    <Dropzone
      multiple={false}
      accept="image/*"
      style={combinedStyle}
      onDrop={handleDrop}
      disableClick={disableClick}
      preventDropOnDocument={preventDropOnDocument}
    >
      {children}
    </Dropzone>
  );
}

ImageUploader.propTypes = {
  onBeforeUpload: PropTypes.func,
  children: PropTypes.element,
  disableClick: PropTypes.bool,
  disableText: PropTypes.bool,
  localState: PropTypes.instanceOf(Map),
  onUpload: PropTypes.func,
  preventDropOnDocument: PropTypes.bool,
  style: PropTypes.object,
};

ImageUploader.defaultProps = {
  disableText: false,
};

function mapStateToProps(state) {
  return {
    localState: state.editor.get('localState'),
  };
}

export default connect(mapStateToProps)(ImageUploader);
