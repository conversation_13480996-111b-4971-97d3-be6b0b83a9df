import React, { useEffect, useRef, useState } from 'react';
import PropTypes from 'prop-types';
import styled from 'styled-components';
import { colors } from '../helpers/styles/editor';
import IconButton from '../icons/IconButton';
import { BOTTOM_BAR_HEIGHT } from './constants';

const Container = styled.div`
  -webkit-font-smoothing: antialiased;
  animation-duration: 0.15s;
  animation-fill-mode: both;
  animation-iteration-count: 1;
  animation-timing-function: ease-out;
  background-color: #ffffff;
  border-radius: 6px;
  box-shadow: 0px 12px 34px rgba(71, 88, 114, 0.2);
  color: rgb(128, 128, 128);
  padding: 16px 24px;
  position: absolute;
  width: 300px;
  z-index: 99;
`;

const ActionsMenu = ({
  children,
  className,
  isOpen = false,
  position = 'bottom',
  style: overrideStyles = {},
  role: ariaRole = 'listbox',
  'aria-label': ariaLabel,
  isCode = false,
  shouldHaveScrollBar = false,
}) => {
  const ref = useRef();
  const [hasRoomToRenderRight, setHasRoomToRenderRight] = useState(true);
  const [hasRoomToRenderBottom, setHasRoomToRenderBottom] = useState(true);
  const [scrollStyles, setScrollStyles] = useState({});

  /*
   * NOTE: Check whether to render this componenet to the left or right
   */
  useEffect(() => {
    if (isOpen) {
      const $menu = ref.current;
      if (!$menu) return;
      // 62px is the height of the fixed bottom bar
      // This means we only need scrolling if the bottom of the container is greater than the
      // window inner height and the fixed bottom bar.
      if (shouldHaveScrollBar) {
        const { bottom, top } = $menu.getBoundingClientRect();
        const maxHeight =
          bottom > window.innerHeight
            ? `${window.innerHeight - BOTTOM_BAR_HEIGHT - top}px`
            : '';
        setScrollStyles(
          window.innerHeight - BOTTOM_BAR_HEIGHT < bottom
            ? { overflowY: 'auto', maxHeight }
            : {}
        );
      }
      setHasRoomToRenderRight(
        window.innerWidth - $menu.parentElement.getBoundingClientRect().right >
          $menu.getBoundingClientRect().width
      );
    }
  }, [isOpen, shouldHaveScrollBar]);

  useEffect(() => {
    const $menu = ref.current;
    if (!$menu) return;

    setHasRoomToRenderBottom(
      window.innerHeight - $menu.parentElement.getBoundingClientRect().bottom >
        $menu.getBoundingClientRect().height
    );
  }, [ref]);

  const direction =
    position === 'bottom' && hasRoomToRenderBottom
      ? { top: '75px' }
      : { bottom: '45px' };
  const styles = {
    ...overrideStyles,
    ...direction,
    animationName: `editor-slide-${isOpen ? 'in' : 'out'}-${position}`,
    right: !hasRoomToRenderRight && '45px',
    left: isCode && hasRoomToRenderRight && `250px`,
    ...scrollStyles,
  };

  return (
    <Container
      style={styles}
      className={className}
      role={ariaRole}
      aria-label={ariaLabel}
      ref={ref}
    >
      {children}
    </Container>
  );
};

ActionsMenu.IconButton = styled(IconButton)``;

ActionsMenu.MenuItem = styled.li`
  color: ${({ selected }) => (selected ? colors.apc_purple : '#222')};
  background-color: transparent;
  cursor: pointer;
  transition: background-color 0.15s ease-out;
  list-style: none;

  &:first-of-type {
    border-top-right-radius: 12px;
    border-top-left-radius: 12px;
    margin-top: ${props => (props.isFontSizeMenuItem ? '10px' : '0px')};
  }
  &:last-of-type {
    border-bottom-right-radius: 12px;
    border-bottom-left-radius: 12px;
  }

  ${ActionsMenu.IconButton} {
    padding: 0;
    margin: 8px 4px;
    border-radius: 6px;
  }

  &:hover ${ActionsMenu.IconButton} {
    background-color: var(--text-color);
  }

  padding: ${props => (props.isFontSizeMenuItem ? '8px' : '0px')};
  text-align: ${props => (props.isFontSizeMenuItem ? 'center' : '')};
`;

ActionsMenu.propTypes = {
  children: PropTypes.node,
  className: PropTypes.string,
  isOpen: PropTypes.bool,
  position: PropTypes.oneOf(['top', 'bottom']),
  style: PropTypes.object,
  isCode: PropTypes.bool,
  shouldHaveScrollBar: PropTypes.bool,
};

export default ActionsMenu;
