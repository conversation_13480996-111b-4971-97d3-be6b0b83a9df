import React, { createRef } from 'react';
import PropTypes from 'prop-types';
import { List } from 'immutable';
import Tether from 'ext/components/Tether';
import AddButton from '../icons/AddButton';
import EditorSelector from './EditorSelector';

export default class AddButtonContainer extends React.Component {
  $target = createRef();

  render() {
    const {
      onSelectEditorType,
      shadow,
      onClick,
      internalAllowedEditorTypes,
      showEditorSelector,
      patternType,
    } = this.props;

    const attachment = ({ ref }) => (
      <EditorSelector
        allowedEditorTypes={internalAllowedEditorTypes}
        forwardRef={ref}
        onSelect={onSelectEditorType}
        patternType={patternType}
        showEditorSelector={showEditorSelector}
      />
    );

    return (
      <div style={{ pointerEvents: 'all' }} onClick={onClick}>
        <Tether
          attachment={attachment}
          offset={{ x: -50, y: 10 }}
          placement="bottom-right"
          visible={showEditorSelector}
          wrapped
        >
          <AddButton shadow={shadow} {...this.props} />
        </Tether>
      </div>
    );
  }
}

AddButtonContainer.propTypes = {
  shadow: PropTypes.bool,
  onSelectEditorType: PropTypes.func,
  internalAllowedEditorTypes: PropTypes.instanceOf(List),
  showEditorSelector: PropTypes.bool,
  onClick: PropTypes.func,
  patternType: PropTypes.string,
};

AddButtonContainer.defaultProps = {
  shadow: true,
  style: {},
  showEditorSelector: false,
};
