import React from 'react';
import styled from 'styled-components';
import PropTypes from 'prop-types';

const ToggleSwitch = styled.div`
  width: 100%;

  label {
    color: #394455;
    font-weight: var(--bold);
    font-size: var(--regular);
  }

  input {
    appearance: none;
    width: 32px;
    height: 16px;
    display: inline-block;
    position: relative;
    border-radius: 50px;
    overflow: hidden;
    outline: none;
    border: none;
    cursor: pointer;
    background-color: #d9dce6;
    transition: background-color ease 0.3s;
    float: right;
  }

  input:before {
    content: '';
    display: block;
    position: absolute;
    z-index: 2;
    width: 12px;
    height: 12px;
    background: #fff;
    left: 3px;
    top: 2px;
    border-radius: 50%;
    font-weight: var(--bold);
    text-indent: -22px;
    text-shadow: -1px -1px rgba(0, 0, 0, 0.15);
    white-space: nowrap;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
    transition: all cubic-bezier(0.3, 1.5, 0.7, 1) 0.3s;
  }

  input:checked {
    background-color: #dcf3ff;
  }

  input:checked:before {
    background: #2cb4ff;
    left: 16px;
  }
`;

const Toggle = ({ checked, onChange, label }) => {
  const id = label.replace(/ /g, '');

  return (
    <ToggleSwitch>
      <input id={id} type="checkbox" checked={checked} onChange={onChange} />
      <label htmlFor={id}>{label}</label>
    </ToggleSwitch>
  );
};

Toggle.propTypes = {
  checked: PropTypes.bool,
  onChange: PropTypes.func,
  label: PropTypes.string,
};

export default Toggle;
