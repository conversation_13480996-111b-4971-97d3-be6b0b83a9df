import React from 'react';
import PropTypes from 'prop-types';
import { Map } from 'immutable';

import { convertBoundingBox } from '../helpers/domHelpers';
import Zone from './Zone';
import { rowStyles } from '../helpers/styles/editor';

/**
 * A React component for each row of the editor
 * @class
 */
export default class Row extends React.Component {
  constructor(props) {
    super(props);

    this.state = {
      position: Map(),
    };
  }

  componentDidMount() {
    this.setBoundingBox();
  }

  setBoundingBox() {
    if (!this.wrapper) {
      return;
    }
    const position = convertBoundingBox(this.wrapper.getBoundingClientRect());
    if (!position.equals(this.state.position)) {
      this.setState({ position });
    }
  }

  render() {
    const {
      row,
      isOver,
      numPages,
      removeZone,
      insertZone,
      moveZone,
      setIsHoveringOverRowContainer,
      rowIndex,
      totalRows,
      theme,
      track,
      disabled,
      patternType,
    } = this.props;
    const { position } = this.state;

    const zoneNodes = row.get('zones').map((zone, i) => {
      return (
        <Zone
          setIsHoveringOverRowContainer={setIsHoveringOverRowContainer}
          numPages={numPages}
          key={zone.get('id')}
          zone={zone}
          removeZone={removeZone}
          insertZone={insertZone}
          moveZone={moveZone}
          row={row}
          rowIndex={rowIndex}
          totalRows={totalRows}
          isOver={isOver}
          rowPosition={position}
          columnIndex={i}
          theme={theme}
          track={track}
          disabled={disabled}
          patternType={patternType}
        />
      );
    });

    return (
      <div className="row" style={rowStyles} ref={el => (this.wrapper = el)}>
        {zoneNodes}
      </div>
    );
  }
}

Row.propTypes = {
  row: PropTypes.instanceOf(Map),
  isDragging: PropTypes.bool,
  isOver: PropTypes.bool,
  removeZone: PropTypes.func,
  insertZone: PropTypes.func,
  moveZone: PropTypes.func,
  numPages: PropTypes.number,
  setIsHoveringOverRowContainer: PropTypes.func,
  rowIndex: PropTypes.number,
  totalRows: PropTypes.number,
  theme: PropTypes.object,
  track: PropTypes.func,
  disabled: PropTypes.bool,
  patternType: PropTypes.string,
};
