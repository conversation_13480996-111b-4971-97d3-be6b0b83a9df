import React from 'react';
import PropTypes from 'prop-types';
import { connect } from 'react-redux';
import { Map, List } from 'immutable';

import * as editorActions from '../actions/editorActions';

const MENU_HEIGHT_ALLOWANCE = 300;

/**
 * A React component renders a grid of toolbar actions
 * such as Bold, Italic, etc.
 * @class
 */
export class Toolbar extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      hasRoomToRenderBelow: true,
    };
  }

  componentDidMount() {
    this.setHasRoomToRenderBelow();
  }

  componentDidUpdate() {
    this.setHasRoomToRenderBelow();
  }

  setHasRoomToRenderBelow() {
    const hasRoomToRenderBelow =
      window.innerHeight - this.toolbar.getBoundingClientRect().bottom >
      MENU_HEIGHT_ALLOWANCE;
    if (hasRoomToRenderBelow !== this.state.hasRoomToRenderBelow) {
      this.setState({ hasRoomToRenderBelow });
    }
  }

  render() {
    const {
      localState,
      persistedState,
      onChange,
      actions,
      selectedAction,
      dispatch,
      cloudinary,
      userProperties,
      shouldDisableXSS,
      sanitizeHtmlConfig,
      numPages,
      focusEditor,
      patternType,
      type,
    } = this.props;

    const { hasRoomToRenderBelow } = this.state;

    const vertLine = {
      width: 1,
      height: 32,
      borderLeft: '1px solid rgba(0, 0, 0, 0.1)',
    };
    const lineContainer = {
      paddingTop: 8,
      paddingBottom: 8,
      paddingLeft: 8,
      paddingRight: 8,
    };

    return (
      <div ref={el => (this.toolbar = el)} style={{ display: 'grid' }}>
        {actions.map((editorAction, index) => {
          if (editorAction.separator) {
            return (
              <div
                key={`separator-${index}`}
                style={{ gridColumn: index + 1, gridRow: 1 }}
              >
                <div style={lineContainer}>
                  <div style={vertLine} />
                </div>
              </div>
            );
          }

          const toolbarProps = {
            localState,
            persistedState,
            onChange,
            cloudinary,
            userProperties,
            sanitizeHtmlConfig,
            shouldDisableXSS,
            hasRoomToRenderBelow,
            focusEditor,
            patternType,
            ...(type && { type }),
            ...(numPages && { numPages }),
            isActive: selectedAction === editorAction.name,
            onToggleActive: isActive => {
              dispatch(
                editorActions.toggleEditorAction(editorAction.name, isActive)
              );
            },
          };

          return (
            <div
              key={editorAction.name}
              style={{ gridColumn: index + 1, gridRow: 1 }}
            >
              <editorAction.Component
                {...toolbarProps}
                {...editorAction.props}
              />
            </div>
          );
        })}
      </div>
    );
  }
}
Toolbar.propTypes = {
  dispatch: PropTypes.func,
  localState: PropTypes.instanceOf(Map),
  persistedState: PropTypes.instanceOf(Map),
  onChange: PropTypes.func,
  actions: PropTypes.array,
  cloudinary: PropTypes.instanceOf(Map),
  userProperties: PropTypes.instanceOf(List),
  selectedAction: PropTypes.string,
  sanitizeHtmlConfig: PropTypes.instanceOf(Map),
  shouldDisableXSS: PropTypes.bool,
  focusEditor: PropTypes.func,
  numPages: PropTypes.number,
};

function mapStateToProps(state) {
  return {
    selectedAction: state.editor.get('activeEditorAction'),
    cloudinary: state.editor.get('cloudinary'),
    userProperties: state.editor.get('userProperties'),
    sanitizeHtmlConfig: state.editor.get('sanitizeHtmlConfig'),
    shouldDisableXSS: state.editor.get('shouldDisableXSS'),
  };
}

export default connect(mapStateToProps)(Toolbar);
