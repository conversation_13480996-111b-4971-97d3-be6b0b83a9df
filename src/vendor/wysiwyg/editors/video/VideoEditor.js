import React from 'react';
import PropTypes from 'prop-types';
import { Map } from 'immutable';

import { placeholderStyle } from '../../helpers/styles/editor';
import { InteractableLayer, KeyCommand, MessageText, Wrapper } from './styled';

export default class VideoEditor extends React.Component {
  constructor(props) {
    super(props);

    this.state = {
      showInteractMessage: false,
      showInteractableArea: true,
    };

    this.onKeyDown = this.onKeyDown.bind(this);
    this.onKeyUp = this.onKeyUp.bind(this);
    this.onMouseEnter = this.onMouseEnter.bind(this);
    this.onMouseLeave = this.onMouseLeave.bind(this);
  }

  onKeyDown(e) {
    if (/shift/i.test(e.key)) {
      this.setState({ showInteractableArea: false });
    }
  }

  onKeyUp(e) {
    if (/shift/i.test(e.key)) {
      this.setState({ showInteractableArea: true });
    }
  }

  onMouseEnter() {
    this.setState({ showInteractMessage: true });
    parent.focus();
    document.addEventListener('keydown', this.onKeyDown);
    document.addEventListener('keyup', this.onKeyUp);
  }

  removeKeypressListeners() {
    document.removeEventListener('keydown', this.onKeyDown);
    document.removeEventListener('keyup', this.onKeyUp);
  }

  onMouseLeave() {
    this.setState({
      showInteractMessage: false,
      showInteractableArea: true,
    });

    this.removeKeypressListeners();
  }

  UNSAFE_componentWillMount() {
    const { persistedState, localState, onChange } = this.props;

    if (!persistedState.get('content')) {
      const newPersistedState = persistedState.set('content', '');
      const newLocalState = localState.set('content', '');
      onChange({
        persistedState: newPersistedState,
        localState: newLocalState,
        html: this.generateHTML(newPersistedState),
      });
    }
  }

  componentWillUnmount() {
    this.removeKeypressListeners();
  }

  // Instance Method
  focus() {
    // Do nothing for this editor
  }

  generateHTML(persistedState) {
    const content = persistedState.get('content') || '';

    return `<div class="video-html">${content}</div>`;
  }

  render() {
    const { persistedState } = this.props;
    const { showInteractableArea, showInteractMessage } = this.state;

    const content = persistedState.get('content');

    return content ? (
      <div
        onMouseEnter={this.onMouseEnter}
        onMouseLeave={this.onMouseLeave}
        style={{ minHeight: 100 }}
      >
        {showInteractMessage && (
          <Wrapper fadeText={showInteractableArea}>
            <MessageText>
              {showInteractableArea && (
                <>
                  <KeyCommand>Hold Shift</KeyCommand> and{' '}
                </>
              )}
              <KeyCommand>{showInteractableArea ? 'c' : 'C'}lick</KeyCommand> to
              edit the video
            </MessageText>
          </Wrapper>
        )}
        {!showInteractableArea && (
          <InteractableLayer
            onClick={() => this.setState({ showInteractableArea: false })}
          />
        )}
        <div
          className="video-html"
          onMouseLeave={() => parent.focus()}
          style={{ minHeight: 100, textAlign: 'center' }}
          dangerouslySetInnerHTML={{ __html: content }}
        />
      </div>
    ) : (
      <div style={placeholderStyle}>Add your Video Script</div>
    );
  }
}

VideoEditor.propTypes = {
  isEditing: PropTypes.bool,
  onChange: PropTypes.func,
  persistedState: PropTypes.instanceOf(Map),
  localState: PropTypes.instanceOf(Map),
};
