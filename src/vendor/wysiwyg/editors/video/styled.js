import styled from 'styled-components';
import { Text } from 'ext/components/ui';

export const Wrapper = styled.div`
  background-color: var(--background-dark);
  border-radius: 6px;
  box-shadow: 0 0 12px rgba(0, 0, 0, 0.25);
  color: var(--white);
  left: 50%;
  opacity: 0.95;
  margin: 24px auto;
  min-width: 320px;
  padding: 8px 32px;
  position: absolute;
  text-align: center;
  transform: translate(-50%);
  width: auto;
  z-index: 999999;

  &:hover {
    opacity: ${({ fadeText }) => (fadeText ? 0.3 : 0.95)};
  }
`;

export const MessageText = styled(Text)`
  color: var(--white);
`;

export const InteractableLayer = styled.div`
  background-color: rgba(0, 0, 0, 0.3);
  backdrop-filter: blur(10px);
  height: 100%;
  position: absolute;
  width: 100%;
  z-index: 99999;
`;

export const KeyCommand = styled.span`
  font-weight: var(--bold);
  color: var(--primary-light);
`;
