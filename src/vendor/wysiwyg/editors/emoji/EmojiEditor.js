import React from 'react';
import PropTypes from 'prop-types';
import { Map } from 'immutable';

import HTMLParser from 'html-parse-stringify2';
import { placeholderStyle } from '../../helpers/styles/editor';
import { OLD_SVG_URL, BASE_SVG_URL } from '../../helpers/emoji';

function replaceEmojiCDN(url) {
  if (url) {
    return url.replace(OLD_SVG_URL, BASE_SVG_URL);
  }
  return url;
}

export default class ImageEditor extends React.Component {
  componentDidUpdate(prevProps) {
    // Update the emoji CDN when the user clicks into the emoji block
    const { isEditing: wasEditing } = prevProps;
    const { isEditing, localState, persistedState, onChange } = this.props;
    if (!wasEditing && isEditing) {
      const oldURL = persistedState.get('url');
      const newURL = replaceEmojiCDN(persistedState.get('url'));
      if (newURL !== oldURL) {
        const newPersistedState = persistedState.set('url', newURL);
        onChange({
          localState,
          persistedState: newPersistedState,
          html: this.generateHTML(newPersistedState),
        });
      }
    }
  }

  generateHTML(persistedState) {
    const {
      url,
      height,
      width,
      heightOverride,
      widthOverride,
      href,
      isNewWindow,
      textAlign,
      marginTop,
      marginRight,
      marginBottom,
      marginLeft,
      altText,
    } = persistedState.toJS();

    if (!url) {
      return '';
    }

    const wrapperAttrs = {
      class: 'image',
    };

    if (textAlign) {
      wrapperAttrs.style = `text-align:${textAlign};`;
    }

    if (marginTop) {
      wrapperAttrs.style += `margin-top:${marginTop}px;`;
    }
    if (marginRight) {
      wrapperAttrs.style += `margin-right:${marginRight}px;`;
    }
    if (marginBottom) {
      wrapperAttrs.style += `margin-bottom:${marginBottom}px;`;
    }
    if (marginLeft) {
      wrapperAttrs.style += `margin-left:${marginLeft}px;`;
    }

    const imageAttrs = { style: '' };
    if (height || heightOverride) {
      imageAttrs.style = `${imageAttrs.style} height: ${
        heightOverride || height
      }px;`;
    }
    if (width || widthOverride) {
      imageAttrs.style = `${imageAttrs.style} width: ${
        widthOverride || width
      }px`;
    }
    if (altText) {
      imageAttrs.alt = `${altText}`;
    }

    const imageAst = {
      type: 'tag',
      name: 'img',
      voidElement: true,
      attrs: {
        src: replaceEmojiCDN(url),
        ...imageAttrs,
      },
    };

    const linkAst = {
      type: 'tag',
      name: 'a',
      voidElement: false,
      attrs: {
        href,
        target: isNewWindow ? '_blank' : '_self',
      },
      children: [imageAst],
    };

    const ast = [
      {
        type: 'tag',
        name: 'div',
        attrs: wrapperAttrs,
        voidElement: false,
        children: [href ? linkAst : imageAst],
      },
    ];

    return HTMLParser.stringify(ast);
  }

  // Instance Method
  focus() {
    // Do nothing for this editor
  }

  handleUpload(imageDetails) {
    const { url, width } = imageDetails;
    const { localState, persistedState, onChange, canvasPosition } = this.props;

    let newPersistedState = persistedState
      .set('url', url)
      .set('width', width)
      .set('textAlign', 'center');

    // Make sure the uploaded image does not have a larger size than the canvas
    if (width > canvasPosition.get('width')) {
      newPersistedState = newPersistedState.set(
        'widthOverride',
        canvasPosition.get('width')
      );
    }

    onChange({
      localState,
      persistedState: newPersistedState,
      html: this.generateHTML(newPersistedState),
    });
  }

  render() {
    const { persistedState, isEditing, onClickEmptyState } = this.props;
    const {
      url,
      height,
      width,
      heightOverride,
      widthOverride,
      textAlign,
      marginTop,
      marginRight,
      marginBottom,
      marginLeft,
    } = persistedState.toJS();

    const dropzoneStyle = {
      paddingBottom: 30,
      paddingTop: 30,
      backgroundColor: '#dafeea',
      color: '#0bdc66',
      textAlign: 'center',
      borderRadius: 4,
      cursor: 'pointer',
      marginLeft: -23,
      width: 'calc(100% + 46px)',
    };

    const wrapperStyle = {};
    if (textAlign) {
      wrapperStyle.textAlign = textAlign;
    }
    if (marginTop) {
      wrapperStyle.marginTop = marginTop;
    }
    if (marginRight) {
      wrapperStyle.marginRight = marginRight;
    }
    if (marginBottom) {
      wrapperStyle.marginBottom = marginBottom;
    }
    if (marginLeft) {
      wrapperStyle.marginLeft = marginLeft;
    }

    let node = (
      <div style={{ height: 100, ...placeholderStyle }}>
        Click to add your Image
      </div>
    );
    if (url) {
      node = (
        <img
          alt="Uploaded"
          src={replaceEmojiCDN(url)}
          style={{
            height: heightOverride || height,
            width: widthOverride || width,
          }}
        />
      );
    } else if (isEditing) {
      node = (
        <div style={dropzoneStyle} onClick={onClickEmptyState}>
          <div>
            <div>Click here to select an emoji.</div>
          </div>
        </div>
      );
    }

    return (
      <div className="image" style={wrapperStyle}>
        {node}
      </div>
    );
  }
}

ImageEditor.propTypes = {
  isEditing: PropTypes.bool,
  onChange: PropTypes.func,
  persistedState: PropTypes.instanceOf(Map),
  localState: PropTypes.instanceOf(Map),
  canvasPosition: PropTypes.instanceOf(Map),
  onClickEmptyState: PropTypes.func,
};
