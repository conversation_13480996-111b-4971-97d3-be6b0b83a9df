import React from 'react';
import PropTypes from 'prop-types';
import { Map } from 'immutable';

import Menu from '../../components/Menu';
import Toolbar from '../../components/Toolbar';
import EmojiSelector from '../../editor-actions/EmojiSelector';
import Alignment from '../../editor-actions/Alignment';
import ImageSize from '../../editor-actions/ImageSize';
import Margin from '../../editor-actions/Margin';
import { toolbarStyle } from '../../helpers/styles/editor';
import { handleNonTextAlignment } from '../../helpers/draft/actions';

export default function EmojiToolbar(props) {
  const { height: maxHeight, width: maxWidth } = props.canvasPosition.toJS();

  const actions = [
    {
      Component: EmojiSelector,
      props: {
        maxWidth,
        maxHeight,
      },
      name: 'emoji-selector',
    },
    {
      Component: ImageSize,
      name: 'image-size',
    },
    {
      Component: Alignment,
      name: 'alignment',
      props: {
        onChange: ({ textAlign, localState, persistedState }) =>
          props.onChange({
            localState,
            persistedState: handleNonTextAlignment(textAlign, persistedState),
          }),
      },
    },
    {
      Component: Margin,
      name: 'margin',
    },
  ];

  return (
    <Menu style={toolbarStyle}>
      <Toolbar actions={actions} {...props} />
    </Menu>
  );
}

EmojiToolbar.propTypes = {
  localState: PropTypes.instanceOf(Map),
  persistedState: PropTypes.instanceOf(Map),
  onChange: PropTypes.func,
  canvasPosition: PropTypes.instanceOf(Map),
};
