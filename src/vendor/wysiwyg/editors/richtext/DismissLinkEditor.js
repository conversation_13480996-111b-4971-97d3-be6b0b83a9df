import React from 'react';
import { Editor, RichUtils } from 'draft-js';

import RichTextEditor, { richTextEditorCSS } from './RichTextEditor';
import { customStyleFn, blockStyleFn } from '../../helpers/draft/convert';
import { placeholderStyle } from '../../helpers/styles/editor';
import { keyBindingFn, handleKeyCommand } from '../../key-bindings';
import {
  generateHTML,
  parseAriaLabel,
  parseContent,
} from '../../helpers/dismissLink';

export default class DismissLinkEditor extends RichTextEditor {
  parseContent = parseContent;

  generateHTML = generateHTML;

  render() {
    const { isEditing, persistedState, localState } = this.props;
    const { marginTop, marginRight, marginBottom, marginLeft } =
      persistedState.toJS();
    const editorState = localState.get('editorState');
    const content = persistedState.get('content') || '';
    const ariaLabel = parseAriaLabel(content);

    const wrapperStyle = { zIndex: 483647 };
    if (marginTop) {
      wrapperStyle.marginTop = marginTop;
    }
    if (marginRight) {
      wrapperStyle.marginRight = marginRight;
    }
    if (marginBottom) {
      wrapperStyle.marginBottom = marginBottom;
    }
    if (marginLeft) {
      wrapperStyle.marginLeft = marginLeft;
    }

    return (
      <div className="dismiss-link" ref={this.wrapper} style={wrapperStyle}>
        <style>{richTextEditorCSS}</style>

        {isEditing ? (
          editorState ? (
            <a
              className="text-muted appcues-skip"
              rel="noreferrer"
              data-step="skip"
              role="button"
              tabIndex="0"
            >
              <Editor
                ref={this.editor}
                blockStyleFn={blockStyleFn}
                customStyleFn={customStyleFn}
                editorState={editorState}
                handleKeyCommand={(command, editorState) =>
                  handleKeyCommand(
                    command,
                    editorState,
                    this.handleEditorStateChange
                  )
                }
                handlePastedText={(text, html, editorState) =>
                  this.handlePastedText(text, html, editorState)
                }
                handleReturn={e => {
                  if (e.shiftKey) {
                    this.handleEditorStateChange(
                      RichUtils.insertSoftNewline(editorState)
                    );
                    return 'handled';
                  }
                  return 'not-handled';
                }}
                keyBindingFn={keyBindingFn}
                onChange={this.handleEditorStateChange}
                placeholder="Start typing"
                preserveSelectionOnBlur
                spellCheck
              />
            </a>
          ) : null
        ) : content && content !== '<p></p>' ? (
          <a
            className="text-muted appcues-skip"
            rel="noreferrer"
            data-step="skip"
            role="button"
            tabIndex="0"
            aria-label={ariaLabel}
            onClickCapture={this.handleClickCapture}
            dangerouslySetInnerHTML={{
              __html: parseContent(content),
            }}
          />
        ) : (
          <a className="text-muted appcues-skip" style={placeholderStyle}>
            Click to add your text
          </a>
        )}
      </div>
    );
  }
}
