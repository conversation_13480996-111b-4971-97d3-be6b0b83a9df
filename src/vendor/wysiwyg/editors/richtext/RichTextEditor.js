import React, { createRef } from 'react';
import PropTypes from 'prop-types';
import { Map } from 'immutable';
import { Editor, EditorState, RichUtils, Modifier } from 'draft-js';
import { getPortalRoot } from 'ext/lib/document';
import {
  decorator,
  convertFromHTML,
  convertFromPastedHTML,
  convertToHTML,
  customStyleFn,
  blockStyleFn,
} from '../../helpers/draft/convert';
import { placeholderStyle } from '../../helpers/styles/editor';
import { keyBindingFn, handleKeyCommand } from '../../key-bindings';

/**
 * NOTE: We're making the .public-DraftEditor-content area larger so that mouse
 *       events are less likely to occur outside of it. This makes selection of
 *       text very difficult if the mouseup occurs outside. (There's probably a
 *       TODO here about solutions to the selection issues). We also need to
 *       .DraftEditor-root area bigger to match, so this doesn't cause
 *       positioning issues with the rich text placeholder.
 *
 *       Additionally, one caveat to adding this mouseup buffer is that the
 *       floating toolbar will render slightly further away from the editing
 *       zone. It's not too jarring but it would be great to investigate whether
 *       we can bring these closer together without affecting other toolbars.
 */
export const richTextEditorCSS = `
  /**
   * Override this aggressive default style to ensure that when editing, the
   * inline styles take precedence.
   */
  appcues cue * {
    color: inherit;
  }

  .rich-text strong {
    color: inherit !important;
  }

  .rich-text .line-height-0 {
    line-height: 0 !important;
  }

  .rich-text .line-height-0_5 {
    line-height: 0.5 !important;
  }

  .rich-text .line-height-1 {
    line-height: 1 !important;
  }

  .rich-text .line-height-1_5 {
    line-height: 1.5 !important;
  }

  .rich-text .line-height-2 {
    line-height: 2 !important;
  }

  .rich-text .DraftEditor-root,
  .rich-text .public-DraftEditor-content {
    margin: -16px -64px;
    padding: 16px 64px;
  }
`;

export default class RichTextEditor extends React.Component {
  wrapper = createRef();

  editor = createRef();

  UNSAFE_componentWillMount() {
    const { persistedState } = this.props;
    const htmlContent = persistedState.get('content');
    const initialEditorState = htmlContent
      ? EditorState.createWithContent(convertFromHTML(htmlContent), decorator)
      : EditorState.createEmpty(decorator);
    this.handleEditorStateChange(initialEditorState);
  }

  componentDidMount() {
    const { current: $wrapper } = this.wrapper;
    if ($wrapper) {
      $wrapper.addEventListener('mouseup', this.onMouseUp, true);
    }
    window.addEventListener('mouseup', this.onMouseUp, true);
  }

  UNSAFE_componentWillReceiveProps(nextProps) {
    const { persistedState } = this.props;

    const htmlContent = persistedState.get('content');

    if (nextProps.isEditing && nextProps.localState.isEmpty()) {
      // If there is no editorState, create a new blank one
      const initialEditorState = htmlContent
        ? EditorState.createWithContent(convertFromHTML(htmlContent), decorator)
        : EditorState.createEmpty(decorator);
      this.handleEditorStateChange(initialEditorState);
    } else if (nextProps.isEditing) {
      // If editorState changes from the toolbar, push any changes up the chain
      const oldEditorState = this.props.localState.get('editorState');
      const newEditorState = nextProps.localState.get('editorState');

      if (oldEditorState !== newEditorState) {
        this.handleEditorStateChange(newEditorState);
      }
    }
  }

  componentWillUnmount() {
    const { current: $wrapper } = this.wrapper;
    if ($wrapper) {
      $wrapper.removeEventListener('mouseup', this.onMouseUp, true);
    }
    window.removeEventListener('mouseup', this.onMouseUp, true);
  }

  onMouseUp = e => {
    const { isEditing } = this.props;

    if (isEditing) {
      // Because the event target can be nested within shadow elements and
      // iframes, 'event.composedPath()' is used to ensure we extract the actual
      // element and not the boundary of a shadow element or iframe
      const [$target] = e.composedPath();

      const { current: $wrapper } = this.wrapper;
      const { current: $editor } = this.editor;

      // Get the portal root which is where the toolbar is rendered
      const $portal = getPortalRoot();

      // If mouse up happened over a Toolbar element, don't reset editor focus.
      const [$toolbar] = $portal
        ? $portal.querySelectorAll('[data-wysiwyg="toolbar"]')
        : [];

      if ($toolbar && $toolbar.contains($target)) {
        return;
      }

      if ($editor && !$wrapper.contains($target)) {
        $editor.blur();
        e.preventDefault();
      }
    }
  };

  // Instance Method
  focus() {
    const { current: $editor } = this.editor;
    if ($editor) {
      $editor.focus();
    }
  }

  handlePastedText(text, html, editorState) {
    const { persistedState, localState, onChange } = this.props;

    // Regex search for HTML tags within html clipboard content
    const containsHTML = /<[a-z][\S\s]*>/i.test(html);

    if (containsHTML) {
      const newContent = convertFromPastedHTML(html).getBlockMap();
      const newContentState = Modifier.replaceWithFragment(
        editorState.getCurrentContent(),
        editorState.getSelection(),
        newContent
      );

      const newEditorState = EditorState.push(editorState, newContentState);
      const newLocalState = localState.set('editorState', newEditorState);
      const newPersistedState = persistedState.set('content', html);

      onChange({
        persistedState: newPersistedState,
        localState: newLocalState,
        html: this.generateHTML(newPersistedState),
      });
      return true;
    }
    return false;
  }

  handleEditorStateChange = editorState => {
    const { persistedState, localState } = this.props;

    const htmlContent = convertToHTML(editorState);

    const newPersistedState = persistedState.set('content', htmlContent);
    const newLocalState = localState.set('editorState', editorState);

    this.props.onChange({
      persistedState: newPersistedState,
      localState: newLocalState,
      html: this.generateHTML(newPersistedState),
    });
  };

  generateHTML(persistedState) {
    const height = persistedState.get('height');
    const width = persistedState.get('width');
    const content = persistedState.get('content') || '';

    const { marginTop, marginRight, marginBottom, marginLeft } =
      persistedState.toJS();

    let styles = '';
    if (height) {
      styles += `height:${height};`;
    }
    if (width) {
      styles += `width:${width};`;
    }

    if (marginTop) {
      styles += `margin-top:${marginTop}px;`;
    }
    if (marginRight) {
      styles += `margin-right:${marginRight}px;`;
    }
    if (marginBottom) {
      styles += `margin-bottom:${marginBottom}px;`;
    }
    if (marginLeft) {
      styles += `margin-left:${marginLeft}px;`;
    }

    const stylesTag = styles && styles.length ? ` style="${styles}"` : '';

    const styleOverride = `<style>.rich-text strong{color:inherit !important;}</style>`;
    const html = `<div class="rich-text"${stylesTag}>${styleOverride}<div>${content}</div></div>`;
    return html;
  }

  handleClickCapture = e => {
    // NOTE: If a hyperlink is clicked while not in edit mode, prevent the link
    //       from being followed since it will throw due to cross-origin frame
    //       errors. It's possible we may need to include other tags or even
    //       prevent default for all clicks, but we can determine that if we
    //       encounter an example where that needs to be handled
    if (e.target.tagName === 'A') {
      e.preventDefault();
    }
  };

  render() {
    const { isEditing, persistedState, localState } = this.props;
    const { marginTop, marginRight, marginBottom, marginLeft } =
      persistedState.toJS();
    const editorState = localState.get('editorState');

    const content = persistedState.get('content') || '';

    const wrapperStyle = { zIndex: 483647 };
    if (marginTop) {
      wrapperStyle.marginTop = marginTop;
    }
    if (marginRight) {
      wrapperStyle.marginRight = marginRight;
    }
    if (marginBottom) {
      wrapperStyle.marginBottom = marginBottom;
    }
    if (marginLeft) {
      wrapperStyle.marginLeft = marginLeft;
    }

    return (
      <div className="rich-text" ref={this.wrapper} style={wrapperStyle}>
        <style>{richTextEditorCSS}</style>

        {isEditing ? (
          editorState ? (
            <Editor
              ref={this.editor}
              blockStyleFn={blockStyleFn}
              customStyleFn={customStyleFn}
              editorState={editorState}
              handleKeyCommand={(command, editorState) =>
                handleKeyCommand(
                  command,
                  editorState,
                  this.handleEditorStateChange
                )
              }
              handlePastedText={(text, html, editorState) =>
                this.handlePastedText(text, html, editorState)
              }
              handleReturn={e => {
                if (e.shiftKey) {
                  this.handleEditorStateChange(
                    RichUtils.insertSoftNewline(editorState)
                  );
                  return 'handled';
                }
                return 'not-handled';
              }}
              keyBindingFn={keyBindingFn}
              onChange={this.handleEditorStateChange}
              placeholder="Start typing"
              preserveSelectionOnBlur
              spellCheck
            />
          ) : null
        ) : content && content !== '<p></p>' ? (
          <div
            onClickCapture={this.handleClickCapture}
            dangerouslySetInnerHTML={{
              __html: content,
            }}
          />
        ) : (
          <div style={placeholderStyle}>Click to add your text</div>
        )}
      </div>
    );
  }
}

RichTextEditor.propTypes = {
  isEditing: PropTypes.bool,
  onChange: PropTypes.func,
  persistedState: PropTypes.instanceOf(Map),
  localState: PropTypes.instanceOf(Map),
};
