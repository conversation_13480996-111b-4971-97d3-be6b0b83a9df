import React from 'react';
import PropTypes from 'prop-types';
import { Map } from 'immutable';

import Menu from '../../components/Menu';
import Toolbar from '../../components/Toolbar';
import EmojiSelector from '../../editor-actions/EmojiSelector';
import Alignment from '../../editor-actions/Alignment';
import Bold from '../../editor-actions/Bold';
import Italic from '../../editor-actions/Italic';
import Underline from '../../editor-actions/Underline';
import FontColor from '../../editor-actions/FontColor';
import FontSize from '../../editor-actions/FontSize';
import TextStyle from '../../editor-actions/TextStyle';
import Font from '../../editor-actions/Font';
import HyperlinkInline from '../../editor-actions/HyperlinkInline';
import List from '../../editor-actions/List';
import UserProperty from '../../editor-actions/UserProperty';
import Margin from '../../editor-actions/Margin';
import { toolbarStyle } from '../../helpers/styles/editor';
import { handleTextAlignment } from '../../helpers/draft/actions';

export default function RichTextToolbar(props) {
  const editor = props.localState.get('editorState');
  const currentFocus = editor ? editor.getSelection().getFocusKey() : null;
  const inlineStyle = editor
    ? editor.getCurrentInlineStyle(currentFocus)
    : null;
  const currentContent = editor ? editor.getCurrentContent() : null;
  const currentContentBlock = currentContent
    ? currentContent.getBlockForKey(currentFocus)
    : null;
  const blockData = currentContentBlock
    ? currentContentBlock.getData().toJSON()
    : null;
  const blockType = currentContentBlock ? currentContentBlock.getType() : null;
  const { height: maxHeight, width: maxWidth } = props.canvasPosition.toJS();

  const actions = props.filterActions([
    {
      Component: TextStyle,
      name: 'text-style',
    },
    {
      Component: Font,
      name: 'font',
      props: {
        blockData,
      },
    },
    {
      Component: FontSize,
      name: 'font-size',
    },
    {
      Component: FontColor,
      name: 'font-color',
    },
    {
      Component: Bold,
      name: 'bold',
    },
    {
      Component: Italic,
      name: 'italic',
      props: {
        inlineStyle,
      },
    },
    {
      Component: Underline,
      name: 'underline',
      props: {
        inlineStyle,
      },
    },
    {
      Component: Alignment,
      name: 'alignment',
      props: {
        blockData,
        onChange: ({ textAlign, localState, persistedState }) =>
          props.onChange({
            persistedState,
            localState: handleTextAlignment(textAlign, localState, blockData),
          }),
      },
    },
    {
      Component: List,
      name: 'list',
      props: {
        blockType,
      },
    },
    {
      Component: EmojiSelector,
      name: 'emoji-selector',
      props: {
        maxWidth,
        maxHeight,
        isTextToolbar: true,
      },
    },
    {
      Component: UserProperty,
      name: 'user-property',
    },
    {
      Component: HyperlinkInline,
      name: HyperlinkInline.actionName,
    },
    {
      Component: Margin,
      name: 'margin',
    },
  ]);

  return (
    <Menu style={toolbarStyle}>
      <Toolbar actions={actions} {...props} />
    </Menu>
  );
}

RichTextToolbar.propTypes = {
  localState: PropTypes.instanceOf(Map),
  persistedState: PropTypes.instanceOf(Map),
  onChange: PropTypes.func,
  filterActions: PropTypes.func,
};

RichTextToolbar.defaultProps = {
  filterActions: actions => actions,
};
