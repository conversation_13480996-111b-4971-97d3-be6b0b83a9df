import React from 'react';
import PropTypes from 'prop-types';
import { Map } from 'immutable';

import Menu from '../../components/Menu';
import Toolbar from '../../components/Toolbar';
import InputFieldOptions from '../../editor-actions/InputFieldOptions';
import Margin from '../../editor-actions/Margin';
import { toolbarStyle } from '../../helpers/styles/editor';

const actions = [
  {
    Component: InputFieldOptions,
    name: 'inputfield-options',
  },
  {
    Component: Margin,
    name: 'margin',
  },
];

export default function TextInputToolbar(props) {
  return (
    <Menu style={toolbarStyle}>
      <Toolbar actions={actions} {...props} />
    </Menu>
  );
}

TextInputToolbar.propTypes = {
  localState: PropTypes.instanceOf(Map),
  persistedState: PropTypes.instanceOf(Map),
  onChange: PropTypes.func,
};
