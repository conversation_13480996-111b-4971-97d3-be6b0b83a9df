import React from 'react';
import PropTypes from 'prop-types';
import { Map } from 'immutable';

import Menu from '../../components/Menu';
import Toolbar from '../../components/Toolbar';
import Alignment from '../../editor-actions/Alignment';
import ImageSize from '../../editor-actions/ImageSize';
import ImageGallery from '../../editor-actions/ImageGallery';
import ImageSelector from '../../editor-actions/ImageSelector';
import ImageUpload from '../../editor-actions/ImageUpload';
import Margin from '../../editor-actions/Margin';
import ImageOnClick from '../../editor-actions/ImageOnClick';
import Accessibility from '../../editor-actions/Accessibility';
import { toolbarStyle } from '../../helpers/styles/editor';
import { handleNonTextAlignment } from '../../helpers/draft/actions';

export default function ImageToolbar(props) {
  const { width: maxWidth } = props.canvasPosition.toJS();
  const actions = [
    {
      Component: ImageSelector,
      props: {
        maxWidth,
        onChange: props.onChange,
        options: [
          {
            Component: ImageUpload,
            props: {
              maxWidth,
              onChange: props.onChange,
              localState: props.localState,
              persistedState: props.persistedState,
            },
          },
          {
            Component: ImageGallery,
            props: {
              maxWidth,
              onChange: props.onChange,
              localState: props.localState,
              persistedState: props.persistedState,
            },
          },
        ],
      },
      name: 'image-selector',
    },
    {
      Component: ImageSize,
      name: 'image-size',
    },
    {
      Component: Alignment,
      name: 'alignment',
      props: {
        onChange: ({ textAlign, localState, persistedState }) =>
          props.onChange({
            localState,
            persistedState: handleNonTextAlignment(textAlign, persistedState),
          }),
      },
    },
    {
      Component: ImageOnClick,
      name: 'image-onclick',
    },
    {
      Component: Margin,
      name: 'margin',
    },
    {
      Component: Accessibility,
      name: 'accessibility-button',
    },
  ];

  return (
    <Menu style={toolbarStyle}>
      <Toolbar actions={actions} {...props} />
    </Menu>
  );
}

ImageToolbar.propTypes = {
  localState: PropTypes.instanceOf(Map),
  persistedState: PropTypes.instanceOf(Map),
  onChange: PropTypes.func,
  canvasPosition: PropTypes.instanceOf(Map),
};
