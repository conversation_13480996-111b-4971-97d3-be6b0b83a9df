import React from 'react';
import PropTypes from 'prop-types';
import { Map } from 'immutable';

import HTMLParser from 'html-parse-stringify2';
import { placeholderStyle } from '../../helpers/styles/editor';
import ImageUploader from '../../components/ImageUploader';
import { BUTTON_ACTION_TYPES } from '../../helpers/constants';

export default class ImageEditor extends React.Component {
  generateHTML(persistedState) {
    const {
      url,
      height,
      width,
      heightOverride,
      widthOverride,
      href,
      isNewWindow,
      textAlign,
      marginTop,
      marginRight,
      marginBottom,
      marginLeft,
      flowId,
      altText,
    } = persistedState.toJS();

    if (!url) {
      return '';
    }

    const wrapperAttrs = {
      class: 'image',
    };

    if (textAlign) {
      wrapperAttrs.style = `text-align:${textAlign};`;
    }

    if (marginTop) {
      wrapperAttrs.style += `margin-top:${marginTop}px;`;
    }
    if (marginRight) {
      wrapperAttrs.style += `margin-right:${marginRight}px;`;
    }
    if (marginBottom) {
      wrapperAttrs.style += `margin-bottom:${marginBottom}px;`;
    }
    if (marginLeft) {
      wrapperAttrs.style += `margin-left:${marginLeft}px;`;
    }

    const imageAttrs = { style: '' };
    if (height || heightOverride) {
      imageAttrs.style = `${imageAttrs.style} height: ${
        heightOverride || height
      }px;`;
    }
    if (width || widthOverride) {
      imageAttrs.style = `${imageAttrs.style} width: ${
        widthOverride || width
      }px`;
    }

    if (altText) {
      imageAttrs.alt = `${altText}`;
    } else {
      imageAttrs.role = 'presentation';
      imageAttrs.alt = '';
    }

    const imageAst = {
      type: 'tag',
      name: 'img',
      voidElement: true,
      attrs: {
        src: url,
        ...imageAttrs,
      },
    };

    const linkAst = {
      type: 'tag',
      name: 'a',
      voidElement: false,
      attrs: {},
      children: [imageAst],
    };

    if (href) {
      linkAst.attrs.href = href;
      linkAst.attrs.target = isNewWindow ? '_blank' : '_parent';
    }
    if (flowId) {
      linkAst.attrs.onclick = `window.parent.Appcues.show('${flowId}')`;
      linkAst.attrs['data-step'] = BUTTON_ACTION_TYPES.END_STEP_AND_FLOW;
    }

    const ast = [
      {
        type: 'tag',
        name: 'div',
        attrs: wrapperAttrs,
        voidElement: false,
        children: [href || flowId ? linkAst : imageAst],
      },
    ];

    return HTMLParser.stringify(ast);
  }

  // Instance Method
  focus() {
    // Do nothing for this editor
  }

  handleUpload(imageDetails) {
    const { url, width } = imageDetails;
    const { localState, persistedState, onChange, canvasPosition } = this.props;

    const urlWithoutProtocol = url.replace(/^https?:\/\//i, '//');
    let newPersistedState = persistedState
      .set('url', urlWithoutProtocol)
      .set('width', width)
      .set('textAlign', 'center');

    // Make sure the uploaded image does not have a larger size than the canvas
    if (width > canvasPosition.get('width')) {
      newPersistedState = newPersistedState.set(
        'widthOverride',
        canvasPosition.get('width')
      );
    }

    onChange({
      localState,
      persistedState: newPersistedState,
      html: this.generateHTML(newPersistedState),
    });
  }

  handleError = error => {
    const { localState, persistedState, onChange } = this.props;
    const newLocalState = localState.set('isError', error);

    onChange({
      localState: newLocalState,
      persistedState,
    });
  };

  render() {
    const { localState, persistedState, isEditing } = this.props;
    const {
      url,
      height,
      width,
      heightOverride,
      widthOverride,
      textAlign,
      marginTop,
      marginRight,
      marginBottom,
      marginLeft,
      altText,
    } = persistedState.toJS();

    const { isUploading } = localState.toJS();

    const dropzoneStyle = {
      paddingBottom: 30,
      paddingTop: 30,
      backgroundColor: '#dafeea',
      color: '#0bdc66',
      textAlign: 'center',
      borderRadius: 4,
      cursor: 'pointer',
      marginLeft: -23,
      width: 'calc(100% + 46px)',
    };

    const wrapperStyle = {};
    if (textAlign) {
      wrapperStyle.textAlign = textAlign;
    }
    if (marginTop) {
      wrapperStyle.marginTop = marginTop;
    }
    if (marginRight) {
      wrapperStyle.marginRight = marginRight;
    }
    if (marginBottom) {
      wrapperStyle.marginBottom = marginBottom;
    }
    if (marginLeft) {
      wrapperStyle.marginLeft = marginLeft;
    }

    let node = (
      <div style={{ height: 100, ...placeholderStyle }}>
        Click to add your Image
      </div>
    );
    if (url && !isUploading) {
      node = (
        <img
          alt={altText ?? ''}
          // This is just at edit mode to make sure it will show img if there is an
          // alt text.
          // in publish mode, if alt text exist then we don't add any role, otherwise
          // we add as presentation.
          // check line 68.
          role={altText ? 'img' : 'presentation'}
          src={url}
          style={{
            height: heightOverride || height,
            width: widthOverride || width,
          }}
          crossOrigin="anonymous"
        />
      );
    } else if (isEditing) {
      node = (
        <ImageUploader
          onError={this.handleError}
          onUpload={imageDetails => this.handleUpload(imageDetails)}
          style={dropzoneStyle}
        >
          <p style={{ color: 'rgb(11, 220, 102)', margin: 0 }}>
            Click here to select an image to upload
            <br />
            or drag and drop an image
          </p>
        </ImageUploader>
      );
    }

    return (
      <div className="image" style={wrapperStyle}>
        {node}
      </div>
    );
  }
}

ImageEditor.propTypes = {
  isEditing: PropTypes.bool,
  onChange: PropTypes.func,
  persistedState: PropTypes.instanceOf(Map),
  localState: PropTypes.instanceOf(Map),
  canvasPosition: PropTypes.instanceOf(Map),
};
