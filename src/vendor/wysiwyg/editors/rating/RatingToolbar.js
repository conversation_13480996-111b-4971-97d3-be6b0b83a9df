import React from 'react';
import PropTypes from 'prop-types';
import { Map } from 'immutable';

import Menu from '../../components/Menu';
import Toolbar from '../../components/Toolbar';

import RatingOptions from '../../editor-actions/RatingOptions';
import Margin from '../../editor-actions/Margin';
import { toolbarStyle } from '../../helpers/styles/editor';

const actions = [
  {
    Component: RatingOptions,
    name: 'rating-options',
  },
  {
    Component: Margin,
    name: 'margin',
  },
];

export default function RatingToolbar(props) {
  return (
    <Menu style={toolbarStyle}>
      <Toolbar actions={actions} {...props} />
    </Menu>
  );
}

RatingToolbar.propTypes = {
  localState: PropTypes.instanceOf(Map),
  persistedState: PropTypes.instanceOf(Map),
  onChange: PropTypes.func,
};
