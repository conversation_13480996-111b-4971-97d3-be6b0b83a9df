import React from 'react';
import PropTypes from 'prop-types';
import { Map } from 'immutable';

import Menu from '../../components/Menu';
import Toolbar from '../../components/Toolbar';
import Code from '../../editor-actions/Code';

const actions = [
  {
    Component: Code,
    props: {
      title: 'Enter your custom html below',
      overrideSanitizeHtmlConfig: {
        allowedTags: false,
        allowedAttributes: false,
        exclusiveFilter: frame => frame.tag.toLowerCase() === 'script',
        parser: {
          lowerCaseAttributeNames: false,
        },
      },
    },
    name: 'code',
  },
];

export default function HtmlEditorToolbar(props) {
  return (
    <Menu>
      <Toolbar actions={actions} {...props} />
    </Menu>
  );
}

HtmlEditorToolbar.propTypes = {
  localState: PropTypes.instanceOf(Map),
  persistedState: PropTypes.instanceOf(Map),
  onChange: PropTypes.func,
};
