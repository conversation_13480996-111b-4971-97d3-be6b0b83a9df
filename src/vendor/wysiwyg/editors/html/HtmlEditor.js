import React from 'react';
import PropTypes from 'prop-types';
import { Map } from 'immutable';

import { placeholderStyle } from '../../helpers/styles/editor';

export default class HtmlEditor extends React.Component {
  UNSAFE_componentWillMount() {
    const { persistedState, localState, onChange } = this.props;

    if (!persistedState.get('content')) {
      const newPersistedState = persistedState.set('content', '');
      const newLocalState = localState.set('content', '');
      onChange({
        persistedState: newPersistedState,
        localState: newLocalState,
        html: this.generateHTML(newPersistedState),
      });
    }
  }

  // Instance Method
  focus() {
    // Do nothing for this editor
  }

  generateHTML(persistedState) {
    const content = persistedState.get('content') || '';

    return `<div class="html">${content}</div>`;
  }

  render() {
    const { persistedState } = this.props;

    const content = persistedState.get('content');

    return content ? (
      <div className="html" dangerouslySetInnerHTML={{ __html: content }} />
    ) : (
      <div style={placeholderStyle}>Add your HTML</div>
    );
  }
}

HtmlEditor.propTypes = {
  isEditing: PropTypes.bool,
  onChange: PropTypes.func,
  persistedState: PropTypes.instanceOf(Map),
  localState: PropTypes.instanceOf(Map),
};
