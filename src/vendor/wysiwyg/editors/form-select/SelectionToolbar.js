import React from 'react';
import PropTypes from 'prop-types';
import { Map } from 'immutable';

import Menu from '../../components/Menu';
import Toolbar from '../../components/Toolbar';

import SelectionFieldOptions from '../../editor-actions/SelectionFieldOptions';
import Margin from '../../editor-actions/Margin';
import { toolbarStyle } from '../../helpers/styles/editor';

const actions = [
  {
    Component: SelectionFieldOptions,
    name: 'selectionfield-options',
  },
  {
    Component: Margin,
    name: 'margin',
  },
];

export default function SelectionToolbar(props) {
  return (
    <Menu style={toolbarStyle}>
      <Toolbar actions={actions} {...props} />
    </Menu>
  );
}

SelectionToolbar.propTypes = {
  localState: PropTypes.instanceOf(Map),
  persistedState: PropTypes.instanceOf(Map),
  onChange: PropTypes.func,
};
