import React from 'react';
import PropTypes from 'prop-types';
import { Map } from 'immutable';

import Menu from '../../components/Menu';
import Toolbar from '../../components/Toolbar';

import ImageGallery from '../../editor-actions/ImageGallery';
import ImageSelector from '../../editor-actions/ImageSelector';
import ImageSize from '../../editor-actions/ImageSize';
import ImageUpload from '../../editor-actions/ImageUpload';
import FontColor from '../../editor-actions/FontColor';
import TextStyle from '../../editor-actions/TextStyle';

import { toolbarStyle } from '../../helpers/styles/editor';

export default function HeroToolbar(props) {
  const { width: maxWidth } = props.canvasPosition.toJS();
  const actions = [
    {
      Component: ImageSelector,
      props: {
        maxWidth,
        onChange: props.onChange,
        options: [
          {
            Component: ImageUpload,
            props: {
              maxWidth,
              onChange: props.onChange,
              localState: props.localState,
              persistedState: props.persistedState,
              isHero: true,
            },
          },
          {
            Component: ImageGallery,
            props: {
              maxWidth,
              onChange: props.onChange,
              localState: props.localState,
              persistedState: props.persistedState,
              isHero: true,
            },
          },
        ],
      },
      name: 'image-selector',
    },
    {
      Component: ImageSize,
      props: {
        heroImage: true,
      },
      name: 'image-size',
    },
    {
      Component: FontColor,
      name: 'font-color',
    },
    {
      Component: TextStyle,
      name: 'text-style',
    },
  ];

  return (
    <Menu style={toolbarStyle}>
      <Toolbar actions={actions} {...props} />
    </Menu>
  );
}

HeroToolbar.propTypes = {
  localState: PropTypes.instanceOf(Map),
  persistedState: PropTypes.instanceOf(Map),
  onChange: PropTypes.func,
  canvasPosition: PropTypes.instanceOf(Map),
};
