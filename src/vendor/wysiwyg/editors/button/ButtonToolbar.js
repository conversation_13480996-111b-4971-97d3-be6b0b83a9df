import React from 'react';
import PropTypes from 'prop-types';
import { Map } from 'immutable';

import Menu from '../../components/Menu';
import Toolbar from '../../components/Toolbar';

import Alignment from '../../editor-actions/Alignment';
import ButtonType from '../../editor-actions/ButtonType';
import ButtonAction from '../../editor-actions/ButtonAction';
import { toolbarStyle } from '../../helpers/styles/editor';
import { handleNonTextAlignment } from '../../helpers/draft/actions';
import ButtonAlignment from '../../icons/ButtonAlignment';

import FontColor from '../../editor-actions/Buttons/FontColor';
import EmojiSelector from '../../editor-actions/Buttons/EmojiSelector';
import Font from '../../editor-actions/Buttons/Font';
import FontSize from '../../editor-actions/Buttons/FontSize';
import Bold from '../../editor-actions/Buttons/Bold';
import Italic from '../../editor-actions/Buttons/Italic';
import Underline from '../../editor-actions/Buttons/Underline';
import ButtonMarginPaddingRadius from '../../editor-actions/ButtonMarginPaddingRadius';
import ButtonContainerStyles from '../../editor-actions/Buttons/Container/ButtonContainerStyles';

export default function ButtonToolbar(props) {
  const actions = [
    {
      Component: ButtonType,
      name: 'button-type',
      props: {
        blockType: props.persistedState.get('buttonType'),
        track: props.track,
      },
    },
    {
      Component: Alignment,
      name: 'alignment',
      props: {
        AlignmentIcon: ButtonAlignment,
        defaultTextAlign: 'center',
        onChange: ({ textAlign, localState, persistedState }) =>
          props.onChange({
            localState,
            persistedState: handleNonTextAlignment(textAlign, persistedState),
          }),
        customTrack: () =>
          props.track('Builder interaction', {
            name: 'Button - Updated Alignment',
            component: 'ButtonToolbar',
          }),
      },
    },
    {
      Component: ButtonContainerStyles,
      name: 'container-styles',
      props: {
        track: props.track,
        zone: props.zone,
      },
    },
    {
      Component: ButtonMarginPaddingRadius,
      name: 'margin-padding-radius-styles',
      props: {
        track: props.track,
      },
    },
    {
      separator: true,
    },
    {
      Component: Font,
      name: 'font',
      props: {
        track: props.track,
      },
    },
    {
      Component: FontSize,
      name: 'font-size',
      props: {
        track: props.track,
      },
    },
    {
      Component: FontColor,
      name: 'font-color',
      props: {
        track: props.track,
      },
    },
    {
      Component: Bold,
      name: 'bold',
      props: {
        track: props.track,
      },
    },
    {
      Component: Italic,
      name: 'italic',
      props: {
        track: props.track,
      },
    },
    {
      Component: Underline,
      name: 'underline',
      props: {
        track: props.track,
      },
    },
    {
      Component: EmojiSelector,
      name: 'emoji-selector',
      props: {
        track: props.track,
      },
    },
    {
      separator: true,
    },
    {
      Component: ButtonAction,
      name: 'button-action',
      props: {
        track: props.track,
        patternType: props.patternType,
        buttonActionType: props.persistedState.get('buttonActionType'),
      },
    },
  ];

  return (
    <Menu style={toolbarStyle}>
      <Toolbar actions={actions} {...props} />
    </Menu>
  );
}

ButtonToolbar.propTypes = {
  localState: PropTypes.instanceOf(Map),
  persistedState: PropTypes.instanceOf(Map),
  onChange: PropTypes.func,
  track: PropTypes.func,
  patternType: PropTypes.string,
  zone: PropTypes.instanceOf(Map),
};
