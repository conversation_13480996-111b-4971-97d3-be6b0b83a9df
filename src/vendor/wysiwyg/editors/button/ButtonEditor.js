import React from 'react';
import PropTypes from 'prop-types';
import { Map } from 'immutable';
import { EditorState } from 'draft-js';
import HTMLParser from 'html-parse-stringify2';
import {
  decorator,
  convertFromHTML,
  convertToHTML,
} from '../../helpers/draft/convert';
import {
  getInlineStyles,
  getConditionalValue,
  getDropShadowString,
} from '../../helpers/styles/editor';
import {
  getButtonColor,
  isButtonPrimary,
  getTextColor,
} from '../../helpers/themes';
import {
  BUTTON_ACTION_TYPES,
  PATTERN_TYPES,
  DEFAULT_BUTTON_TEXT,
  DEFAULT_HOTSPOT_BUTTON_TEXT,
} from '../../helpers/constants';
import ContentEditable from '../../components/ContentEditable';
import { getCursorIndex } from '../../helpers/cursor';
import {
  getDefaultPadding,
  getDataStep,
  generateButtonAST,
  getShadowAxis,
  getCustomHoverStyle,
} from '../../helpers/buttons';

const getDefaultButtonLabel = patternType =>
  patternType === PATTERN_TYPES.HOTSPOT_GROUP
    ? DEFAULT_HOTSPOT_BUTTON_TEXT
    : DEFAULT_BUTTON_TEXT;

export default class ButtonEditor extends React.Component {
  contentEditableRef = React.createRef();

  lastCursorIndex = null;

  isStartingIndexSelected = false;

  componentDidMount() {
    window.addEventListener('onAddEmoji', this.insertEmoji);
  }

  componentWillUnmount() {
    window.removeEventListener('onAddEmoji', this.insertEmoji);
  }

  UNSAFE_componentWillMount() {
    const { persistedState, localState, onChange, patternType } = this.props;
    const defaultText = getDefaultButtonLabel(patternType);

    const content = persistedState.get('buttonText') || defaultText;

    const initialEditorState = EditorState.createWithContent(
      convertFromHTML(content),
      decorator
    );
    this.handleEditorStateChange(initialEditorState);

    const shouldUseNextAsDefaultAction =
      patternType !== PATTERN_TYPES.HOTSPOT_GROUP;
    const defaultAction = shouldUseNextAsDefaultAction
      ? BUTTON_ACTION_TYPES.NEXT_PAGE
      : BUTTON_ACTION_TYPES.URL;

    const defaultTextAlign = 'center';

    const marginTop = persistedState.get('marginTop');
    const marginBottom = persistedState.get('marginBottom');
    const buttonActionType =
      persistedState.get('buttonActionType') || defaultAction;
    const fontSize = persistedState.get('fontSize') || 'inherit';
    const textAlign = persistedState.get('textAlign') ?? defaultTextAlign;
    const fontColor = persistedState.get('fontColor') || undefined;
    const isBold = persistedState.get('isBold') || undefined;
    const isItalic = persistedState.get('isItalic') || undefined;
    const isUnderlined = persistedState.get('isUnderlined') || undefined;
    const fontFamily = persistedState.get('fontFamily') || undefined;
    const lineHeight = persistedState.get('lineHeight') || undefined;
    const letterSpacing = persistedState.get('letterSpacing') || undefined;
    const textTransform = persistedState.get('textTransform') || undefined;

    const isMarginTopSet = marginTop || marginTop === 0;
    const isMarginBottomSet = marginBottom || marginBottom === 0;

    const newPersistedState = persistedState
      .set('marginTop', isMarginTopSet ? marginTop : 5)
      .set('marginBottom', isMarginBottomSet ? marginBottom : 5)
      .set('buttonActionType', buttonActionType)
      .set('fontSize', fontSize)
      .set('textAlign', textAlign)
      .set('buttonText', content)
      .set('fontColor', fontColor)
      .set('isBold', isBold)
      .set('isItalic', isItalic)
      .set('isUnderlined', isUnderlined)
      .set('fontFamily', fontFamily)
      .set('lineHeight', lineHeight)
      .set('letterSpacing', letterSpacing)
      .set('textTransform', textTransform);

    onChange({
      localState,
      persistedState: newPersistedState,
    });
  }

  shouldComponentUpdate(nextProps) {
    const { localState } = this.props;
    const hasButtonTextChanged =
      localState.get('buttonText') !== nextProps.localState.get('buttonText');

    if (hasButtonTextChanged) {
      return false;
    }

    return true;
  }

  onButtonBlur = () => {
    /**
     * we want to default the character (eg emojis) insertion to the end of the button text
     * unless the user explicitly specifies the position they want to insert in
     * by clicking that position.
     */
    const buttonTextLength = this.props.persistedState.get('buttonText').length;
    const shouldInsertAtEnd =
      getCursorIndex(this.contentEditableRef.current) === 0 &&
      !this.isStartingIndexSelected;
    this.lastCursorIndex = shouldInsertAtEnd
      ? buttonTextLength
      : getCursorIndex(this.contentEditableRef.current);
  };

  insertEmoji = ({ detail: { emoji } }) => {
    const { persistedState, localState } = this.props;
    const buttonText = persistedState.get('buttonText');
    // we should keep track of the last index
    // as clicking in the emoji menu we lost focus
    // which makes the next click always insert at the index zero
    // keeping this value we make sure we always insert in the intended index
    if (this.lastCursorIndex === null) {
      this.lastCursorIndex = buttonText.length;
    }

    const newPersistedState = persistedState.set(
      'buttonText',
      `${buttonText.slice(0, this.lastCursorIndex)}${emoji}${buttonText.slice(
        this.lastCursorIndex
      )}`
    );

    this.props.onChange({
      localState,
      persistedState: newPersistedState,
    });
    this.lastCursorIndex += emoji.length;
  };

  onButtonClick = () => {
    this.lastCursorIndex = getCursorIndex(this.contentEditableRef.current);
    this.isStartingIndexSelected = this.lastCursorIndex === 0;
  };

  // Instance Method
  focus() {}

  onTextChange = text => {
    const { persistedState, localState, onChange } = this.props;
    const newPersistedState = persistedState.set('buttonText', text);
    const newLocalState = localState.set('buttonText', text);

    onChange({
      persistedState: newPersistedState,
      localState: newLocalState,
      html: this.generateHTML(newPersistedState),
    });
  };

  handleEditorStateChange(editorState) {
    const { localState, onChange, persistedState } = this.props;
    const htmlContent = convertToHTML(editorState);
    const newLocalState = localState.set('editorState', editorState);

    const newPersistedState = persistedState.set('content', htmlContent);

    onChange({
      persistedState: newPersistedState,
      localState: newLocalState,
      html: this.generateHTML(newPersistedState),
    });
  }

  generateHTML(persistedState) {
    const { zone, theme, patternType } = this.props;
    const ast = generateButtonAST({
      persistedState,
      theme,
      zoneId: zone.get('id'),
      patternType,
    });

    return HTMLParser.stringify(ast);
  }

  render() {
    const { isEditing, persistedState, zone, theme, patternType, localState } =
      this.props;
    const defaultText = getDefaultButtonLabel(patternType);
    const buttonText = persistedState.get('buttonText') || defaultText;

    const {
      textAlign,
      className,
      marginTop,
      marginRight,
      marginBottom,
      marginLeft,
      padding,
      inlineBorderColor,
      inlineBorderWidth,
      inlineBackgroundColor,
      inlineDropShadow,
      hoverBackgroundColor,
      hoverDropShadow,
      hoverBorderColor,
      hoverBorderWidth,
      inputHoverLock,
    } = persistedState.toJS();

    const dropShadow = inlineDropShadow && JSON.parse(inlineDropShadow);
    const shadowAxis = getShadowAxis(dropShadow);

    const buttonStyleProps = [
      'borderRadius',
      'width',
      'fontSize',
      'buttonType',
    ];
    const classNameString =
      className && className.length ? ` ${className}` : '';

    const isMarginTopSet = marginTop || marginTop === 0;
    const isMarginBottomSet = marginBottom || marginBottom === 0;

    const containerStyle = {};
    containerStyle.textAlign = textAlign || 'center';
    containerStyle.marginTop = isMarginTopSet ? marginTop : 5;
    containerStyle.marginBottom = isMarginBottomSet ? marginBottom : 5;

    const defaultFontSize =
      patternType === PATTERN_TYPES.HOTSPOT_GROUP ||
      patternType === PATTERN_TYPES.TOOLTIP_GROUP
        ? 12
        : 14;

    containerStyle.fontSize = `${defaultFontSize}px`;

    if (marginLeft) {
      containerStyle.marginLeft = marginLeft;
    }

    containerStyle.width = '100%';

    if (marginRight) {
      containerStyle.width = `calc(100% - ${marginRight}px)`;
    }

    const buttonType = persistedState.get('buttonType');
    const isPrimary = isButtonPrimary(buttonType);
    const buttonBorder = `${inlineBorderWidth}px solid ${inlineBorderColor}`;
    const buttonColor =
      inlineBackgroundColor ?? getButtonColor(buttonType, theme);
    const textColor = getTextColor(buttonType, theme);

    const buttonStyle = {};
    buttonStyleProps.forEach(key => {
      if (key === 'buttonType' && theme) {
        if (textColor) {
          buttonStyle.color = `${textColor}`;
        }

        if (buttonColor) {
          buttonStyle.backgroundColor = `${buttonColor}`;
        }

        if (inlineBorderWidth && inlineBorderColor) {
          buttonStyle.border = `${buttonBorder}`;
        }
      } else if (persistedState.get(key)) {
        buttonStyle[key] = `${persistedState.get(key)}px`;
      }
    });

    const { defaultHorizontalPadding, defaultVerticalPadding } =
      getDefaultPadding(patternType);

    // padding here is used to keep padding backwards compatible since
    // in the past users were able to set only one value for padding
    // setting the padding values (top, right, bottom, left)
    // with the same value.
    const defaultXPadding = padding ?? defaultHorizontalPadding;
    const defaultYPadding = padding ?? defaultVerticalPadding;

    const inlineStyles = getInlineStyles({
      color: persistedState.get('fontColor'),
      fontWeight: getConditionalValue({
        value: persistedState.get('isBold'),
        whenTrue: 'bold',
        whenFalse: 'normal',
      }),
      fontStyle: getConditionalValue({
        value: persistedState.get('isItalic'),
        whenTrue: 'italic',
        whenFalse: 'normal',
      }),
      textDecoration: getConditionalValue({
        value: persistedState.get('isUnderlined'),
        whenTrue: 'underline',
      }),
      fontFamily: persistedState.get('fontFamily'),
      lineHeight: persistedState.get('lineHeight'),
      letterSpacing: persistedState.get('letterSpacing'),
      textTransform: persistedState.get('textTransform'),
      paddingTop: getConditionalValue({
        value: persistedState.get('paddingTop'),
        whenTrue: `${persistedState.get('paddingTop')}px`,
        otherwise: `${defaultYPadding}px`,
      }),
      paddingRight: getConditionalValue({
        value: persistedState.get('paddingRight'),
        whenTrue: `${persistedState.get('paddingRight')}px`,
        otherwise: `${defaultXPadding}px`,
      }),
      paddingBottom: getConditionalValue({
        value: persistedState.get('paddingBottom'),
        whenTrue: `${persistedState.get('paddingBottom')}px`,
        otherwise: `${defaultYPadding}px`,
      }),
      paddingLeft: getConditionalValue({
        value: persistedState.get('paddingLeft'),
        whenTrue: `${persistedState.get('paddingLeft')}px`,
        otherwise: `${defaultXPadding}px`,
      }),
      boxShadow: getConditionalValue({
        value: dropShadow,
        whenTrue: getDropShadowString(shadowAxis, dropShadow),
        otherwise: '',
      }),
    });

    const updatedButtonStyle = {
      ...buttonStyle,
      ...inlineStyles,
      textAlign: 'center',
    };

    const dataStep = getDataStep(persistedState);

    return (
      <div
        className={`button-wrapper ${
          isPrimary ? 'appcues-actions-right' : 'appcues-actions-left'
        }`}
        style={containerStyle}
      >
        {(hoverBackgroundColor ||
          hoverDropShadow ||
          hoverBorderWidth ||
          hoverBorderColor) && (
          <style>
            {getCustomHoverStyle({
              id: zone.get('id'),
              hoverBackgroundColor,
              hoverDropShadow,
              editingZoneId: localState.get('editingZoneId'),
              hoverBorderColor,
              hoverBorderWidth,
              inputHoverLock,
            })}
          </style>
        )}
        {isEditing ? (
          <a
            className={`appcues-button appcues-button-success ${classNameString}`}
            id={`button-${zone.get('id')}`}
            style={updatedButtonStyle}
            data-field-id={zone.get('id')}
            data-step={dataStep}
          >
            <ContentEditable
              ref={this.contentEditableRef}
              onTextChange={this.onTextChange}
              onBlur={this.onButtonBlur}
              defaultValue={buttonText}
              onClick={this.onButtonClick}
            />
          </a>
        ) : (
          <a
            className={`appcues-button appcues-button-success ${classNameString}`}
            id={`button-${zone.get('id')}`}
            style={updatedButtonStyle}
            disabled
            data-field-id={zone.get('id')}
            data-step={dataStep}
          >
            {buttonText}
          </a>
        )}
      </div>
    );
  }
}

ButtonEditor.propTypes = {
  isEditing: PropTypes.bool,
  onChange: PropTypes.func,
  persistedState: PropTypes.instanceOf(Map),
  localState: PropTypes.instanceOf(Map),
  zone: PropTypes.instanceOf(Map),
  theme: PropTypes.object,
};
