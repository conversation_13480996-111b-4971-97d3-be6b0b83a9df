import Actions from '../helpers/actionConstants';

export function updateZoneHtml(id, html) {
  return {
    type: Actions.ZONES_UPDATE_HTML,
    id,
    html,
  };
}

export function removeZone(id) {
  return {
    type: Actions.ZONES_REMOVE_ONE,
    id,
  };
}

export function moveZone({ zoneId, rowId, columnIndex }) {
  return {
    type: Actions.ZONE_MOVED,
    zoneId,
    rowId,
    columnIndex,
  };
}

export function moveZoneToNewRow({ zoneId, rowIndex }) {
  return {
    type: Actions.ZONE_MOVED_TO_NEW_ROW,
    zoneId,
    rowIndex,
  };
}
