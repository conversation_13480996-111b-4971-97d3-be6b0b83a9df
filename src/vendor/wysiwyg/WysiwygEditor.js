import React, { useEffect, useState } from 'react';
import PropTypes from 'prop-types';
import { applyMiddleware, createStore } from 'redux';
import { Provider, useSelector } from 'react-redux';
import thunkMiddleware from 'redux-thunk';
import { DndProvider } from 'react-dnd';
import { HTML5Backend } from 'react-dnd-html5-backend';
import { fromJS } from 'immutable';
import isEqual from 'lodash.isequal';
import getReduxConfig from 'ext/lib/redux-config';
import usePrevious from 'lib/hooks/use-previous';
import { flowShape } from 'entities/flows';
import { selectIsFirstStep } from 'lib/selectors';

import Canvas from './components/Canvas';
import { addFlows } from './actions/flowActions';
import { replaceRows } from './actions/rowActions';
import { reducer } from './reducers/root';
import StepContext from './step-context';

const middlewares = [thunkMiddleware];
const { composeEnhancers, logStateChange } = getReduxConfig();

const WysiwygEditor = props => {
  const { stepChild, flows, flow, rows } = props;
  const isFirstStep = useSelector(selectIsFirstStep);
  const [store, setStore] = useState(null);
  const previous = usePrevious(stepChild);
  const prevRows = usePrevious(rows);

  useEffect(() => {
    // FIXME: Sigh... current builder recreates a new store for each WYSIWYG
    // instance...
    const store = createStore(
      reducer,
      composeEnhancers(applyMiddleware(...middlewares))
    );
    setStore(store);
    logStateChange(store, 'Wysiwyg');
  }, [stepChild]);

  useEffect(() => {
    if (store === null) return;

    // forcing it to the next event loop cycle
    // to avoid some test issues regarding redux
    // as well calling it as an async action
    // as we receive the flows via props and it arrives empty in the first render
    // when refreshing the page with a flow opened,
    // we can't initialize the store again if it was initialized already.
    // it makes the editor loses its content.
    setTimeout(() => {
      store.dispatch(addFlows(flows, flow));
    }, 0);
  }, [flows, flow, store]);

  useEffect(() => {
    if (store === null) return;

    if (prevRows && !isEqual(prevRows, rows)) {
      store.dispatch(replaceRows(fromJS(rows)));
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [store, rows, flow]);

  // FIXME: Sigh part II... need to force an unmount of `Canvas` in order for
  //        all of the `componentDidMount` to retrigger...
  if (store === null || previous !== stepChild) {
    return null;
  }

  return (
    <div className="wysiwyg-container">
      {/* eslint-disable-next-line react/jsx-no-constructed-context-values */}
      <StepContext.Provider value={{ isFirstStep }}>
        <Provider store={store}>
          <DndProvider backend={HTML5Backend} context={props.window || window}>
            <Canvas {...props} />
          </DndProvider>
        </Provider>
      </StepContext.Provider>
    </div>
  );
};

WysiwygEditor.propTypes = {
  allowedEditorTypes: PropTypes.array,
  basePadding: PropTypes.number,
  closeAll: PropTypes.bool,
  cloudinary: PropTypes.shape({
    accountId: PropTypes.string,
    userId: PropTypes.string,
    // NOTE: Cloudinary values are now directly used in the `ImageUploader`, but
    //       kept this `cloudinary` prop to pass the `accountId` and `userId`
    // uploadUrl: PropTypes.string,
    // apiKey: PropTypes.string,
  }),
  disableAddButton: PropTypes.bool,
  disabled: PropTypes.bool,
  height: PropTypes.string,
  isHoveringOverContainer: PropTypes.bool,
  maxRows: PropTypes.number,
  onEditEnd: PropTypes.func,
  onEditStart: PropTypes.func,
  onEditorMenuClose: PropTypes.func,
  onEditorMenuOpen: PropTypes.func,
  onSave: PropTypes.func,
  patternType: PropTypes.string,
  resetShouldCloseMenu: PropTypes.func,
  rows: PropTypes.array,
  shouldCloseMenu: PropTypes.bool,
  shouldDisableXSS: PropTypes.bool,
  startEditable: PropTypes.bool,
  stepChild: PropTypes.string,
  userProperties: PropTypes.arrayOf(
    PropTypes.shape({
      name: PropTypes.string,
      value: PropTypes.string,
    })
  ),
  width: PropTypes.number,
  window: PropTypes.object,
  flows: PropTypes.objectOf(
    PropTypes.shape({
      id: PropTypes.string,
      name: PropTypes.string,
    })
  ),
  flow: flowShape,
};

export default WysiwygEditor;
