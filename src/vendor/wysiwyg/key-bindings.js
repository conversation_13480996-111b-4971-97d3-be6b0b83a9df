import { getDefaultKeyBinding } from 'draft-js';
import { applyBoldToEditorState } from './editor-actions/Bold';
import { applyUnderlineToEditorState } from './editor-actions/Underline';
import { applyItalicToEditorState } from './editor-actions/Italic';
import { applyTextStyleToEditorState } from './editor-actions/TextStyle';
import { TEXT_STYLE_TYPE } from './helpers/constants';

export function handleKeyCommand(command, editorState, onEditorStateChange) {
  switch (command) {
    case 'bold':
      onEditorStateChange(applyBoldToEditorState(editorState));
      break;

    case 'underline':
      onEditorStateChange(applyUnderlineToEditorState(editorState));
      break;

    case 'italic':
      onEditorStateChange(applyItalicToEditorState(editorState));
      break;

    case TEXT_STYLE_TYPE.UNSTYLED:
    case TEXT_STYLE_TYPE.HEADER_ONE:
    case TEXT_STYLE_TYPE.HEADER_TWO:
    case TEXT_STYLE_TYPE.HEADER_THREE:
    case TEXT_STYLE_TYPE.HEADER_FOUR:
    case TEXT_STYLE_TYPE.HEADER_FIVE:
      onEditorStateChange(applyTextStyleToEditorState(editorState, command));
      break;

    default:
      return 'not-handled';
  }
  return 'handled';
}

function hasControlShiftModifier(e) {
  return !!e.shiftKey && !!e.ctrlKey;
}

export function keyBindingFn(e) {
  switch (e.nativeEvent.code) {
    case 'Digit0':
      return hasControlShiftModifier(e) ? TEXT_STYLE_TYPE.UNSTYLED : null;

    case 'Digit1':
      return hasControlShiftModifier(e) ? TEXT_STYLE_TYPE.HEADER_ONE : null;

    case 'Digit2':
      return hasControlShiftModifier(e) ? TEXT_STYLE_TYPE.HEADER_TWO : null;

    case 'Digit3':
      return hasControlShiftModifier(e) ? TEXT_STYLE_TYPE.HEADER_THREE : null;

    case 'Digit4':
      return hasControlShiftModifier(e) ? TEXT_STYLE_TYPE.HEADER_FOUR : null;

    case 'Digit5':
      return hasControlShiftModifier(e) ? TEXT_STYLE_TYPE.HEADER_FIVE : null;

    default:
      return getDefaultKeyBinding(e);
  }
}
