export const getUserPropertyType = userPropertyOptions => {
  if (userPropertyOptions.length === 0) return 'text';

  return userPropertyOptions
    .map(option => {
      if (!isNaN(Number(option.name))) return 'number';
      if (option.name === 'true' || option.name === 'false') return 'checkbox';
      return 'text';
    })
    .reduce((finalType, currentType) => {
      if (!finalType || finalType === currentType) return currentType;
      return 'text';
    });
};
