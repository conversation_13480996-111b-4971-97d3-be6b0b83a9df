import {
  disabledActionsByPatternsType,
  BUTTON_ACTION_TYPES_LIST,
} from './constants';

export function isTimestamp(value) {
  let val = value;

  if (/^([+-])?(\d+|Infinity)$/.test(val)) {
    val = Number(val);
  }

  switch (typeof val) {
    case 'string':
      return !isNaN(new Date(Date.parse(val)).getTime());
    case 'number':
      // Verify the number is most likely a timestamp in
      // seconds/milliseconds (i.e. has 8 or more digits).
      return String(val).length >= 8;
    default:
      return false;
  }
}

// smooths out the animation when toggling editor actions
export const animateWhenTogglingOff = fn => setTimeout(fn, 200);

export const getActionsByPatternTypeFactory = () => {
  const cache = {};

  return (patternType, disabledActions = []) => {
    if (cache[patternType]) return cache[patternType];

    const disabledActionsList = new Set([
      ...(disabledActionsByPatternsType[patternType.replace('-first', '')] ??
        []),
      ...disabledActions,
    ]);

    const list = disabledActionsList.size
      ? BUTTON_ACTION_TYPES_LIST.filter(
          ({ value }) => !disabledActionsList.has(value)
        )
      : BUTTON_ACTION_TYPES_LIST;

    cache[patternType] = list;
    return list;
  };
};

export const getActionsByPatternType = getActionsByPatternTypeFactory();
