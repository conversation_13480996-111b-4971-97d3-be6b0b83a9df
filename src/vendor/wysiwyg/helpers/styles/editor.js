import styled from 'styled-components';
import { getDefaultPadding } from '../buttons';
import FontIcon from '../../components/FontAwesome/FontIcon';

export const colors = {
  informationalBlue: '#00b2e5',
  green: '#00b850',
  apc_purple: '#5C5CFF',
  apc_purple_20_percent: 'var(--secondary-x-light)',
  apc_purple_10_percent: '#EFF0FF',
  apc_gray_5: '#7e89a9',
  apc_gray_6: '#475872',
  apc_gray_7: '#394455',
  apc_gray_2: '#E7ECF3',
  apc_gray_1: '#F5F7FA',
};

export const draggingOverlayStyle = {
  position: 'absolute',
  top: 0,
  left: 0,
  width: '100%',
  height: '100%',
  zIndex: 4,
  background: 'rgba(15, 9, 69, 0.1)',
};

export function getButtonProps(isActive) {
  return {
    hideBackground: true,
    color: isActive ? colors.apc_purple : colors.apc_gray_5,
    clickColor: '#333',
    activeColor: colors.apc_purple,
    hoverColor: colors.apc_purple,
  };
}

export const toolbarStyle = {
  borderRadius: '6px',
  boxShadow: '0px 12px 34px rgba(71, 88, 114, 0.2)',
  height: '48px',
  paddingLeft: '4px',
  paddingRight: '4px',
};

export const flexStyle = {
  display: 'flex',
};

export const flexColumn = {
  ...flexStyle,
  flexDirection: 'column',
};

export const flexRow = {
  ...flexStyle,
  flexDirection: 'row',
};

export const flexJustifyContentSpaceBetween = {
  ...flexStyle,
  justifyContent: 'space-between',
};

export const tabStyle = {
  flexGrow: 1,
  cursor: 'pointer',
  color: '#969696',
  backgroundColor: '#fff',
  textAlign: 'center',
  fontWeight: 'var(--semi-bold)',
  width: '50%',
  transition: 'background-color 0.15s ease-out, color 0.15s ease-out',
  textTransform: 'capitalize',
  padding: '5px 0',
};

export const selectedTabStyle = {
  color: '#fff',
  backgroundColor: '#23baff',
};

export const secondaryMenuTitleStyle = {
  fontSize: '12px',
  fontWeight: '700',
  color: '#7E89A9',
  letterSpacing: '0.05em',
  lineHeight: '100%',
  marginBottom: '16px',
  textTransform: 'uppercase',
};

export const selectMenuStyle = {
  background: 'transparent',
  border: '1px solid rgba(128, 128, 128, 0.5)',
  fontSize: 'var(--small)',
  height: '29px',
  padding: '5px',
  width: '175px',
  cursor: 'pointer',
};

export const smallInputStyle = {
  fontSize: 'var(--x-small)',
  width: 132,
  height: 32,
  marginLeft: 8,
  flexShrink: 1,
  flexBasis: '45%',
};

export const zoneContainerStyles = {
  flex: 1,
};

export const rowStyles = {
  display: 'flex',
  flexWrap: 'wrap',
  width: '100%',
};

export const getInlineStyles = stylesConfig => {
  return Object.entries(stylesConfig).reduce((prev, [key, value]) => {
    if (value) {
      return { ...prev, [key]: value };
    }

    return prev;
  }, {});
};

export const getConditionalValue = ({
  value,
  whenTrue,
  whenFalse,
  otherwise,
}) => {
  if (value === undefined) {
    return otherwise;
  }

  return value ? whenTrue : whenFalse;
};

export const buildConditionalString = stylesConfig => {
  return Object.entries(stylesConfig)
    .reduce((prev, [style, shouldAdd]) => {
      if (shouldAdd) {
        return [...prev, style];
      }

      return prev;
    }, [])
    .join('');
};

export const getDropShadowString = (shadowAxis, dropShadow) => {
  if (!dropShadow) return '';

  return `${shadowAxis.x}px ${shadowAxis.y}px ${dropShadow?.blur ?? 0}px ${
    dropShadow?.size ?? 0
  }px ${dropShadow?.color}`;
};

export const getButtonStyleString = ({
  borderRadius,
  fontSize,
  width,
  buttonColor,
  textColor,
  patternType,
  fontColor,
  isBold,
  isItalic,
  isUnderlined,
  fontFamily,
  lineHeight,
  letterSpacing,
  textTransform,
  padding,
  paddingTop,
  paddingRight,
  paddingBottom,
  paddingLeft,
  buttonBorder,
  dropShadow,
  shadowAxis,
}) => {
  const { defaultHorizontalPadding, defaultVerticalPadding } =
    getDefaultPadding(patternType);

  // padding here is used to keep padding backwards compatible since
  // in the past users were able to set only one value for padding
  // setting the padding values (top, right, bottom, left)
  // with the same value.
  const defaultXPadding = padding || defaultHorizontalPadding;
  const defaultYPadding = padding || defaultVerticalPadding;

  return buildConditionalString({
    'text-align:center;': true,
    [`border-radius:${borderRadius}px;`]: borderRadius !== undefined,
    [`font-size:${fontSize}px;`]: fontSize !== undefined,
    [`width:${width}px;`]: width !== undefined,
    [`background-color:${buttonColor};`]: buttonColor,
    [`color:${textColor};`]: textColor,
    [`border:${buttonBorder};`]: buttonBorder,
    [`color:${fontColor};`]: fontColor,
    'font-weight:bold;': isBold,
    'font-weight:normal;': !isBold && isBold !== undefined,
    'font-style:italic;': isItalic,
    'font-style:normal;': !isItalic && isItalic !== undefined,
    [`font-family:${fontFamily};`]: fontFamily,
    [`line-height:${lineHeight};`]: lineHeight !== undefined,
    [`letter-spacing:${letterSpacing};`]: letterSpacing !== undefined,
    [`text-transform:${textTransform};`]: textTransform,
    'text-decoration:underline;': isUnderlined,
    'text-decoration:none;': !isUnderlined && isUnderlined !== undefined,
    [`padding-top:${paddingTop ?? defaultYPadding}px;`]: true,
    [`padding-right:${paddingRight ?? defaultXPadding}px;`]: true,
    [`padding-bottom:${paddingBottom ?? defaultYPadding}px;`]: true,
    [`padding-left:${paddingLeft ?? defaultXPadding}px;`]: true,
    [`box-shadow:${getDropShadowString(shadowAxis, dropShadow)};`]:
      Boolean(dropShadow),
  });
};

export const buttonNavTypeWrapperStyle = {
  cursor: 'pointer',
  display: 'flex',
  justifyContent: 'space-evenly',
  '-webkit-font-smoothing': 'antialiased',
};

export const labelStyle = {
  fontSize: 'var(--regular)',
  display: 'flex',
  paddingTop: 4,
  marginBottom: 8,
  fontWeight: 'var(--normal)',
  color: '#888',
  lineHeight: 'normal',
};

export const menuTextStyle = {
  fontSize: 'var(--regular)',
  fontWeight: 'var(--normal)',
  color: '#888',
};

export const buttonNavTypeMenuStyle = {
  margin: '16px 8px',
  flexShrink: 0,
};

export const inputStyle = {
  outline: 'none',
  display: 'flex',
  padding: '0.58em 0.5em',
  borderRadius: 4,
  border: '1px solid transparent',
  backgroundColor: '#f5f7fa',
  lineHeight: '24px',
  fontSize: 'var(--regular)',
  color: '#666',
  width: '100%',
};

export const actionsMenuInputStyle = {
  outline: 'none',
  display: 'flex',
  padding: '5px 10px',
  borderRadius: 4,
  lineHeight: '24px',
  fontSize: 'var(--regular)',
  color: '#666',
  backgroundColor: '#f5f7fa',
  border: 'none',
};

export const shortInputStyle = {
  ...inputStyle,
  width: 62,
};

export const marginBoxRowStyle = {
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
  margin: '4px 0',
};

export const marginBoxStyle = {
  height: '50px',
  width: '60px',
  margin: '4px 16px',
  border: '2px dashed #808080',
};

export const buttonStyleOptionStyle = {
  width: '45%',
  display: 'flex',
  flexDirection: 'column',
  marginBottom: '10px',
};

export const checkboxStyle = {
  marginRight: 10,
  marginTop: 4,
};

export const placeholderStyle = {
  textAlign: 'center',
  color: '#808080',
  fontSize: 'smaller',
  textTransform: 'uppercase',
};

export const InfoIcon = styled(FontIcon)`
  float: right;
  cursor: pointer;
  width: 1em;
  path {
    fill: #7e89a9;
  }
`;

export const InfoOverlay = styled.div`
  z-index: 1;
  position: absolute;
  border-radius: 12px;
  background: #0b1a38;
  padding: 12px;
  opacity: 0.85;
  left: 0px;
  top: -80px;
  pointer-events: none;
  height: ${props => (props.valueOptions ? '100%' : 'inherit')};

  &&& {
    color: #ffffff;
  }
`;

export const LabelRow = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-top: 20px;
  position: relative;
`;

export const removeArrowStyle =
  '.button-wrapper>[data-step=prev]::before {content: none} .button-wrapper>.appcues-button[data-step=next]::after {content: none}';

export const emojiPickerStyles = `.emoji-mart,
.emoji-mart * {
  box-sizing: border-box;
  line-height: 1.15;
  color: #222427;
}

.emoji-mart {
  font-family: -apple-system, BlinkMacSystemFont, "Helvetica Neue", sans-serif;
  font-size: var(--medium-large);
  display: inline-block;
}

.emoji-mart .emoji-mart-emoji {
  padding: 6px;
}

.emoji-mart-bar {
  border: 0 solid #d9d9d9;
}
.emoji-mart-bar:first-child {
  border-bottom-width: 1px;
  border-top-left-radius: 5px;
  border-top-right-radius: 5px;
}
.emoji-mart-bar:last-child {
  border-top-width: 1px;
  border-bottom-left-radius: 5px;
  border-bottom-right-radius: 5px;
}

.emoji-mart-anchors {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  padding: 0 6px;
  line-height: 0;
}

.emoji-mart-anchor {
  position: relative;
  display: block;
  flex: 1 1 auto;
  color: #858585;
  text-align: center;
  padding: 12px 4px;
  overflow: hidden;
  transition: color .1s ease-out;
  margin: 0;
  box-shadow: none;
  background: none;
  border: none;
}
.emoji-mart-anchor:focus { outline: 0 }
.emoji-mart-anchor:hover,
.emoji-mart-anchor:focus,
.emoji-mart-anchor-selected {
  color: #464646;
}

.emoji-mart-anchor-selected .emoji-mart-anchor-bar {
  bottom: 0;
}

.emoji-mart-anchor-bar {
  position: absolute;
  bottom: -3px; left: 0;
  width: 100%; height: 3px;
  background-color: #464646;
}

.emoji-mart-anchors i {
  display: inline-block;
  width: 100%;
  max-width: 22px;
}

.emoji-mart-anchors svg,
.emoji-mart-anchors img {
  fill: currentColor;
  height: 18px;
  width: 18px;
}

.emoji-mart-scroll {
  overflow-y: scroll;
  overflow-x: hidden;
  height: 270px;
  padding: 0 6px 6px 6px;
  will-change: transform; /* avoids "repaints on scroll" in mobile Chrome */
}

.emoji-mart-search {
  margin-top: 6px;
  padding: 0 6px;
  position: relative;
}

.emoji-mart-search input {
  font-size: var(--medium-large);
  display: block;
  width: 100%;
  padding: 5px 25px 6px 10px;
  border-radius: 5px;
  border: 1px solid #d9d9d9;
  outline: 0;
}

.emoji-mart-search input,
.emoji-mart-search input::-webkit-search-decoration,
.emoji-mart-search input::-webkit-search-cancel-button,
.emoji-mart-search input::-webkit-search-results-button,
.emoji-mart-search input::-webkit-search-results-decoration {
  /* remove webkit/blink styles for <input type="search">
   * via https://stackoverflow.com/a/9422689 */
  -webkit-appearance: none;
}

.emoji-mart-search-icon {
  position: absolute;
  top: 7px;
  right: 11px;
  z-index: 2;
  padding: 2px 5px 1px;
  border: none;
  background: none;
}

.emoji-mart-category .emoji-mart-emoji span {
  z-index: 1;
  position: relative;
  text-align: center;
  cursor: default;
}

.emoji-mart-category .emoji-mart-emoji:hover:before {
  z-index: 0;
  content: "";
  position: absolute;
  top: 0; left: 0;
  width: 100%; height: 100%;
  background-color: #f4f4f4;
  border-radius: 100%;
}

.emoji-mart-category-label {
  z-index: 2;
  position: relative;
  position: -webkit-sticky;
  position: sticky;
  top: 0;
}

.emoji-mart-category-label span {
  display: block;
  width: 100%;
  font-weight: var(--medium);
  padding: 5px 6px;
  background-color: #fff;
  background-color: rgba(255, 255, 255, .95);
}

.emoji-mart-category-list {
  margin: 0;
  padding: 0;
}

.emoji-mart-category-list li {
  list-style: none;
  margin: 0;
  padding: 0;
  display: inline-block;
}

.emoji-mart-emoji {
  position: relative;
  display: inline-block;
  font-size: 0;
  margin: 0;
  padding: 0;
  border: none;
  background: none;
  box-shadow: none;
}

.emoji-mart-emoji-native {
  font-family: "Segoe UI Emoji", "Segoe UI Symbol", "Segoe UI", "Apple Color Emoji", "Twemoji Mozilla", "Noto Color Emoji", "Android Emoji";
}

.emoji-mart-no-results {
  font-size: var(--regular);
  text-align: center;
  padding-top: 70px;
  color: #858585;
}
.emoji-mart-no-results-img {
  display: block;
  margin-left: auto;
  margin-right: auto;
  width: 50%;
}
.emoji-mart-no-results .emoji-mart-category-label {
  display: none;
}
.emoji-mart-no-results .emoji-mart-no-results-label {
  margin-top: .2em;
}
.emoji-mart-no-results .emoji-mart-emoji:hover:before {
  content: none;
}

.emoji-mart-preview {
  position: relative;
  height: 70px;
}

.emoji-mart-preview-emoji,
.emoji-mart-preview-data,
.emoji-mart-preview-skins {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
}

.emoji-mart-preview-emoji {
  left: 12px;
}

.emoji-mart-preview-data {
  left: 68px; right: 12px;
  word-break: break-all;
}

.emoji-mart-preview-skins {
  right: 30px;
  text-align: right;
}

.emoji-mart-preview-skins.custom {
  right: 10px;
  text-align: right;
}

.emoji-mart-preview-name {
  font-size: var(--regular);
}

.emoji-mart-preview-shortname {
  font-size: var(--x-small);
  color: #888;
}
.emoji-mart-preview-shortname + .emoji-mart-preview-shortname,
.emoji-mart-preview-shortname + .emoji-mart-preview-emoticon,
.emoji-mart-preview-emoticon + .emoji-mart-preview-emoticon {
  margin-left: .5em;
}

.emoji-mart-preview-emoticon {
  font-size: var(--x-small);
  color: #bbb;
}

.emoji-mart-title span {
  display: inline-block;
  vertical-align: middle;
}

.emoji-mart-title .emoji-mart-emoji {
  padding: 0;
}

.emoji-mart-title-label {
  color: #999A9C;
  font-size: var(--x-large);
  font-weight: var(--light);
}

.emoji-mart-skin-swatches {
  font-size: 0;
  padding: 2px 0;
  border: 1px solid #d9d9d9;
  border-radius: 12px;
  background-color: #fff;
}

.emoji-mart-skin-swatches.custom {
  font-size: 0;
  border: none;
  background-color: #fff;
}

.emoji-mart-skin-swatches.opened .emoji-mart-skin-swatch {
  width: 16px;
  padding: 0 2px;
}

.emoji-mart-skin-swatches.opened .emoji-mart-skin-swatch.selected:after {
  opacity: .75;
}

.emoji-mart-skin-swatch {
  display: inline-block;
  width: 0;
  vertical-align: middle;
  transition-property: width, padding;
  transition-duration: .125s;
  transition-timing-function: ease-out;
}

.emoji-mart-skin-swatch:nth-child(1) { transition-delay: 0s }
.emoji-mart-skin-swatch:nth-child(2) { transition-delay: .03s }
.emoji-mart-skin-swatch:nth-child(3) { transition-delay: .06s }
.emoji-mart-skin-swatch:nth-child(4) { transition-delay: .09s }
.emoji-mart-skin-swatch:nth-child(5) { transition-delay: .12s }
.emoji-mart-skin-swatch:nth-child(6) { transition-delay: .15s }

.emoji-mart-skin-swatch.selected {
  position: relative;
  width: 16px;
  padding: 0 2px;
}

.emoji-mart-skin-swatch.selected:after {
  content: "";
  position: absolute;
  top: 50%; left: 50%;
  width: 4px; height: 4px;
  margin: -2px 0 0 -2px;
  background-color: #fff;
  border-radius: 100%;
  pointer-events: none;
  opacity: 0;
  transition: opacity .2s ease-out;
}

.emoji-mart-skin-swatch.custom {
  display: inline-block;
  width: 0;
  height: 38px;
  overflow: hidden;
  vertical-align: middle;
  transition-property: width, height;
  transition-duration: .125s;
  transition-timing-function: ease-out;
  cursor: default;
}

.emoji-mart-skin-swatch.custom.selected {
  position: relative;
  width: 36px;
  height: 38px;
  padding: 0 2px 0 0;
}

.emoji-mart-skin-swatch.custom.selected:after {
  content: "";
  width: 0;
  height: 0;
}

.emoji-mart-skin-swatches.custom .emoji-mart-skin-swatch.custom:hover {
  background-color: #f4f4f4;
  border-radius: 10%;
}

.emoji-mart-skin-swatches.custom.opened .emoji-mart-skin-swatch.custom {
  width: 36px;
  height: 38px;
  padding: 0 2px 0 0;
}

.emoji-mart-skin-swatches.custom.opened .emoji-mart-skin-swatch.custom.selected:after {
  opacity: .75;
}

.emoji-mart-skin-text.opened {
  display: inline-block;
  vertical-align: middle;
  text-align: left;
  color: #888;
  font-size: var(--x-small);
  padding: 5px 2px;
  width: 95px;
  height: 40px;
  border-radius: 10%;
  background-color: #fff;
}

.emoji-mart-skin {
  display: inline-block;
  width: 100%;
  padding-top: 100%;
  max-width: 12px;
  border-radius: 100%;
}

.emoji-mart-skin-tone-1 { background-color: #ffc93a }
.emoji-mart-skin-tone-2 { background-color: #fadcbc }
.emoji-mart-skin-tone-3 { background-color: #e0bb95 }
.emoji-mart-skin-tone-4 { background-color: #bf8f68 }
.emoji-mart-skin-tone-5 { background-color: #9b643d }
.emoji-mart-skin-tone-6 { background-color: #594539 }

/* For screenreaders only, via https://stackoverflow.com/a/19758620 */
.emoji-mart-sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  border: 0;
}

/*
 * Dark mode styles
 */

.emoji-mart-dark {
  color: #fff;
  border-color: #555453;
  background-color: #222;
}

.emoji-mart-dark .emoji-mart-bar {
  border-color: #555453;
}

.emoji-mart-dark .emoji-mart-search input {
  color: #fff;
  border-color: #555453;
  background-color: #2f2f2f;
}

.emoji-mart-dark .emoji-mart-search-icon svg {
  fill: #fff;
}

.emoji-mart-dark .emoji-mart-category .emoji-mart-emoji:hover:before {
  background-color: #444;
}

.emoji-mart-dark .emoji-mart-category-label span {
  background-color: #222;
  color: #fff;
}

.emoji-mart-dark .emoji-mart-skin-swatches {
  border-color: #555453;
  background-color: #222;
}

.emoji-mart-dark .emoji-mart-anchor:hover,
.emoji-mart-dark .emoji-mart-anchor:focus,
.emoji-mart-dark .emoji-mart-anchor-selected {
  color: #bfbfbf;
}
`;

export const editorButtonStyle = {
  alignItems: 'center',
  backgroundColor: '#23baff',
  border: 'none',
  borderRadius: '4px',
  color: 'rgba(255, 255, 255, 0.9)',
  cursor: 'pointer',
  display: 'inline-flex',
  float: 'right',
  fontSize: 'var(--regular)',
  fontWeight: 'var(--medium)',
  justifyContent: 'center',
  marginTop: '10px',
  outline: 'none',
  padding: '9px 12px 8px',
  textAlign: 'center',
  width: '40%',
};

/** *******************
 *
 * STYLED COMPONENTS
 *
 ******************** */

export const DropDownMenuList = styled.div`
  display: flex;
  position: absolute;
  left: 0;
  background-color: #fff;
  max-height: 266px;
  width: 100%;
  border-radius: 5px;
  flex-direction: column;
  opacity: 0;
  height: 0;
  overflow: hidden;
  transition: opacity 0.15s ease-out, margin-top 0.2s ease-out;
  pointer-events: none;
  color: #394455;
  z-index: 150;
  box-shadow: 0px 0px 12px rgba(30, 30, 70, 0.2);

  ${({ isMenuOpen }) =>
    isMenuOpen &&
    `
    overflow-y: auto;
    overflow-x: hidden;
    height: auto;
    pointer-events: all;
    margin-top: 5px;
    opacity: 1;
  `}
`;

export const Button = styled.div`
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
  flex-shrink: 0;
  flex-grow: 1;
  padding: 9px 12px 8px;
  background-color: #f5f7fa;
  text-align: center;
  cursor: pointer;
  border-radius: 6px;
  color: #242a35 !important;
  font-size: var(--regular);
  height: 40px;
`;

export const LabeledDropdownContainer = styled.div`
  display: flex;
  margin: 16px;

  label {
    color: ${colors.apc_gray_7};
    width: 150px;
    margin-left: 10px;
    margin-top: 10px;
    font-weight: var(--bold);
  }

  ${Button} {
    float: right;
    width: 150px;
    margin-top: 5px;

    ${DropDownMenuList} {
      width: 150px;
    }

    span:first-of-type {
      text-overflow: ellipsis;
      white-space: nowrap;
      overflow: hidden;
    }
  }
`;

export const RadioGroup = styled.div`
  margin: 16px 0;
`;

export const Option = styled.div`
  display: grid;
  gap: 16px;
`;

export const OptionItem = styled.div``;

export const OptionItemBody = styled.div`
  padding-top: 16px;
`;

export const OptionsContainer = styled.div`
  ${Option}:not(:last-child) {
    margin-bottom: 16px;
  }
`;

export const InputContainer = styled.div`
  margin-bottom: ${({ selected }) => (selected ? 20 : 8)}px;
`;

export const ErrorMessage = styled.p`
  color: var(--warning);
  font-size: var(--x-small);
  margin: 0;
`;

export const ActionsItem = styled.div`
  display: flex;
  align-items: center;
  cursor: pointer;

  ${({ hasBorder }) =>
    hasBorder &&
    `
    border-bottom-right-radius: 12px;
    border-bottom-left-radius: 12px;
  `}

  span {
    color: ${colors.apc_gray_7};
  }

  &:hover {
    background-color: ${colors.apc_gray_1};

    svg {
      color: ${colors.apc_purple};
    }
  }
`;
