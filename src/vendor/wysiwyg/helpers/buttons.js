import cond from 'lodash.cond';
import {
  PATTERN_TYPES,
  BUTTON_ACTION_TYPES,
  BUTTON_ACTIONS_WITH_DATA_STEP_ATTRS,
  DEFAULT_BUTTON_TEXT,
} from './constants';
import { isButtonPrimary, getButtonColor, getTextColor } from './themes';
import {
  buildConditionalString,
  getButtonStyleString,
  getDropShadowString,
  removeArrowStyle,
} from './styles/editor';
import { escapeHTML } from './draft/html';

export const getDefaultPadding = patternType => {
  const isHotspotOrTooltip =
    patternType === PATTERN_TYPES.HOTSPOT_GROUP ||
    patternType === PATTERN_TYPES.TOOLTIP_GROUP;

  return {
    defaultHorizontalPadding: isHotspotOrTooltip ? 14 : 18,
    defaultVerticalPadding: isHotspotOrTooltip ? 5 : 8,
  };
};

export const getCustomHoverStyle = ({
  hoverBackgroundColor,
  hoverDropShadow,
  id,
  editingZoneId = null,
  hoverBorderColor,
  hoverBorderWidth,
  inputHoverLock,
}) => {
  if (
    !hoverBackgroundColor &&
    !hoverDropShadow &&
    !(hoverBorderColor && hoverBorderWidth) &&
    !inputHoverLock
  )
    return;

  const dropShadowHover = hoverDropShadow && JSON.parse(hoverDropShadow);
  const shadowAxisHover = getShadowAxis(dropShadowHover);
  const hoverBorder = `${hoverBorderWidth}px solid ${hoverBorderColor}`;

  const styles = buildConditionalString({
    [`background-color: ${hoverBackgroundColor} !important;`]:
      hoverBackgroundColor,
    [`border: ${hoverBorder} !important;`]: hoverBorder,
    [`box-shadow: ${getDropShadowString(
      shadowAxisHover,
      dropShadowHover
    )} !important;`]: hoverDropShadow,
  });

  const pseudoClass = editingZoneId === id || inputHoverLock ? '' : ':hover';

  return `#button-${id}${pseudoClass} {
    ${styles};
    transition: all .2s ease-out;
  }`;
};

// Shadow axis are defined as cos/sin of radians * distance,
// So first we need to convert degree => radians and we do that with:
// (angle * Math.PI) / 180;
// we also subtract angle - 90 to make sure it runs clock wise.
export const getShadowAxis = (dropShadow = {}) => {
  const { distance = 0, angle = 0 } = dropShadow;
  const radians = ((angle - 90) * Math.PI) / 180;

  return {
    x: Math.cos(radians) * distance,
    y: Math.sin(radians) * distance,
  };
};

const isEqualType =
  type =>
  ({ buttonActionType }) => {
    return buttonActionType === type;
  };

const isTypeWithDataStep = ({ buttonActionType }) => {
  return BUTTON_ACTIONS_WITH_DATA_STEP_ATTRS.includes(buttonActionType);
};

const shouldSetFlowAsComplete = ({ markCurrentFlowAsComplete }) => {
  return markCurrentFlowAsComplete;
};

const isSkipAndMarkFlowComplete = ({
  buttonActionType,
  markCurrentFlowAsComplete,
}) => {
  return (
    buttonActionType === BUTTON_ACTION_TYPES.END_FLOW &&
    markCurrentFlowAsComplete
  );
};

const isTypeNull = ({ buttonActionType }) => {
  return !buttonActionType;
};

const returnCurrentType = ({ buttonActionType }) => buttonActionType;
const returnStepIndex = ({ stepIndex }) => stepIndex;
const returnType = type => () => type;

export const getDataStep = persistedState => {
  const { buttonActionType, markCurrentFlowAsComplete, stepIndex } =
    persistedState.toJS();

  const findType = cond([
    [isTypeWithDataStep, returnCurrentType],
    [isEqualType(BUTTON_ACTION_TYPES.CUSTOM_PAGE), returnStepIndex],
    [
      isEqualType(BUTTON_ACTION_TYPES.APPCUES),
      returnType(BUTTON_ACTION_TYPES.END_STEP_AND_FLOW),
    ],
    [isTypeNull, returnType(BUTTON_ACTION_TYPES.NEXT_PAGE)],
    [
      isSkipAndMarkFlowComplete,
      returnType(BUTTON_ACTION_TYPES.SKIP_AND_END_FLOW),
    ],
    [
      shouldSetFlowAsComplete,
      returnType(BUTTON_ACTION_TYPES.END_STEP_AND_FLOW),
    ],
    [isEqualType(BUTTON_ACTION_TYPES.END_FLOW), returnCurrentType],
    [() => true, () => null],
  ]);

  return findType({
    buttonActionType,
    stepIndex,
    markCurrentFlowAsComplete,
  });
};

export const generateButtonAST = ({
  persistedState,
  zoneId,
  theme,
  patternType,
}) => {
  const {
    textAlign,
    href,
    borderRadius,
    fontSize,
    width,
    isNewWindow,
    buttonActionType,
    marginTop,
    marginRight,
    marginBottom,
    marginLeft,
    flowId,
    eventName,
    trackEvent,
    userPropertiesToUpdate,
    updateUserProperties,
    buttonType,
    fontColor,
    hoverBackgroundColor,
    hoverDropShadow,
    hoverBorderColor,
    hoverBorderWidth,
    isBold,
    isItalic,
    isUnderlined,
    fontFamily,
    lineHeight,
    letterSpacing,
    textTransform,
    paddingTop,
    paddingRight,
    paddingBottom,
    paddingLeft,
    padding,
    inlineBorderWidth,
    inlineBorderColor,
    inlineBackgroundColor,
    inlineDropShadow,
    inputHoverLock,
  } = persistedState.toJS();

  const dropShadow = inlineDropShadow && JSON.parse(inlineDropShadow);
  const shadowAxis = getShadowAxis(dropShadow);

  const isPrimary = isButtonPrimary(buttonType);
  const buttonColor =
    inlineBackgroundColor ?? getButtonColor(buttonType, theme);
  const textColor = getTextColor(buttonType, theme);
  const buttonBorder = `${inlineBorderWidth}px solid ${inlineBorderColor}`;

  const wrapperAttrs = {
    class: `button-wrapper ${
      isPrimary ? 'appcues-actions-right' : 'appcues-actions-left'
    }`,
  };
  wrapperAttrs.style = `width:100%;text-align:${textAlign || 'center'};`;

  const defaultFontSize =
    patternType === PATTERN_TYPES.HOTSPOT_GROUP ||
    patternType === PATTERN_TYPES.TOOLTIP_GROUP
      ? 12
      : 14;
  wrapperAttrs.style += `font-size:${defaultFontSize}px;`;

  const isMarginTopSet = marginTop || marginTop === 0;
  const isMarginBottomSet = marginBottom || marginBottom === 0;

  wrapperAttrs.style += `margin-top:${isMarginTopSet ? marginTop : 5}px;`;
  wrapperAttrs.style += `margin-bottom:${
    isMarginBottomSet ? marginBottom : 5
  }px;`;

  if (marginRight) {
    wrapperAttrs.style += `width:calc(100% - ${marginRight}px);`;
  }

  if (marginLeft) {
    wrapperAttrs.style += `margin-left:${marginLeft}px;`;
  }

  const buttonAttrs = {
    class: 'appcues-button-success appcues-button',
    'data-field-id': zoneId,
    id: `button-${zoneId}`,
  };
  buttonAttrs.style = getButtonStyleString({
    borderRadius,
    fontSize,
    width,
    buttonColor,
    textColor,
    patternType,
    fontColor,
    isBold,
    isItalic,
    isUnderlined,
    fontFamily,
    lineHeight,
    letterSpacing,
    textTransform,
    padding,
    paddingTop,
    paddingRight,
    paddingBottom,
    paddingLeft,
    buttonBorder,
    shadowAxis,
    dropShadow,
  });

  if (trackEvent && eventName) {
    buttonAttrs['data-attrs-event'] = JSON.stringify({
      event: eventName,
      properties: {
        _builderButtonEvent: true,
      },
    }).replace(/"/g, '&quot;');
  }

  if (
    updateUserProperties &&
    userPropertiesToUpdate &&
    Object.keys(userPropertiesToUpdate).length > 0
  ) {
    buttonAttrs['data-attrs-profile-update'] = JSON.stringify(
      userPropertiesToUpdate
    ).replace(/"/g, '&quot;');
  }

  const dataStep = getDataStep(persistedState);
  if (dataStep !== null) {
    buttonAttrs['data-step'] = dataStep;
  }

  if (buttonActionType === BUTTON_ACTION_TYPES.URL) {
    buttonAttrs.href = href;
    buttonAttrs.target = isNewWindow ? '_blank' : '_self';
  } else if (buttonActionType === BUTTON_ACTION_TYPES.APPCUES) {
    if (href) {
      buttonAttrs.href = href;
      buttonAttrs['data-next-content-id'] = flowId;
    } else {
      buttonAttrs.onclick = `window.parent.Appcues.show('${flowId}')`;
    }
  }

  if (!href) {
    buttonAttrs.role = 'button';
    buttonAttrs.tabindex = 0;
  }

  const buttonText = persistedState.get('buttonText') || DEFAULT_BUTTON_TEXT;

  const buttonObj = {
    type: 'tag',
    name: 'a',
    voidElement: false,
    attrs: buttonAttrs,
    children: [
      {
        type: 'text',
        content: escapeHTML(buttonText),
      },
    ],
  };

  const removeArrowStyleObj = {
    type: 'tag',
    name: 'style',
    voidElement: false,
    attrs: null,
    children: [
      {
        type: 'text',
        content: removeArrowStyle,
      },
    ],
  };

  const buttonWrapperChildren = [];
  if (
    buttonActionType === BUTTON_ACTION_TYPES.PREVIOUS_PAGE ||
    buttonActionType === BUTTON_ACTION_TYPES.NEXT_PAGE
  ) {
    buttonWrapperChildren.push(removeArrowStyleObj);
  }
  if (
    hoverBackgroundColor ||
    hoverDropShadow ||
    hoverBorderColor ||
    hoverBorderWidth
  ) {
    const buttonStyleObject = {
      type: 'tag',
      name: 'style',
      voidElement: false,
      attrs: null,
      children: [
        {
          type: 'text',
          content: getCustomHoverStyle({
            id: zoneId,
            hoverBackgroundColor,
            hoverDropShadow,
            hoverBorderColor,
            hoverBorderWidth,
            inputHoverLock,
          }),
        },
      ],
    };

    buttonWrapperChildren.push(buttonStyleObject);
  }
  buttonWrapperChildren.push(buttonObj);

  const ast = [];
  ast.push({
    type: 'tag',
    name: 'div',
    voidElement: false,
    attrs: wrapperAttrs,
    children: buttonWrapperChildren,
  });

  return ast;
};
