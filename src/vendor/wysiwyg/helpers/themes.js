import { BUTTON_TYPES } from './constants';

export function isButtonPrimary(buttonType) {
  return !!(buttonType === BUTTON_TYPES.PRIMARY || buttonType === undefined);
}

export function getButtonColor(buttonType, theme) {
  const isPrimary = isButtonPrimary(buttonType);
  return isPrimary ? theme.primaryColor : theme.secondaryColor;
}

export function getTextColor(buttonType, theme) {
  const isPrimary = isButtonPrimary(buttonType);
  return isPrimary ? theme.buttonTextColor : theme.secondaryButtonTextColor;
}
