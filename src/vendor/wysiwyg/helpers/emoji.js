import { Modifier } from 'draft-js';

export const OLD_SVG_URL = '//twemoji.maxcdn.com/2/svg/';
export const BASE_SVG_URL =
  '//cdn.jsdelivr.net/gh/twitter/twemoji@14.0.2/assets/svg/';

const generateEmojiUrl = emoji => {
  // check to make sure that our
  // saved url matches what the twemoji cdn expects.
  if (/-/g.test(emoji.unified)) {
    // Removes variant values introduced by emojiMart
    return `${BASE_SVG_URL}${emoji.unified.split('-fe0f')[0]}.svg`;
  }
  if (/20e3/g.test(emoji.unified)) {
    // Removes variant values introduced by emojiMart, and leading 00's
    return `${BASE_SVG_URL}${
      emoji.unified.slice(2).split('-fe0f')[0]
    }-20e3.svg`;
  }
  return `${BASE_SVG_URL}${emoji.unified}.svg`;
};

export const pickNativeEmoji = ({ localState, emoji }) => {
  const editorState = localState.get('editorState');
  const selectionState = editorState.getSelection();
  const contentState = editorState.getCurrentContent();
  return selectionState.isCollapsed()
    ? Modifier.insertText(contentState, selectionState, emoji.native)
    : Modifier.replaceText(contentState, selectionState, emoji.native);
};

export const pickImageEmoji = ({ persistedState, emoji }) => {
  const url = generateEmojiUrl(emoji);
  const width = persistedState.get('width');
  const newWidth = width || 50;

  return persistedState
    .set('url', url)
    .set('width', newWidth)
    .set('altText', `${emoji.name} emoji`)
    .set('textAlign', 'center');
};
