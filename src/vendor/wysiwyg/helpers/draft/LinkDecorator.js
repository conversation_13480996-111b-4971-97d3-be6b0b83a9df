import React from 'react';
import { ContentState } from 'draft-js';
import Link from '../../components/entities/Link';

const DYNAMIC_ATTRS = ['data-step', 'class', 'role', 'tabindex'];

const getDynamicAttrs = (
  getCallback,
  attrNames,
  parseAttributeName = name => name
) => {
  return attrNames.reduce((prev, attrName) => {
    const attrValue = getCallback(attrName);

    if (attrValue) {
      return {
        ...prev,
        [parseAttributeName(attrName)]: attrValue,
      };
    }

    return prev;
  }, {});
};

const toReactAttrName = name => {
  const DYNAMIC_ATTRS_SNAPDOM_TO_REACT = {
    class: 'className',
    tabindex: 'tabIndex',
  };

  return DYNAMIC_ATTRS_SNAPDOM_TO_REACT[name] || name;
};

function removeInheritColor(attributes) {
  // we can't get parent color if it is inherit
  // otherwise it will override the Appcues default link color
  const { color, ...rest } = attributes;

  if (color !== 'inherit') return attributes;

  return rest;
}

export function findLinkEntities(contentBlock, callback, contentState) {
  contentBlock.findEntityRanges(character => {
    const entityKey = character.getEntity();
    return (
      entityKey !== null &&
      contentState.getEntity(entityKey).getType() === 'LINK'
    );
  }, callback);
}

export const LinkDecorator = {
  strategy: findLinkEntities,
  component: Link,
};

export function linkToEntity(nodeName, node) {
  if (nodeName === 'a') {
    // We want to pull the literal href value provided by users.
    const href = node.getAttribute('href');

    return ContentState.createFromText(node.outerHTML)
      .createEntity(
        'LINK',
        'MUTABLE',
        removeInheritColor({
          href,
          color: node.style.color || node.parentNode.style.color,
          isNewWindow: node.target === '_blank',
          ...getDynamicAttrs(
            attrName => node.getAttribute(attrName),
            DYNAMIC_ATTRS
          ),
        })
      )
      .getLastCreatedEntityKey();
  }
}

export function entityToLink(entity, originalText) {
  if (entity.type === 'LINK') {
    return React.createElement('a', {
      style: { color: entity.data.color },
      href: entity.data.href,
      target: entity.data.isNewWindow ? '_blank' : '_self',
      rel: 'noreferrer',
      ...getDynamicAttrs(
        attrName => entity.data[attrName],
        DYNAMIC_ATTRS,
        toReactAttrName
      ),
    });
  }

  return originalText;
}
