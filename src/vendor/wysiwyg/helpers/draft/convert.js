/* eslint react/display-name: 0 */ // Not exporting React components here
import React from 'react';
import {
  convertToHTML as draftConvertToHTML,
  convertFromHTML as draftConvertFromHTML,
} from 'draft-convert';
import { CompositeDecorator, ContentState } from 'draft-js';
import { LinkDecorator, linkToEntity, entityToLink } from './LinkDecorator';
import { INLINE_STYLE_TYPE } from '../constants';

export const CUSTOM_STYLE_PREFIX_COLOR = 'COLOR_';
export const CUSTOM_STYLE_PREFIX_FONT_SIZE = 'FONT_SIZE_';
export const CUSTOM_STYLE_PREFIX_FONT_FAMILY = 'FONT_FAMILY_';
export const CUSTOM_STYLE_PREFIX_FONT_WEIGHT = 'FONT_WEIGHT_';
export const CUSTOM_STYLE_PREFIX_FONT_LETTER_SPACING = 'FONT_LETTER_SPACING_';
export const CUSTOM_STYLE_PREFIX_FONT_TEXT_TRANSFORM = 'FONT_TEXT_TRANSFORM_';
export const NBSP = '\u00A0';
export const ZWSP = '\u200B';
export const ZWSP_RE = new RegExp(ZWSP, 'g');

export const INLINE_STYLES = {
  [CUSTOM_STYLE_PREFIX_FONT_SIZE]: {
    css: 'font-size',
    react: 'fontSize',
  },
  [CUSTOM_STYLE_PREFIX_COLOR]: {
    css: 'color',
    react: 'color',
  },
  [CUSTOM_STYLE_PREFIX_FONT_FAMILY]: {
    css: 'font-family',
    react: 'fontFamily',
  },
  [CUSTOM_STYLE_PREFIX_FONT_WEIGHT]: {
    css: 'font-weight',
    react: 'fontWeight',
  },
  [CUSTOM_STYLE_PREFIX_FONT_LETTER_SPACING]: {
    css: 'letter-spacing',
    react: 'letterSpacing',
  },
  [CUSTOM_STYLE_PREFIX_FONT_TEXT_TRANSFORM]: {
    css: 'text-transform',
    react: 'textTransform',
  },
};

export const decorator = new CompositeDecorator([LinkDecorator]);

export function convertFromHTML(rawHtmlContent, convertOptions = {}) {
  // We add zero-width spaces (unicode 200B) to get the browser to render a line
  // break for a <br> and to give empty unstyled blocks (<p> tags) a size (see
  // convertToHTML() below). When editing, we don't really want those zero-width
  // spaces in there, so just remove them here.
  const htmlContent = rawHtmlContent.replace(ZWSP_RE, '');

  return draftConvertFromHTML({
    htmlToStyle: (nodeName, node, originalCurrentStyle) => {
      let currentStyle = originalCurrentStyle;
      if (node instanceof HTMLElement && node.style) {
        currentStyle = originalCurrentStyle
          .withMutations(function (style) {
            const { fontStyle } = node.style;
            const { textDecoration } = node.style;

            // Add styles from INLINE_STYLES
            for (const styleType in INLINE_STYLES) {
              const styleTypeText = INLINE_STYLES[styleType].react;
              const styleTypeValue = node.style[styleTypeText];
              const isFontColor = styleType === CUSTOM_STYLE_PREFIX_COLOR;
              if (
                styleTypeValue &&
                (!isFontColor || (isFontColor && styleTypeValue !== 'inherit'))
              ) {
                style.add(`${styleType}${styleTypeValue}`);
              }
            }

            if (fontStyle === 'italic') {
              style.add('ITALIC');
            } else if (fontStyle === 'normal') {
              style.remove('ITALIC');
            }

            if (textDecoration === 'underline') {
              style.add('UNDERLINE');
            }

            if (textDecoration === 'line-through') {
              style.add('STRIKETHROUGH');
            }

            if (textDecoration === 'none') {
              style.remove('UNDERLINE');
              style.remove('STRIKETHROUGH');
            }
          })
          .toOrderedSet();
      }
      return currentStyle;
    },
    htmlToEntity: (nodeName, node) => {
      const entity = linkToEntity(nodeName, node);
      return entity;
    },
    textToEntity: () => {
      return [];
    },
    htmlToBlock: (nodeName, node) => {
      let nodeType = 'unstyled';
      switch (nodeName) {
        case 'h1':
          nodeType = 'header-one';
          break;
        case 'h2':
          nodeType = 'header-two';
          break;
        case 'h3':
          nodeType = 'header-three';
          break;
        case 'h4':
          nodeType = 'header-four';
          break;
        case 'h5':
          nodeType = 'header-five';
          break;
        case 'unordered-list-item':
          nodeType = 'unordered-list-item';
          break;
        case 'ordered-list-item':
          nodeType = 'ordered-list-item';
          break;
        default:
          break;
      }

      const data = {};

      if (node.style && node.style.lineHeight) {
        data.lineHeight = node.style.lineHeight;
      }

      if (node.style && node.style.textAlign) {
        data.textAlign = node.style.textAlign;
      }

      if (Object.keys(data).length) {
        return {
          type: nodeType,
          data,
        };
      }
    },
    ...convertOptions,
  })(htmlContent);
}

export function convertFromPastedHTML(rawHtmlContent) {
  // Strip tabs from pasted content for now.
  const htmlContent = rawHtmlContent.replace(/\t/g, '');
  return convertFromHTML(htmlContent, {
    htmlToBlock: (nodeName, node) => {
      // Don't convert table elements
      if (
        nodeName === 'table' ||
        nodeName === 'tr' ||
        nodeName === 'td' ||
        nodeName === 'tbody'
      ) {
        return;
      }

      let nodeType = 'unstyled';
      switch (nodeName) {
        case 'h1':
          nodeType = 'header-one';
          break;
        case 'h2':
          nodeType = 'header-two';
          break;
        case 'h3':
          nodeType = 'header-three';
          break;
        case 'h4':
          nodeType = 'header-four';
          break;
        case 'h5':
          nodeType = 'header-five';
          break;
        case 'br':
          return false;
        default:
          break;
      }

      const isNotNestedBlock =
        nodeName !== 'ul' && nodeName !== 'ol' && nodeName !== 'blockquote';

      const data = {};

      if (node.style && node.style.lineHeight) {
        data.lineHeight = node.style.lineHeight;
      }

      if (node.style && node.style.textAlign) {
        data.textAlign = node.style.textAlign;
      }

      if (Object.keys(data).length && isNotNestedBlock) {
        return {
          type: nodeType,
          data,
        };
      }
    },
  });
}

export function convertToHTML(editorState) {
  const transformedContentState = ContentState.createFromBlockArray(
    editorState
      .getCurrentContent()
      .getBlockMap()
      .map(block =>
        block.update('text', text =>
          text
            // Replaces spaces following a newline with $nbsp;
            .replace(/\n /g, `\n${NBSP}`)
            // Replaces extra spaces with &nbsp; characters.
            .replace(/ {2,}/g, match => ` ${NBSP.repeat(match.length - 1)}`)
            // Replaces a leading space with &nbsp;
            .replace(/^ /, NBSP)
            // Replaces single trailing newlines with a newline and a zero-width
            // space, so that the <br> that the newline turns into actually
            // renders a line break in the browser. Normally a <br> followed by
            // nothing will collapse and not cause a line break to be rendered.
            // Adding the zero-width space (unicode 200B) after the <br> causes
            // the browser to render the line break. See this SO question for
            // more: https://stackoverflow.com/q/15008205
            .replace(/\n$/, `\n${ZWSP}`)
            // For empty paragraphs (created when the user hits Return but doesn't
            // add any actual text afterwards), the browser will render the <p>
            // tag with no height if there's no content inside. Add a zero-width
            // space inside so that it has content and has a size.
            .replace('', () =>
              block.getType() === 'unstyled' && block.getLength() === 0
                ? ZWSP
                : ''
            )
        )
      )
      .toArray()
  );
  return draftConvertToHTML({
    styleToHTML: style => {
      if (style.startsWith(CUSTOM_STYLE_PREFIX_COLOR)) {
        return (
          <span
            style={{
              color: style.split(CUSTOM_STYLE_PREFIX_COLOR)[1],
            }}
          />
        );
      }

      if (style.startsWith(CUSTOM_STYLE_PREFIX_FONT_SIZE)) {
        return (
          <span
            style={{
              fontSize: style.split(CUSTOM_STYLE_PREFIX_FONT_SIZE)[1],
              color: 'inherit',
            }}
          />
        );
      }

      if (style === INLINE_STYLE_TYPE.ITALIC) {
        return (
          <em
            style={{
              color: 'inherit',
            }}
          />
        );
      }

      if (style === INLINE_STYLE_TYPE.UNDERLINE) {
        return (
          <u
            style={{
              color: 'inherit',
            }}
          />
        );
      }

      if (style.startsWith(CUSTOM_STYLE_PREFIX_FONT_WEIGHT)) {
        const fontWeight = style.slice(CUSTOM_STYLE_PREFIX_FONT_WEIGHT.length);
        return (
          <span
            style={{
              color: 'inherit',
              fontWeight,
            }}
          />
        );
      }

      if (style.startsWith(CUSTOM_STYLE_PREFIX_FONT_LETTER_SPACING)) {
        const letterSpacing = style.slice(
          CUSTOM_STYLE_PREFIX_FONT_LETTER_SPACING.length
        );
        return (
          <span
            style={{
              color: 'inherit',
              letterSpacing,
            }}
          />
        );
      }

      if (style.startsWith(CUSTOM_STYLE_PREFIX_FONT_TEXT_TRANSFORM)) {
        const textTransform = style.slice(
          CUSTOM_STYLE_PREFIX_FONT_TEXT_TRANSFORM.length
        );
        return (
          <span
            style={{
              color: 'inherit',
              textTransform,
            }}
          />
        );
      }

      if (style.startsWith(CUSTOM_STYLE_PREFIX_FONT_FAMILY)) {
        // NOTE: Need to remove double quotes from font family string or else the style is invalid
        return (
          <span
            style={{
              fontFamily: style
                .split(CUSTOM_STYLE_PREFIX_FONT_FAMILY)[1]
                .replace(/["']+/g, ''),
              color: 'inherit',
            }}
          />
        );
      }
    },
    blockToHTML: block => {
      if (block.data && Object.keys(block.data).length) {
        const styleProps = {
          style: block.data,
        };

        switch (block.type) {
          /* eslint-disable jsx-a11y/heading-has-content */
          case 'header-one':
            return <h1 {...styleProps} />;
          case 'header-two':
            return <h2 {...styleProps} />;
          case 'header-three':
            return <h3 {...styleProps} />;
          case 'header-four':
            return <h4 {...styleProps} />;
          case 'header-five':
            return <h5 {...styleProps} />;
          /* eslint-enable jsx-a11y/heading-has-content */
          case 'unordered-list-item':
            return {
              start: '<li>',
              end: '</li>',
              nestStart: '<ul>',
              nestEnd: '</ul>',
            };
          case 'ordered-list-item':
            return {
              start: '<li>',
              end: '</li>',
              nestStart: '<ol>',
              nestEnd: '</ol>',
            };
          default:
            return <p {...styleProps} />;
        }
      }
    },
    entityToHTML: (entity, originalText) => {
      return entityToLink(entity, originalText);
    },
  })(transformedContentState);
}

export function customStyleFn(style) {
  const styleNames = style.toJS();
  return styleNames.reduce((styles, styleName) => {
    if (styleName === 'CODE') {
      styles.color = '#c7254e';
      styles.padding = '2px 4px';
      styles.fontSize = '90%';
      styles.backgroundColor = 'rgba(249,242,244,0.7)';
      styles.borderRadius = '4px';
    }
    if (styleName === 'CODE' || styleName === 'PRE') {
      styles.fontFamily = 'Menlo,Monaco,Consolas,"Courier New",monospace';
    }

    // Add styles from INLINE_STYLES
    for (const styleType in INLINE_STYLES) {
      if (styleName.startsWith(styleType)) {
        const styleTypeText = INLINE_STYLES[styleType].react;
        styles[styleTypeText] = styleName.split(styleType)[1];
      }
    }

    return styles;
  }, {});
}

export function blockStyleFn(contentBlock) {
  const classNames = [];
  const { textAlign, lineHeight } = contentBlock.getData().toJS();

  if (textAlign) {
    classNames.push(`align-${textAlign}`);
  }

  if (lineHeight) {
    classNames.push(`line-height-${lineHeight.replace('.', '_')}`);
  }

  return classNames.join(' ');
}

export function getResetSelection(editorState) {
  const firstBlock = editorState.getCurrentContent().getFirstBlock();
  const firstBlockKey = firstBlock.getKey();

  const currentSelection = editorState.getSelection();
  const newSelection = currentSelection
    .set('anchorKey', firstBlockKey)
    .set('focusKey', firstBlockKey)
    .set('anchorOffset', 0)
    .set('focusOffset', 0)
    .set('isBackward', false);

  return newSelection;
}
