import { Set, is } from 'immutable';
import { RichUtils, EditorState, Modifier } from 'draft-js';
import { getEditorDocument } from 'ext/lib/document';
import { TEXT_STYLE_TYPE } from '../constants';
import { getBlocksFromSelection } from './selection';
import { CUSTOM_STYLE_PREFIX_FONT_SIZE, INLINE_STYLES } from './convert';
import { findLinkEntities } from './LinkDecorator';

const DEFAULT_FONT_SIZE = '14px';

/**
 * LISTS
 */

export const handleList = (listType, localState) => {
  return localState.set(
    'editorState',
    RichUtils.toggleBlockType(localState.get('editorState'), listType)
  );
};

/**
 * TEXT ALIGNMENT
 */

export const handleTextAlignment = (textAlign, localState, blockData) => {
  const editorState = localState.get('editorState');
  const newContentState = Modifier.setBlockData(
    editorState.getCurrentContent(),
    editorState.getSelection(),
    {
      ...blockData,
      textAlign,
    }
  );
  return localState.set(
    'editorState',
    EditorState.push(editorState, newContentState, 'change-block-data')
  );
};

/**
 * NON TEXT ALIGNMENT
 */

export const handleNonTextAlignment = (textAlign, persistedState) => {
  // NOTE: We do this because when centering stuff other than text (button,
  // image, emoji), we aren't actually focusing on anything with the cursor
  // since going into edit mode focuses on the content already
  return persistedState.set('textAlign', textAlign);
};

/**
 * FONT SIZE
 */

const getFontSizeFromBlockType = blockType => {
  switch (blockType) {
    case TEXT_STYLE_TYPE.HEADER_ONE:
      return '32px';
    case TEXT_STYLE_TYPE.HEADER_TWO:
      return '24px';
    case TEXT_STYLE_TYPE.HEADER_THREE:
      return '20px';
    case TEXT_STYLE_TYPE.HEADER_FOUR:
      return '18px';
    case TEXT_STYLE_TYPE.HEADER_FIVE:
      return '16px';
    default:
      return DEFAULT_FONT_SIZE;
  }
};

/**
 * INLINE STYLE HANDLERS
 */

const getAndRemoveStyleFromSelection = (styleType, editorState, selection) => {
  // Get all of the styles from this chunk that begin with the
  // styleType prefix and add them to the running set of
  // style strings.
  const styles = getBlocksFromSelection(editorState).reduce((memo, block) => {
    let styleSet = memo;
    block.findStyleRanges(
      () => true,
      start => {
        styleSet = styleSet.union(
          block.getInlineStyleAt(start).filter(val => val.startsWith(styleType))
        );
      }
    );
    return styleSet;
  }, Set());

  // We should only allow one styleType per character, so remove any
  // existing instance of styleType from selected content before we apply
  // the new style
  const nextContentState = styles.reduce((contentState, style) => {
    return Modifier.removeInlineStyle(contentState, selection, style);
  }, editorState.getCurrentContent());
  const nextEditorState = EditorState.push(
    editorState,
    nextContentState,
    'change-inline-style'
  );

  return { nextContentState, nextEditorState };
};

export const updateInlineStyle = (value, editorState, styleType) => {
  const selection = editorState.getSelection();
  let { nextContentState, nextEditorState } = getAndRemoveStyleFromSelection(
    styleType,
    editorState,
    selection
  );

  // Get all current styleType styles to override for current styleType
  const currentSizeStyles = editorState
    .getCurrentInlineStyle()
    .filter(style => style.startsWith(styleType));

  if (selection.isCollapsed()) {
    nextEditorState = currentSizeStyles.reduce((state, style) => {
      return RichUtils.toggleInlineStyle(state, style);
    }, nextEditorState);
  }

  // Apply the new styleType
  nextEditorState = RichUtils.toggleInlineStyle(
    nextEditorState,
    styleType + value
  );

  // Apply styleType changes to link entities within the selected range
  const nextEditorSelection = nextEditorState.getSelection();
  const startOffset = nextEditorSelection.getStartOffset();
  const startKey = nextEditorSelection.getStartKey();
  const endOffset = nextEditorSelection.getEndOffset();
  const endKey = nextEditorSelection.getEndKey();
  nextContentState = nextEditorState.getCurrentContent();
  getBlocksFromSelection(nextEditorState).forEach(block => {
    findLinkEntities(
      block,
      (start, end) => {
        if (
          (block.getKey() === startKey && end <= startOffset) ||
          (block.getKey() === endKey && start >= endOffset)
        ) {
          return;
        }
        const linkKey = block.getEntityAt(start);
        if (linkKey) {
          nextContentState = nextContentState.mergeEntityData(linkKey, {
            [INLINE_STYLES[styleType].react]: value,
          });
        }
      },
      nextContentState
    );
  });
  if (!is(nextEditorState.getCurrentContent(), nextContentState)) {
    nextEditorState = EditorState.push(
      nextEditorState,
      nextContentState,
      'apply-inline-style'
    );
  }

  return nextEditorState;
};

const getDefaultStyle = (editorState, styleType) => {
  // Modal/slideout and tooltip/hotspot editors are different
  const $editor = getEditorDocument();

  if (editorState && styleType === CUSTOM_STYLE_PREFIX_FONT_SIZE) {
    const blockType = RichUtils.getCurrentBlockType(editorState) || 'unstyled';
    return getFontSizeFromBlockType(blockType);
  }

  // Once we have the selector from the main window, we need to jump into the
  // iframe's window and select a location where the theme's styleType style will be
  // applied
  const selection = editorState && editorState.getSelection();
  const anchorKey = selection && selection.getAnchorKey();

  // For new lines, the anchorKey isn't correct so if we can't generate a selector
  // with anchorKey, fallback to a more generic selector
  const editorFrameContent =
    $editor.querySelector(
      `.wysiwyg-container .public-DraftEditor-content [data-offset-key~="${anchorKey}-0-0"]`
    ) || $editor.querySelector('.wysiwyg-container');

  // Once we have the selector within the iframe, we get the computed style of it
  // and specifically the color value

  if (!INLINE_STYLES[styleType]) {
    return '';
  }

  return (
    window
      .getComputedStyle(editorFrameContent)
      // Need to split to get shortened font name and sometimes quotes show up
      .getPropertyValue(INLINE_STYLES[styleType].css)
  );
};

export const getCurrentStyle = (editorState, styleType) => {
  if (editorState) {
    const styles = editorState.getCurrentInlineStyle().toJS();
    if (styles.length > 0) {
      const styleIndex = styles.findIndex(
        style => style.indexOf(styleType) === 0
      );
      if (styleIndex !== -1) {
        return styles[styleIndex].slice(styleType.length);
      }
    }
  }
  return getDefaultStyle(editorState, styleType);
};
