import FormRatingButton from '../icons/FormRatingButton';
import FormRadioButton from '../icons/FormRadioButton';
import FormMultiSelectButton from '../icons/FormMultiSelectButton';
import FormLargeInput from '../icons/FormLargeInput';
import FormSmallInput from '../icons/FormSmallInput';

import EmojiBlockButton from '../icons/EmojiBlockButton';
import ImageBlockButton from '../icons/ImageBlockButton';
import VideoButton from '../icons/VideoButton';
import HeroButton from '../icons/HeroButton';
import CodeButton from '../icons/CodeButton';
import ButtonButton from '../icons/ButtonButton';

export const DRAGABLE_ITEMS = {
  ROW: 'row',
  ZONE: 'zone',
};

export const GALLERY_TYPES = {
  HERO: 'gallery',
};

export const BUTTON_TYPES = {
  PRIMARY: 'primary',
  SECONDARY: 'secondary',
};

export const EDITOR_TYPES = {
  TEXT: 'RichText',
  IMAGE: 'Image',
  HERO: 'Hero',
  VIDEO: 'Video',
  HTML: 'HTML',
  TEXTINPUT: 'TextInput',
  TEXTAREAINPUT: 'TextAreaInput',
  RADIO: 'SelectionField',
  MULTI_SELECT: 'MultiSelect',
  RATING: 'Rating',
  BUTTON: 'Button',
  EMOJI: 'Emoji',
  DISMISS_LINK: 'DismissLink',
};

export const PATTERN_TYPES = {
  MODAL: 'modal',
  HOTSPOT_GROUP: 'hotspot-group',
  TOOLTIP_GROUP: 'tooltip-group',
  SLIDEOUT: 'shorty',
};

export const categories = [
  {
    name: 'Text',
    content: [EDITOR_TYPES.TEXT],
  },
  {
    name: 'Image',
    content: [EDITOR_TYPES.IMAGE],
    icon: ImageBlockButton,
  },
  {
    name: 'Hero',
    content: [EDITOR_TYPES.HERO],
    icon: HeroButton,
  },
  {
    name: 'Button',
    content: [EDITOR_TYPES.BUTTON],
    icon: ButtonButton,
  },
  {
    name: 'Video',
    content: [EDITOR_TYPES.VIDEO],
    icon: VideoButton,
  },
  {
    name: 'Emoji',
    content: [EDITOR_TYPES.EMOJI],
    icon: EmojiBlockButton,
  },
  {
    name: 'Rating',
    content: [EDITOR_TYPES.RATING],
    icon: FormRatingButton,
  },
  {
    name: 'Radio Select',
    content: [EDITOR_TYPES.RADIO],
    icon: FormRadioButton,
  },
  {
    name: 'Multi-Select',
    content: [EDITOR_TYPES.MULTI_SELECT],
    icon: FormMultiSelectButton,
  },
  {
    name: 'Small Input',
    content: [EDITOR_TYPES.TEXTINPUT],
    icon: FormSmallInput,
  },
  {
    name: 'Large Input',
    content: [EDITOR_TYPES.TEXTAREAINPUT],
    icon: FormLargeInput,
  },
  {
    name: 'HTML',
    content: [EDITOR_TYPES.HTML],
    icon: CodeButton,
  },
];

export const BUTTON_ACTION_TYPES = {
  URL: 'url',
  NEXT_PAGE: 'next',
  PREVIOUS_PAGE: 'prev',
  CUSTOM_PAGE: 'custom',
  END_FLOW: 'skip',
  NEXT_GROUP: 'end',
  END_STEP_AND_FLOW: 'end-flow',
  APPCUES: 'appcues',
  SKIP_AND_END_FLOW: 'skip-and-end-flow',
};

export const BUTTON_ACTIONS_WITH_DATA_STEP_ATTRS = [
  BUTTON_ACTION_TYPES.NEXT_PAGE,
  BUTTON_ACTION_TYPES.PREVIOUS_PAGE,
  BUTTON_ACTION_TYPES.NEXT_GROUP,
];

export const BUTTON_ACTION_TYPES_LIST = [
  {
    label: 'Next step',
    value: BUTTON_ACTION_TYPES.NEXT_PAGE,
  },
  {
    label: 'Previous step',
    value: BUTTON_ACTION_TYPES.PREVIOUS_PAGE,
  },
  {
    label: 'Go to URL',
    value: BUTTON_ACTION_TYPES.URL,
  },
  {
    label: 'Trigger Flow',
    value: BUTTON_ACTION_TYPES.APPCUES,
  },
  {
    label: 'Dismiss Flow',
    value: BUTTON_ACTION_TYPES.END_FLOW,
  },
  {
    label: 'Go to custom step',
    value: BUTTON_ACTION_TYPES.CUSTOM_PAGE,
  },
  {
    label: 'Skip to next group',
    value: BUTTON_ACTION_TYPES.NEXT_GROUP,
  },
];

export const disabledActionsByPatternsType = {
  [PATTERN_TYPES.HOTSPOT_GROUP]: [
    BUTTON_ACTION_TYPES.CUSTOM_PAGE,
    BUTTON_ACTION_TYPES.PREVIOUS_PAGE,
    BUTTON_ACTION_TYPES.NEXT_PAGE,
  ],
};

export const IMG_ACTION_TYPES = {
  GO_TO_URL: 'url',
  SHOW_APPCUES_FLOW: 'appcues',
};

export const INPUT_TYPES = {
  TEXT: 'text',
  EMAIL: 'email',
  NUMBER: 'number',
  DATE: 'date',
  PHONE: 'tel',
  URL: 'url',
};

export const ALIGNMENT_TYPE = {
  LEFT: 'left',
  CENTER: 'center',
  RIGHT: 'right',
};

export const INLINE_STYLE_TYPE = {
  BOLD: 'BOLD',
  ITALIC: 'ITALIC',
  UNDERLINE: 'UNDERLINE',
};

export const LIST_STYLE_TYPE = {
  UNORDERED_LIST_ITEM: 'unordered-list-item',
  ORDERED_LIST_ITEM: 'ordered-list-item',
};

export const TEXT_STYLE_TYPE = {
  UNSTYLED: 'unstyled',
  HEADER_ONE: 'header-one',
  HEADER_TWO: 'header-two',
  HEADER_THREE: 'header-three',
  HEADER_FOUR: 'header-four',
  HEADER_FIVE: 'header-five',
};

export const INPUT_TYPES_LIST = [
  {
    label: 'Text',
    value: 'text',
  },
  {
    label: 'Number',
    value: 'number',
  },
  {
    label: 'Date',
    value: 'date',
  },
  {
    label: 'E-mail',
    value: 'email',
  },
  {
    label: 'Phone',
    value: 'tel',
  },
  {
    label: 'URL',
    value: 'url',
  },
];

export const MAX_ZONES = 6;

export const DEFAULT_USER_PROPS = [
  'userId',
  '_userAgent',
  '_updatedAt',
  '_sessionRandomizer',
  '_sessionPageviews',
  '_operatingSystem',
  '_myAppcuesId',
  '_localId',
  '_lastPageUrl',
  '_lastPageTitle',
  '_lastContentShownAt',
  '_lastBrowserLanguage',
  '_isAnonymous',
  '_hostname',
  '_deviceType',
  '_currentPageUrl',
  '_currentPageTitle',
  '_browser',
  '_appcuesId',
  '_ABGroup',
];

export const DEFAULT_BUTTON_TEXT = 'Next';
export const DEFAULT_HOTSPOT_BUTTON_TEXT = 'Button';
