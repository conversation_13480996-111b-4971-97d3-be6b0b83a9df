import htmlParseStringify2 from 'html-parse-stringify2';
import { buildConditionalString } from './styles/editor';

export const parseContent = content => {
  // the regex applied finds any <p> or </p> tag
  // or ⊘ symbol
  return content
    .replace(new RegExp(/<\/?p>/, 'gm'), '')
    .replace(new RegExp(/⊘/, 'gm'), '<span aria-hidden="true">⊘</span>');
};

export const parseAriaLabel = content =>
  content
    .replace(new RegExp(/<[^>]*>/, 'gm'), '')
    .replace(new RegExp(/⊘/, 'gm'), '')
    .trim();

export const generateHTML = persistedState => {
  const {
    marginTop,
    marginRight,
    marginBottom,
    marginLeft,
    height,
    width,
    content = '',
  } = persistedState.toJS();
  const ariaLabel = parseAriaLabel(content);

  const styles = buildConditionalString({
    [`height:${height};`]: height,
    [`width:${width};`]: width,
    [`margin-top:${marginTop}px;`]: marginTop,
    [`margin-right:${marginRight}px;`]: marginRight,
    [`margin-bottom:${marginBottom}px;`]: marginBottom,
    [`margin-left:${marginLeft}px;`]: marginLeft,
  });

  const styleAttr = styles && styles.length ? ` style="${styles}"` : '';

  const html = `<div class="dismiss-link"${styleAttr}>
        <style>.dismiss-link strong{color:inherit !important;}</style>
        <a rel="noreferrer" class="text-muted appcues-skip" data-step="skip" role="button" tabindex="0" aria-label="${ariaLabel}">
          ${parseContent(content)}
        </a>
      </div>`;
  return html;
};

export const generateDismissLinkAST = ({ persistedState }) => {
  return htmlParseStringify2.parse(generateHTML(persistedState));
};
