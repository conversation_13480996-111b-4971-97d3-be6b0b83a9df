export default {
  // Rows
  ROWS_ADD_ONE: 'ROWS_ADD_ONE',
  ROWS_ADD_MANY: 'ROWS_ADD_MANY',
  UNSHIFT_ROW: 'UNSHIFT_ROW',
  ROWS_REPLACE_ALL: 'ROWS_REPLACE_ALL',
  ROWS_REMOVE_ONE: 'ROWS_REMOVE_ONE',
  ROWS_INSERT_ZONE: 'ROWS_INSERT_ZONE',
  ROWS_REMOVE_ONE_ZONE: 'ROWS_REMOVE_ONE_ZONE',

  // ZONES
  ZONES_ADD_ONE: 'ZONES_ADD_ONE',
  ZONES_UPDATE_HTML: 'ZONES_UPDATE_HTML',
  ZONES_REMOVE_ONE: 'ZONES_REMOVE_ONE',
  ZONE_MOVED: 'ZONE_MOVED',
  ZONE_MOVED_TO_NEW_ROW: 'ZONE_MOVED_TO_NEW_ROW',

  // Editor Selector
  EDITOR_SELECTOR_SHOW: 'EDITOR_SELECTOR_SHOW',
  EDITOR_SELECTOR_HIDE: 'EDITOR_SELECTOR_HIDE',
  EDITOR_SELECTOR_SET_POSITION: 'EDITOR_SELECTOR_SET_POSITION',

  // Editor
  EDITOR_SET_CANVAS_POSITION: 'EDITOR_SET_CANVAS_POSITION',
  EDITOR_EDITING_START: 'EDITOR_EDITING_START',
  EDITOR_EDITING_CANCEL: 'EDITOR_EDITING_CANCEL',
  EDITOR_UPDATE_ZONE: 'EDITOR_UPDATE_ZONE',
  EDITOR_UPDATE_DRAFT: 'EDITOR_UPDATE_DRAFT',
  EDITOR_ACTIONS_TOGGLE: 'EDITOR_ACTIONS_TOGGLE',
  EDITOR_ACTIONS_TOGGLE_IF_CURRENT: 'EDITOR_ACTIONS_TOGGLE_IF_CURRENT',
  EDITOR_INLINE_ACTIONS_TOGGLE: 'EDITOR_INLINE_ACTIONS_TOGGLE',
  EDITOR_MOVING_ROW_START: 'EDITOR_MOVING_ROW_START',
  EDITOR_MOVING_ROW_END: 'EDITOR_MOVING_ROW_END',
  EDITOR_MOVE_ROW: 'EDITOR_MOVE_ROW',
  EDITOR_SETTINGS_CLOUDINARY: 'EDITOR_SETTINGS_CLOUDINARY',
  EDITOR_SET_BASE_PADDING: 'EDITOR_SET_BASE_PADDING',
  EDITOR_SETTINGS_USER_PROPERTIES: 'EDITOR_SETTINGS_USER_PROPERTIES',
  EDITOR_SETTINGS_SANITIZE_HTML: 'EDITOR_SETTINGS_SANITIZE_HTML',
  EDITOR_SETTINGS_ALLOWED_EDITOR_TYPES: 'EDITOR_SETTINGS_ALLOWED_EDITOR_TYPES',
  EDITOR_SETTINGS_DISABLE_ADD_BUTTON: 'EDITOR_SETTINGS_DISABLE_ADD_BUTTON',
  EDITOR_SETTINGS_ACE_EDITOR: 'EDITOR_SETTINGS_ACE_EDITOR',
  EDITOR_SHOULD_DISABLE_XSS: 'EDITOR_SHOULD_DISABLE_XSS',

  ADD_FLOWS: 'ADD_FLOWS',
};
