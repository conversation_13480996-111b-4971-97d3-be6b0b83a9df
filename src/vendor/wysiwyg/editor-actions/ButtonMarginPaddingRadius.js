import React from 'react';
import styled from 'styled-components';
import PropTypes from 'prop-types';
import { Map } from 'immutable';

import RangeInput from 'ext/components/ui/RangeInput';
import MarginPaddingEditor from 'ext/components/MarginPaddingEditor';

import ActionsMenu from '../components/ActionsMenu';
import MarginButton from '../icons/MarginButton';
import { animateWhenTogglingOff } from '../helpers/utils';
import { getButtonProps } from '../helpers/styles/editor';
import { getDefaultPadding } from '../helpers/buttons';

const POSITION_TO_STYLE_MAP = {
  padding: {
    left: 'paddingLeft',
    top: 'paddingTop',
    right: 'paddingRight',
    bottom: 'paddingBottom',
  },
  margin: {
    left: 'marginLeft',
    top: 'marginTop',
    right: 'marginRight',
    bottom: 'marginBottom',
  },
};

const Content = styled.div`
  display: grid;
  grid-row: auto;
  gap: 14px;
`;

const activeButtonProps = getButtonProps(true);
const inactiveButtonProps = getButtonProps(false);

const ButtonMarginPaddingRadius = ({
  isActive,
  onToggleActive,
  hasRoomToRenderBelow,
  persistedState,
  onChange,
  localState,
  patternType,
  track,
}) => {
  const buttonProps = isActive ? activeButtonProps : inactiveButtonProps;

  const toggleDropdown = e => {
    e.preventDefault();

    if (isActive) {
      animateWhenTogglingOff(() => onToggleActive(!isActive));
    } else {
      onToggleActive(!isActive);
    }
  };

  const handleOnUpdate = prefix => (position, value) => {
    const newPersistedState = persistedState.set(
      POSITION_TO_STYLE_MAP[prefix][position],
      parseInt(value || 0)
    );

    track('Builder interaction', {
      name: `Button - Updated ${prefix}`,
      component: 'EditorActions/ButtonsContainerStyles',
    });

    onChange({
      localState,
      persistedState: newPersistedState,
    });
  };

  const handleOnBorderRadiusUpdate = value => {
    const newPersistedState = persistedState.set(`borderRadius`, value);

    track('Builder interaction', {
      name: 'Button - Updated Corner Radius',
      component: 'EditorActions/ButtonsContainerStyles',
    });

    onChange({
      localState,
      persistedState: newPersistedState,
    });
  };

  const { defaultHorizontalPadding, defaultVerticalPadding } =
    getDefaultPadding(patternType);

  // padding here is used to keep padding backwards compatible since
  // in the past users were able to set only one value for padding
  // setting the padding values (top, right, bottom, left)
  // with the same value.
  const currentPaddingValue = persistedState.get('padding');
  const defaultXPadding = currentPaddingValue || defaultHorizontalPadding;
  const defaultYPadding = currentPaddingValue || defaultVerticalPadding;

  const padding = {
    left: persistedState.get('paddingLeft') ?? defaultXPadding,
    top: persistedState.get('paddingTop') ?? defaultYPadding,
    right: persistedState.get('paddingRight') ?? defaultXPadding,
    bottom: persistedState.get('paddingBottom') ?? defaultYPadding,
  };

  const margin = {
    left: persistedState.get('marginLeft') ?? 0,
    top: persistedState.get('marginTop') ?? 0,
    right: persistedState.get('marginRight') ?? 0,
    bottom: persistedState.get('marginBottom') ?? 0,
  };

  const persistedBorderRadius = persistedState.get('borderRadius');
  const borderRadius = persistedBorderRadius
    ? parseInt(persistedBorderRadius)
    : 3;

  return (
    <>
      <MarginButton
        isMenuButton
        isActive={isActive}
        onClick={toggleDropdown}
        {...buttonProps}
      />

      {isActive && (
        <ActionsMenu
          isOpen
          position={hasRoomToRenderBelow ? 'bottom' : 'top'}
          role="toolbar"
          aria-label="Button margin, padding and border radius"
        >
          <Content>
            <MarginPaddingEditor
              onMarginUpdate={handleOnUpdate('margin')}
              onPaddingUpdate={handleOnUpdate('padding')}
              margin={margin}
              padding={padding}
            />

            <RangeInput
              min={0}
              max={100}
              value={borderRadius}
              onChange={handleOnBorderRadiusUpdate}
              label="Corner Radius"
            />
          </Content>
        </ActionsMenu>
      )}
    </>
  );
};

ButtonMarginPaddingRadius.propTypes = {
  localState: PropTypes.instanceOf(Map),
  persistedState: PropTypes.instanceOf(Map),
  onChange: PropTypes.func,
  onToggleActive: PropTypes.func,
  isActive: PropTypes.bool,
  hasRoomToRenderBelow: PropTypes.bool,
  track: PropTypes.func,
};

export default ButtonMarginPaddingRadius;
