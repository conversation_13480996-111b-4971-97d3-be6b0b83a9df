import React, { useEffect, useRef, useState } from 'react';
import PropTypes from 'prop-types';
import styled from 'styled-components';
import FontIcon from '../../components/FontAwesome/FontIcon';

const bounds = {
  LOWER: 1,
  UPPER: 400,
};

const Input = styled.input`
  border: none;
  border-bottom-left-radius: 6px;
  border-top-left-radius: 6px;
  color: #7e89a9;
  font-size: var(--regular);
  height: 32px;
  outline: none;
  padding-left: 8px;
  text-align: center;
  width: 42px;

  &::-webkit-inner-spin-button,
  &::-webkit-outer-spin-button {
    -webkit-appearance: none;
    margin: 0;
  }

  ${({ isActive }) =>
    isActive &&
    `
    color: #5c5cff;
    background: rgb(222, 222, 255);
`}
`;

const Handle = styled.span`
  align-items: center;
  border-bottom-right-radius: 6px;
  border-top-right-radius: 6px;
  cursor: pointer;
  display: flex;
  height: 32px;
  justify-content: center;
  padding: 0 4px;

  && > svg {
    width: 8px;
    path {
      color: #7e89a9;
    }
  }

  ${({ isActive }) =>
    isActive &&
    `
    background: rgb(222, 222, 255);
`}
`;

const Wrapper = styled.span`
  align-content: center;
  display: flex;
  padding: 8px 4px;

  &:hover {
    ${Input}, ${Handle} {
      background: ${props =>
        props.isActive ? 'rgb(222,222,255)' : 'rgb(239,239,255)'};
    }

    ${Input} {
      color: #5c5cff;
    }
  }
`;

/*
 * constrain number to lower and upper bounds
 *
 * @param {number} number - value to constrain
 * @returns {number} constained value
 */
const constrain = number =>
  Math.min(bounds.UPPER, Math.max(bounds.LOWER, number));

const FontSizeInput = ({ isActive, onChange, onToggleMenu, value }) => {
  const ref = useRef();
  const [staged, setStaged] = useState(value);
  const [actual, setActual] = useState(value);

  /**
   * NOTE: when a value is set using the dropdown after a value has already been
   * set before using the input, the internal state of this component becomes
   * out of sync since we use the staged value as the display label. this checks
   * that the last known set value equals the provided prop value and updates
   * the internal states to ensure that changes made by the dropdown menu are
   * applied here as well.
   */
  useEffect(() => {
    if (actual !== Number.parseInt(value, 10)) {
      setStaged(value);
      setActual(value);
    }
  }, [actual, setActual, setStaged, value]);

  /*
   * NOTE: Add handlers to close menu and blur input on Enter
   */
  useEffect(() => {
    const $input = ref.current;

    const submit = event => {
      if (event.key === 'Enter') {
        event.preventDefault();
        onToggleMenu();
        $input.blur();
      }
    };

    $input.addEventListener('keydown', submit);

    return () => {
      $input.removeEventListener('keydown', submit);
    };
  }, []); // eslint-disable-line react-hooks/exhaustive-deps

  const handleChange = ({ target }) => {
    setStaged(target.value);
  };

  const handleBlur = () => {
    const parsed = Number.parseInt(staged, 10);
    const constrained = constrain(parsed || value);
    setStaged(constrained);
    setActual(constrained);
    onChange(constrained);
  };

  const handleClick = ({ target }) => {
    const $input = ref.current;

    // open menu if currently closed or handler clicked
    if (!isActive || (isActive && target !== $input)) {
      onToggleMenu();
    }

    // focus input if menu closed or input clicked
    if (!isActive || (isActive && target === $input)) {
      $input.focus();
    }
  };

  return (
    <Wrapper isActive={isActive}>
      <Input
        ref={ref}
        isActive={isActive}
        max={bounds.UPPER}
        min={bounds.LOWER}
        onBlur={handleBlur}
        onChange={handleChange}
        onClick={handleClick}
        role="textbox"
        type="number"
        value={staged}
      />
      <Handle isActive={isActive} onClick={handleClick} role="button">
        <FontIcon icon="caret-down" />
      </Handle>
    </Wrapper>
  );
};

FontSizeInput.propTypes = {
  isActive: PropTypes.bool,
  onChange: PropTypes.func,
  onToggleMenu: PropTypes.func,
  value: PropTypes.string,
};

export default FontSizeInput;
