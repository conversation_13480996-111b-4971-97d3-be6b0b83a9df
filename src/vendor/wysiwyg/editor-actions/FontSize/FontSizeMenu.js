import React from 'react';
import PropTypes from 'prop-types';
import ActionsMenu from 'vendor/wysiwyg/components/ActionsMenu';

const fontSizes = [
  { value: '8' },
  { value: '9' },
  { value: '10' },
  { value: '11' },
  { value: '12' },
  { value: '14' },
  { value: '18' },
  { value: '24' },
  { value: '30' },
  { value: '36' },
  { value: '48' },
  { value: '60' },
  { value: '72' },
  { value: '96' },
];

export default class FontSizeMenu extends React.PureComponent {
  handleChange = size => {
    const { onChange, onToggleMenu } = this.props;
    onToggleMenu();
    onChange(size);
  };

  render() {
    const { hasRoomToRenderBelow, selectedFontSize } = this.props;

    const actionsMenuProps = {
      isOpen: true,
      position: hasRoomToRenderBelow ? 'bottom' : 'top',
      style: {
        padding: '0px',
        width: '60px',
        height: '290px',
        overflowY: 'scroll',
      },
    };

    return (
      <ActionsMenu {...actionsMenuProps}>
        <div style={{ margin: '-10px 0' }}>
          {fontSizes.map(sizeOption => {
            return (
              <ActionsMenu.MenuItem
                isFontSizeMenuItem
                key={sizeOption.value}
                onClick={() => this.handleChange(sizeOption.value)}
                selected={selectedFontSize === sizeOption.value}
                data-testid={`font-size-option-${sizeOption.value}`}
              >
                {sizeOption.value}
              </ActionsMenu.MenuItem>
            );
          })}
        </div>
      </ActionsMenu>
    );
  }
}

FontSizeMenu.propTypes = {
  selectedFontSize: PropTypes.string,
  hasRoomToRenderBelow: PropTypes.bool,
  onChange: PropTypes.func,
  onToggleMenu: PropTypes.func,
};
