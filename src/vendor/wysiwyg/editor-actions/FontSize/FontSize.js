import React from 'react';
import PropTypes from 'prop-types';
import { Map } from 'immutable';
import {
  getCurrentStyle,
  updateInlineStyle,
} from 'vendor/wysiwyg/helpers/draft/actions';
import { animateWhenTogglingOff } from 'vendor/wysiwyg/helpers/utils';
import { CUSTOM_STYLE_PREFIX_FONT_SIZE } from 'vendor/wysiwyg/helpers/draft/convert';
import FontSizeMenu from './FontSizeMenu';
import FontSizeInput from './FontSizeInput';

export default class FontSize extends React.Component {
  toggleDropdown = () => {
    const { onToggleActive, isActive } = this.props;

    if (isActive) {
      animateWhenTogglingOff(() => onToggleActive(!isActive));
    } else {
      onToggleActive(!isActive);
    }
  };

  /**
   * change handler for input and menu
   *
   * @param {number} value - selected or input value
   * @param {boolean} open - whether to keep the menu open
   * @returns {void}
   */
  handleFormat = value => {
    const { localState, persistedState, onChange } = this.props;
    const editorState = localState.get('editorState');
    const nextEditorState = updateInlineStyle(
      `${value}px`,
      editorState,
      CUSTOM_STYLE_PREFIX_FONT_SIZE
    );
    const newLocalState = localState.set('editorState', nextEditorState);

    onChange({
      localState: newLocalState,
      persistedState,
    });
  };

  render() {
    const { isActive, hasRoomToRenderBelow, localState } = this.props;
    const currentValue = getCurrentStyle(
      localState.get('editorState'),
      CUSTOM_STYLE_PREFIX_FONT_SIZE
    );

    const formattedCurrentValue = currentValue.replace('px', '');

    return (
      <>
        <FontSizeInput
          isActive={isActive}
          onChange={this.handleFormat}
          onToggleMenu={this.toggleDropdown}
          value={formattedCurrentValue}
        />
        {isActive && (
          <FontSizeMenu
            selectedFontSize={formattedCurrentValue}
            hasRoomToRenderBelow={hasRoomToRenderBelow}
            onChange={this.handleFormat}
            onToggleMenu={this.toggleDropdown}
          />
        )}
      </>
    );
  }
}

FontSize.propTypes = {
  localState: PropTypes.instanceOf(Map),
  persistedState: PropTypes.instanceOf(Map),
  onChange: PropTypes.func,
  onToggleActive: PropTypes.func,
  isActive: PropTypes.bool,
  hasRoomToRenderBelow: PropTypes.bool,
};
