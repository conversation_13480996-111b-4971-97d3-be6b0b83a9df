import React from 'react';
import PropTypes from 'prop-types';
import { Map } from 'immutable';

import { getButtonProps, ActionsItem } from '../helpers/styles/editor';
import ImageUploader from '../components/ImageUploader';

import FileUploadButton from '../icons/FileUploadButton';

export default class ImageUpload extends React.Component {
  handleUpload(imageDetails) {
    const { url, width } = imageDetails;
    const {
      localState,
      persistedState,
      onChange,
      maxWidth,
      setIsSelectorOpen,
      isHero,
    } = this.props;

    const urlWithoutProtocol = url.replace(/^https?:\/\//i, '//');

    let newPersistedState = persistedState
      .set('url', urlWithoutProtocol)
      .set('width', width)
      .set('textAlign', 'center');

    if (isHero) {
      newPersistedState = newPersistedState.set('backgroundType', 'url');
    }

    // Make sure the uploaded image does not have a larger size than available
    if (maxWidth && width > maxWidth) {
      newPersistedState = newPersistedState.set('widthOverride', maxWidth);
    }
    const newLocalState = localState.set('isUploading', false);

    onChange({
      localState: newLocalState,
      persistedState: newPersistedState,
    });

    setIsSelectorOpen(false);
  }

  handleLoading = () => {
    const { localState, persistedState, onChange } = this.props;
    const newLocalState = localState.set('isUploading', true);

    onChange({
      localState: newLocalState,
      persistedState,
    });
  };

  handleError = error => {
    const { localState, persistedState, onChange } = this.props;
    const newLocalState = localState.set('isError', error);

    onChange({
      localState: newLocalState,
      persistedState,
    });
  };

  render() {
    const buttonProps = getButtonProps(false);

    return (
      <ImageUploader
        onError={this.handleError}
        onBeforeUpload={this.handleLoading}
        onUpload={imageDetails => this.handleUpload(imageDetails)}
        disableText
      >
        <ActionsItem>
          <FileUploadButton onClick={() => {}} {...buttonProps} />
          <span>Upload image</span>
        </ActionsItem>
      </ImageUploader>
    );
  }
}

ImageUpload.propTypes = {
  localState: PropTypes.instanceOf(Map),
  persistedState: PropTypes.instanceOf(Map),
  onChange: PropTypes.func,
  maxWidth: PropTypes.number,
  setIsSelectorOpen: PropTypes.func,
};
