import React from 'react';
import PropTypes from 'prop-types';
import { Map } from 'immutable';
import styled from 'styled-components';
import ActionsMenu from '../components/ActionsMenu';
import ButtonTypeButton from '../icons/ButtonTypeButton';

import { BUTTON_TYPES } from '../helpers/constants';
import { animateWhenTogglingOff } from '../helpers/utils';

const Wrapper = styled.div`
  &:hover svg path {
    fill: #5c5cff;
  }
`;

// https://draftjs.org/docs/advanced-topics-custom-block-render-map.html
const typeOptions = [
  {
    name: 'Primary',
    value: BUTTON_TYPES.PRIMARY,
  },
  {
    name: 'Secondary',
    value: BUTTON_TYPES.SECONDARY,
  },
];

export const RESET_STYLES = [
  'inlineBackgroundColor',
  'inlineDropShadow',
  'inlineBorderColor',
  'inlineBorderWidth',
  'hoverDropShadow',
  'hoverBackgroundColor',
  'hoverBorderColor',
  'hoverBorderWidth',
  'fontColor',
  'fontSize',
  'fontFamily',
  'isBold',
  'isItalic',
  'isUnderlined',
  'lineHeight',
  'letterSpacing',
  'textTransform',
  'marginTop',
  'marginRight',
  'marginBottom',
  'marginLeft',
  'paddingTop',
  'paddingBottom',
  'paddingLeft',
  'paddingRight',
];

const Item = styled.li`
  background-color: transparent;
  border: none;
  color: var(--background);
  cursor: pointer;
  display: block;
  font-weight: var(--regular);
  padding: 8px 8px 8px 24px;
  transition: background-color 0.15s ease-out;
  width: 100%;

  &:hover {
    background-color: var(--light-text-color);
  }

  ${props =>
    props.isActive &&
    `
    color: var(--secondary);
    font-weight: var(--bold);
  `};
`;

export default class ButtonType extends React.Component {
  constructor(props) {
    super(props);

    this.state = {
      isMenuOpen: props.isActive || false,
    };
  }

  componentDidUpdate(prevProps) {
    const { isActive } = this.props;

    if (prevProps.isActive !== isActive) {
      this.setState({
        isMenuOpen: isActive,
      });
    }
  }

  toggleDropdown() {
    const { onToggleActive, isActive } = this.props;
    this.setState(({ isMenuOpen }) => ({
      isMenuOpen: !isMenuOpen,
    }));

    if (isActive) {
      animateWhenTogglingOff(() => onToggleActive(!isActive));
    } else {
      onToggleActive(!isActive);
    }
  }

  handleFormat(selectedValue) {
    const { localState, persistedState, onChange, onToggleActive, track } =
      this.props;

    const newPersistedState = persistedState
      .withMutations(map => {
        RESET_STYLES.forEach(style => map.delete(style));
      })
      .set('buttonType', selectedValue);

    track('Builder interaction', {
      name: 'Button - Updated type',
      type: selectedValue,
    });

    this.setState({
      isMenuOpen: false,
    });

    animateWhenTogglingOff(() => onToggleActive(false));

    onChange({
      localState,
      persistedState: newPersistedState,
    });
  }

  render() {
    const { isMenuOpen } = this.state;
    const { blockType, isActive, hasRoomToRenderBelow } = this.props;

    const actionsMenuProps = {
      isOpen: isMenuOpen,
      position: hasRoomToRenderBelow ? 'bottom' : 'top',
      style: {
        padding: '8px 0',
        width: '200px',
      },
    };

    return (
      <Wrapper>
        <ButtonTypeButton
          isActive={isActive}
          blockType={blockType}
          onClick={() => this.toggleDropdown()}
        />
        {isActive ? (
          <ActionsMenu {...actionsMenuProps}>
            {typeOptions.map((typeOption, i) => {
              const isActive = blockType === typeOption.value;
              return (
                <Item
                  isActive={isActive}
                  onClick={() => this.handleFormat(typeOption.value)}
                  key={i}
                >
                  {typeOption.name}
                </Item>
              );
            })}
          </ActionsMenu>
        ) : null}
      </Wrapper>
    );
  }
}

ButtonType.propTypes = {
  localState: PropTypes.instanceOf(Map),
  persistedState: PropTypes.instanceOf(Map),
  onChange: PropTypes.func,
  onToggleActive: PropTypes.func,
  isActive: PropTypes.bool,
  hasRoomToRenderBelow: PropTypes.bool,
  blockType: PropTypes.string,
  track: PropTypes.func,
};
