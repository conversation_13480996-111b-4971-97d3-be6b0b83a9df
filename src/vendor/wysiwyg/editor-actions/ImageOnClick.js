import React from 'react';
import PropTypes from 'prop-types';
import { Map } from 'immutable';

import {
  getButtonProps,
  inputStyle,
  checkboxStyle,
  labelStyle,
  menuTextStyle,
  flexColumn,
  tabStyle,
  selectedTabStyle,
  buttonNavTypeWrapperStyle,
  buttonNavTypeMenuStyle,
} from '../helpers/styles/editor';
import { IMG_ACTION_TYPES } from '../helpers/constants';
import ActionsMenu from '../components/ActionsMenu';
import { animateWhenTogglingOff } from '../helpers/utils';

import LinkButton from '../icons/LinkButton';

export default class ImageOnClick extends React.Component {
  constructor(props) {
    super(props);

    this.state = {
      isMenuOpen: props.isActive || false,
      actionType: props.flowId
        ? IMG_ACTION_TYPES.SHOW_APPCUES_FLOW
        : IMG_ACTION_TYPES.GO_TO_URL,
      href: props.href || '',
      isNewWindow: props.isNewWindow || false,
      flowId: '',
    };
  }

  componentDidUpdate(prevProps, prevState) {
    const { persistedState } = this.props;
    const href = persistedState.get('href');
    const isNewWindow = persistedState.get('isNewWindow');
    const flowId = persistedState.get('flowId');

    const isHrefSet = href && !prevState.href && href !== prevState.href;
    if (isHrefSet) {
      this.setState({ href });
    }

    const isNewWindowSet = isNewWindow && isNewWindow !== prevState.isNewWindow;
    if (isNewWindowSet) {
      this.setState({ isNewWindow });
    }

    const isFlowIdSet =
      flowId && !prevState.flowId && flowId !== prevState.flowId;
    if (isFlowIdSet) {
      this.setState({
        flowId,
        actionType: IMG_ACTION_TYPES.SHOW_APPCUES_FLOW,
      });
    }
  }

  toggleDropdown() {
    const { onToggleActive, isActive } = this.props;

    this.setState(({ isMenuOpen }) => ({
      isMenuOpen: !isMenuOpen,
    }));

    if (isActive) {
      animateWhenTogglingOff(() => onToggleActive(!isActive));
    } else {
      onToggleActive(!isActive);
    }
  }

  selectOnClickAction(actionType) {
    this.setState({ actionType });
  }

  handleHrefInput(e) {
    const href = e.target.value;
    this.setState(
      {
        href,
      },
      this.saveAction
    );
  }

  handleClick(e) {
    e.target.focus();
  }

  handleIsNewWindow(e) {
    const isNewWindow = e.target.checked;
    this.setState(
      {
        isNewWindow,
      },
      this.saveAction
    );
  }

  handleFlowIdInput(e) {
    const { value } = e.target;
    this.setState(
      {
        flowId: value,
      },
      this.saveAction
    );
  }

  getNewState(currentActionType) {
    const { persistedState } = this.props;
    const { flowId, href, isNewWindow } = this.state;

    switch (currentActionType) {
      case IMG_ACTION_TYPES.GO_TO_URL:
        return persistedState
          .set('href', href)
          .set('isNewWindow', isNewWindow)
          .delete('flowId');
      case IMG_ACTION_TYPES.SHOW_APPCUES_FLOW:
        return persistedState
          .set('flowId', flowId)
          .delete('href')
          .delete('isNewWindow');
      default:
        return persistedState;
    }
  }

  saveAction() {
    const { localState, onChange } = this.props;
    const { actionType, flowId, href } = this.state;

    // Prevent user from losing flowId when saving on URL tab with blank URL
    if (actionType === IMG_ACTION_TYPES.GO_TO_URL && !href && flowId) {
      return;
    }

    // Prevent user from losing URL when saving on Appcues Show tab with blank flowId
    if (actionType === IMG_ACTION_TYPES.SHOW_APPCUES_FLOW && !flowId && href) {
      return;
    }

    const newPersistedState = this.getNewState(actionType);

    onChange({
      localState,
      persistedState: newPersistedState,
    });
  }

  render() {
    const { isActive, hasRoomToRenderBelow } = this.props;
    const { isMenuOpen, actionType, flowId, href, isNewWindow } = this.state;

    const buttonProps = getButtonProps(isActive);

    const row = {
      marginTop: 10,
      display: 'flex',
    };

    const actionsMenuProps = {
      isOpen: isMenuOpen,
      position: hasRoomToRenderBelow ? 'bottom' : 'top',
      // NOTE: possible 55 offset to bottom
    };

    const dropdownNodes = isActive && (
      <ActionsMenu {...actionsMenuProps}>
        <div style={buttonNavTypeWrapperStyle}>
          <div
            onClick={() => this.selectOnClickAction(IMG_ACTION_TYPES.GO_TO_URL)}
            style={{
              ...tabStyle,
              ...(actionType === IMG_ACTION_TYPES.GO_TO_URL
                ? selectedTabStyle
                : {}),
            }}
          >
            Go to URL
          </div>
          <div
            onClick={() =>
              this.selectOnClickAction(IMG_ACTION_TYPES.SHOW_APPCUES_FLOW)
            }
            style={{
              ...tabStyle,
              ...(actionType === IMG_ACTION_TYPES.SHOW_APPCUES_FLOW
                ? selectedTabStyle
                : {}),
            }}
          >
            Trigger Flow
          </div>
        </div>
        <div style={buttonNavTypeMenuStyle}>
          {actionType === IMG_ACTION_TYPES.GO_TO_URL && (
            <div>
              <div style={{ ...row, flexDirection: 'column' }}>
                <label style={labelStyle}>URL</label>
                <input
                  type="text"
                  style={inputStyle}
                  value={href}
                  onClickCapture={this.handleClick}
                  onChange={e => this.handleHrefInput(e)}
                />
              </div>
              <div style={{ ...row, alignItems: 'center' }}>
                <input
                  id="link-checkbox"
                  type="checkbox"
                  style={checkboxStyle}
                  checked={isNewWindow}
                  onChange={e => this.handleIsNewWindow(e)}
                />
                <label style={menuTextStyle} htmlFor="link-checkbox">
                  Open In New Window
                </label>
              </div>
            </div>
          )}
          {actionType === IMG_ACTION_TYPES.SHOW_APPCUES_FLOW && (
            <div>
              <div style={flexColumn}>
                <label style={labelStyle}>Flow ID</label>
                <input
                  type="text"
                  value={flowId}
                  style={inputStyle}
                  onChange={e => this.handleFlowIdInput(e)}
                />
              </div>
              <p
                style={{
                  ...menuTextStyle,
                  marginTop: '10px',
                  lineHeight: '16px',
                }}
              >
                Enter the Flow ID of a published flow to trigger it from this
                image.
              </p>
            </div>
          )}
        </div>
      </ActionsMenu>
    );

    return (
      <div>
        <LinkButton
          isMenuButton
          isActive={isActive}
          onClick={() => this.toggleDropdown()}
          {...buttonProps}
        />
        {dropdownNodes}
      </div>
    );
  }
}

ImageOnClick.propTypes = {
  href: PropTypes.string,
  isNewWindow: PropTypes.bool,
  flowId: PropTypes.string,
  localState: PropTypes.instanceOf(Map),
  persistedState: PropTypes.instanceOf(Map),
  onChange: PropTypes.func,
  onToggleActive: PropTypes.func,
  isActive: PropTypes.bool,
  hasRoomToRenderBelow: PropTypes.bool,
};
