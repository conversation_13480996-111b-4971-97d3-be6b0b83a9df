import React from 'react';
import PropTypes from 'prop-types';
import { Map } from 'immutable';
import { LIST_STYLE_TYPE } from '../helpers/constants';
import ActionsMenu from '../components/ActionsMenu';
import ListIcon from '../icons/ListIcon';
import { animateWhenTogglingOff } from '../helpers/utils';
import { getButtonProps } from '../helpers/styles/editor';
import { handleList } from '../helpers/draft/actions';

export default class List extends React.Component {
  toggleDropdown = () => {
    const { onToggleActive, isActive } = this.props;

    if (isActive) {
      animateWhenTogglingOff(() => onToggleActive(!isActive));
    } else {
      onToggleActive(!isActive);
    }
  };

  handleFormat = listType => {
    const { localState, persistedState, onChange } = this.props;
    onChange({
      localState: handleList(listType, localState),
      persistedState,
    });
    this.toggleDropdown();
  };

  render() {
    const { isActive, hasRoomToRenderBelow, blockType } = this.props;
    const listType =
      Object.values(LIST_STYLE_TYPE).includes(blockType) && blockType;
    const buttonProps = getButtonProps(isActive);
    const secondaryButtonProps = getButtonProps(false);

    return (
      <>
        <ListIcon
          isMenuButton
          isActive={isActive}
          listType={listType || 'unordered-list-item'}
          onClick={() => this.toggleDropdown()}
          hideBackground={buttonProps.hideBackground}
          color={buttonProps.color}
          clickColor={buttonProps.clickColor}
          activeColor={buttonProps.activeColor}
          hoverColor={buttonProps.hoverColor}
        />
        {isActive && (
          <ActionsMenu
            isOpen
            position={hasRoomToRenderBelow ? 'bottom' : 'top'}
            style={{
              padding: '0px 4px',
              width: '48px',
            }}
          >
            <ActionsMenu.MenuItem
              onClick={() =>
                this.handleFormat(LIST_STYLE_TYPE.UNORDERED_LIST_ITEM)
              }
              data-testid="unordered-list"
            >
              <ListIcon
                isActive={listType === LIST_STYLE_TYPE.UNORDERED_LIST_ITEM}
                listType={LIST_STYLE_TYPE.UNORDERED_LIST_ITEM}
                hideBackground={secondaryButtonProps.hideBackground}
                color={secondaryButtonProps.color}
                clickColor={secondaryButtonProps.clickColor}
                activeColor={secondaryButtonProps.activeColor}
                hoverColor={secondaryButtonProps.hoverColor}
              />
            </ActionsMenu.MenuItem>
            <ActionsMenu.MenuItem
              onClick={() =>
                this.handleFormat(LIST_STYLE_TYPE.ORDERED_LIST_ITEM)
              }
              data-testid="ordered-list"
            >
              <ListIcon
                isActive={listType === LIST_STYLE_TYPE.ORDERED_LIST_ITEM}
                listType={LIST_STYLE_TYPE.ORDERED_LIST_ITEM}
                hideBackground={secondaryButtonProps.hideBackground}
                color={secondaryButtonProps.color}
                clickColor={secondaryButtonProps.clickColor}
                activeColor={secondaryButtonProps.activeColor}
                hoverColor={secondaryButtonProps.hoverColor}
              />
            </ActionsMenu.MenuItem>
          </ActionsMenu>
        )}
      </>
    );
  }
}

List.propTypes = {
  localState: PropTypes.instanceOf(Map),
  persistedState: PropTypes.instanceOf(Map),
  onChange: PropTypes.func,
  blockType: PropTypes.oneOfType([
    PropTypes.oneOf([
      LIST_STYLE_TYPE.ORDERED_LIST_ITEM,
      LIST_STYLE_TYPE.UNORDERED_LIST_ITEM,
    ]),
    // FIXME: because prop spreading from parent component...
    PropTypes.string,
  ]),
  onToggleActive: PropTypes.func,
  isActive: PropTypes.bool,
  hasRoomToRenderBelow: PropTypes.bool,
};
