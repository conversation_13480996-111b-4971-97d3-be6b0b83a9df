import React from 'react';
import PropTypes from 'prop-types';
import { Map } from 'immutable';
import DropDownMenu from '../components/DropDownMenu';
import { updateInlineStyle, getCurrentStyle } from '../helpers/draft/actions';
import { CUSTOM_STYLE_PREFIX_FONT_TEXT_TRANSFORM } from '../helpers/draft/convert';
import { LabeledDropdownContainer } from '../helpers/styles/editor';

const options = [
  {
    label: 'None',
    value: 'none',
  },
  {
    label: 'AA',
    value: 'uppercase',
  },
  {
    label: 'Aa',
    value: 'capitalize',
  },
  {
    label: 'aa',
    value: 'lowercase',
  },
];

export default class FontTextTransform extends React.Component {
  handleFormat = value => {
    const { localState, persistedState, onChange } = this.props;
    const editorState = localState.get('editorState');
    const nextEditorState = updateInlineStyle(
      value,
      editorState,
      CUSTOM_STYLE_PREFIX_FONT_TEXT_TRANSFORM
    );
    const newLocalState = localState.set('editorState', nextEditorState);
    onChange({
      localState: newLocalState,
      persistedState,
    });
  };

  render() {
    const { localState } = this.props;
    const currentValue = getCurrentStyle(
      localState.get('editorState'),
      CUSTOM_STYLE_PREFIX_FONT_TEXT_TRANSFORM
    );

    return (
      <LabeledDropdownContainer>
        <label>Case</label>
        <DropDownMenu
          unsearchable
          className="form-control"
          selectedValue={currentValue}
          options={options}
          onSelect={this.handleFormat}
        />
      </LabeledDropdownContainer>
    );
  }
}

FontTextTransform.propTypes = {
  localState: PropTypes.instanceOf(Map),
  persistedState: PropTypes.instanceOf(Map),
  onChange: PropTypes.func,
  hasRoomToRenderBelow: PropTypes.bool,
};
