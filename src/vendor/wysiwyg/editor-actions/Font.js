import React from 'react';
import PropTypes from 'prop-types';
import { Map } from 'immutable';

import { getButtonProps } from '../helpers/styles/editor';
import ActionsMenu from '../components/ActionsMenu';

import { FontButton } from '../icons/FontButton';
import { animateWhenTogglingOff } from '../helpers/utils';
import FontFamily from './FontFamily';
// import FontWeight from "./FontWeight";
import FontLineHeight from './FontLineHeight';
import FontLetterSpacing from './FontLetterSpacing';
import FontTextTransform from './FontTextTransform';

export default class Font extends React.Component {
  toggleDropdown = () => {
    const { onToggleActive, isActive } = this.props;

    if (isActive) {
      animateWhenTogglingOff(() => onToggleActive(!isActive));
    } else {
      onToggleActive(!isActive);
    }
  };

  render() {
    const {
      isActive,
      hasRoomToRenderBelow,
      localState,
      persistedState,
      onChange,
      blockData,
    } = this.props;
    const buttonProps = getButtonProps(isActive);
    const fontOptionProps = {
      hasRoomToRenderBelow,
      localState,
      persistedState,
      onChange,
    };

    return (
      <div>
        <FontButton
          isMenuButton
          isActive={isActive}
          onClick={this.toggleDropdown}
          {...buttonProps}
        />
        {isActive && (
          <ActionsMenu
            isOpen={isActive}
            position={hasRoomToRenderBelow ? 'bottom' : 'top'}
            style={{
              padding: '0px',
              width: '320px',
            }}
          >
            <FontFamily {...fontOptionProps} />
            {/* 
            NOTE: We're holding off on including this component until we pull in custom fonts into the
            FontFamily component since the default fonts we include only have two weights right now, 
            normal and bold, which are covered by the Bold button in the RichTextToolbar
            
            <FontWeight {...fontOptionProps} /> 
            */}
            <FontLineHeight {...fontOptionProps} blockData={blockData} />
            <FontLetterSpacing {...fontOptionProps} />
            <FontTextTransform {...fontOptionProps} />
          </ActionsMenu>
        )}
      </div>
    );
  }
}

Font.propTypes = {
  localState: PropTypes.instanceOf(Map),
  persistedState: PropTypes.instanceOf(Map),
  onChange: PropTypes.func,
  onToggleActive: PropTypes.func,
  isActive: PropTypes.bool,
  hasRoomToRenderBelow: PropTypes.bool,
  blockData: PropTypes.object,
};
