import React from 'react';
import PropTypes from 'prop-types';
import { Map } from 'immutable';
import { RichUtils } from 'draft-js';

import { getButtonProps } from '../helpers/styles/editor';
import { INLINE_STYLE_TYPE } from '../helpers/constants';
import ItalicButton from '../icons/ItalicButton';

export function applyItalicToEditorState(editorState) {
  return RichUtils.toggleInlineStyle(editorState, INLINE_STYLE_TYPE.ITALIC);
}

export default class Italic extends React.Component {
  handleFormat() {
    const { localState, persistedState, onChange } = this.props;
    const newLocalState = localState.set(
      'editorState',
      applyItalicToEditorState(localState.get('editorState'))
    );
    onChange({
      localState: newLocalState,
      persistedState,
    });
  }

  render() {
    const { inlineStyle } = this.props;
    const isItalic = inlineStyle && inlineStyle.has(INLINE_STYLE_TYPE.ITALIC);
    const buttonProps = getButtonProps(isItalic);

    return (
      <ItalicButton
        isActive={isItalic}
        onClick={() => this.handleFormat()}
        {...buttonProps}
      />
    );
  }
}

Italic.propTypes = {
  localState: PropTypes.instanceOf(Map),
  persistedState: PropTypes.instanceOf(Map),
  onChange: PropTypes.func,
  inlineStyle: PropTypes.object,
};
