import React from 'react';
import PropTypes from 'prop-types';
import { Map } from 'immutable';
import { RichUtils } from 'draft-js';

import styled from 'styled-components';
import { getButtonProps, colors } from '../helpers/styles/editor';
import ActionsMenu from '../components/ActionsMenu';

import FontStyleButton from '../icons/FontStyleButton';

import { animateWhenTogglingOff } from '../helpers/utils';
import { TEXT_STYLE_TYPE } from '../helpers/constants';

const Wrapper = styled.div`
  &:hover svg path {
    fill: #5c5cff;
  }

  ${({ isActive }) =>
    isActive &&
    `
    svg path {
      fill: #5c5cff;
    }
`}
`;

// https://draftjs.org/docs/advanced-topics-custom-block-render-map.html
const styleOptions = [
  {
    name: 'Normal',
    value: TEXT_STYLE_TYPE.UNSTYLED,
    shortcutKey: '0',
    htmlTag: 'p',
  },
  {
    name: 'Header 1',
    value: TEXT_STYLE_TYPE.HEADER_ONE,
    shortcutKey: '1',
    htmlTag: 'h1',
  },
  {
    name: 'Header 2',
    value: TEXT_STYLE_TYPE.HEADER_TWO,
    shortcutKey: '2',
    htmlTag: 'h2',
  },
  {
    name: 'Header 3',
    value: TEXT_STYLE_TYPE.HEADER_THREE,
    shortcutKey: '3',
    htmlTag: 'h3',
  },
  {
    name: 'Header 4',
    value: TEXT_STYLE_TYPE.HEADER_FOUR,
    shortcutKey: '4',
    htmlTag: 'h4',
  },
  {
    name: 'Header 5',
    value: TEXT_STYLE_TYPE.HEADER_FIVE,
    shortcutKey: '5',
    htmlTag: 'h5',
  },
];

export function applyTextStyleToEditorState(editorState, styleType) {
  return RichUtils.toggleBlockType(editorState, styleType);
}

const ShortcutTag = styled.span`
  && {
    font-size: var(--regular);
    line-height: 150%;
    border-radius: 6px;
    padding: 6px 12px;
    margin-left: 18px;
    background-color: rgba(92, 92, 255, 0.1);
    color: black;
    font-weight: var(--normal);
  }
`;

function Option({
  tag: CustomTag,
  name,
  className,
  shortcutKey,
  onClick,
  isSelected,
}) {
  return (
    <CustomTag
      onClick={onClick}
      role="option"
      aria-selected={isSelected}
      aria-label={name}
      className={className}
    >
      {name}
      <ShortcutTag>{`ctrl + shift + ${shortcutKey}`}</ShortcutTag>
    </CustomTag>
  );
}

const StyledOption = styled(Option)`
  color: #222;
  margin: 0;
  padding: 8px;
  cursor: pointer;
  transition: background-color 0.15s ease-out;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  background-color: transparent;

  &:hover {
    background-color: var(--light-text-color);
  }

  &[aria-selected='true'] {
    color: ${colors.apc_purple};
  }
`;

export default class TextStyle extends React.Component {
  constructor(props) {
    super(props);

    const currentBlockType = this.findCurrentBlockType(props.localState);

    this.state = {
      blockType: currentBlockType,
      isMenuOpen: props.isActive || false,
    };
  }

  UNSAFE_componentWillReceiveProps(nextProps) {
    const currentBlockType = this.findCurrentBlockType(nextProps.localState);
    if (currentBlockType !== this.state.blockType) {
      this.setState({
        blockType: currentBlockType,
      });

      if (this.props.isActive) {
        this.toggleDropdown();
      }
    }

    if (nextProps.isActive !== this.props.isActive) {
      this.setState({
        isMenuOpen: nextProps.isActive,
      });
    }
  }

  findCurrentBlockType(localState) {
    const editorState = localState.get('editorState');
    let currentBlockType = TEXT_STYLE_TYPE.UNSTYLED;
    if (editorState) {
      currentBlockType = RichUtils.getCurrentBlockType(editorState);
    }
    return currentBlockType;
  }

  toggleDropdown() {
    const { onToggleActive, isActive } = this.props;
    this.setState(({ isMenuOpen }) => ({
      isMenuOpen: !isMenuOpen,
    }));

    if (isActive) {
      animateWhenTogglingOff(() => onToggleActive(!isActive));
    } else {
      onToggleActive(!isActive);
    }
  }

  handleSave(value) {
    this.handleFormat(value);
  }

  handleFormat(selectedValue) {
    const { localState, persistedState, onChange, onToggleActive } = this.props;

    const newLocalState = localState.set(
      'editorState',
      applyTextStyleToEditorState(localState.get('editorState'), selectedValue)
    );

    this.setState({
      blockType: selectedValue,
      isMenuOpen: false,
    });

    animateWhenTogglingOff(() => onToggleActive(false));

    onChange({
      localState: newLocalState,
      persistedState,
    });
  }

  render() {
    const { blockType, isMenuOpen } = this.state;
    const { isActive, hasRoomToRenderBelow } = this.props;

    const buttonProps = getButtonProps(isActive);

    const actionsMenuProps = {
      isOpen: isMenuOpen,
      position: hasRoomToRenderBelow ? 'bottom' : 'top',
      style: {
        height: '200px',
        overflowY: 'scroll',
        padding: '8px 0',
        width: '300px',
      },
    };

    const dropdownNodes = isActive ? (
      <ActionsMenu {...actionsMenuProps}>
        {styleOptions.map(({ htmlTag, name, value, shortcutKey }, i) => (
          <StyledOption
            tag={htmlTag}
            name={name}
            shortcutKey={shortcutKey}
            onClick={() => this.handleSave(value)}
            key={i}
            isSelected={blockType === value}
          />
        ))}
      </ActionsMenu>
    ) : null;

    return (
      <Wrapper isActive={isActive}>
        <FontStyleButton
          isMenuButton
          isActive={isActive}
          blockType={blockType}
          onClick={() => this.toggleDropdown()}
          {...buttonProps}
        />
        {dropdownNodes}
      </Wrapper>
    );
  }
}

TextStyle.propTypes = {
  localState: PropTypes.instanceOf(Map),
  persistedState: PropTypes.instanceOf(Map),
  onChange: PropTypes.func,
  onToggleActive: PropTypes.func,
  isActive: PropTypes.bool,
  hasRoomToRenderBelow: PropTypes.bool,
};
