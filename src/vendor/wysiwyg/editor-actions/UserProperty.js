import React, { useEffect, useState } from 'react';
import PropTypes from 'prop-types';
import { Map, List } from 'immutable';
import { EditorState, Modifier } from 'draft-js';
import styled from 'styled-components';
import {
  editorButtonStyle,
  getButtonProps,
  inputStyle,
  secondaryMenuTitleStyle,
} from '../helpers/styles/editor';
import DropDownMenu from '../components/DropDownMenu';
import FocusableInput from '../components/FocusableInput';
import ActionsMenu from '../components/ActionsMenu';

import UserPropertyButton from '../icons/UserPropertyButton';
import { animateWhenTogglingOff } from '../helpers/utils';
import FontIcon from '../components/FontAwesome/FontIcon';

const InfoIcon = styled(FontIcon)`
  float: right;
  cursor: pointer;
  width: 1em;
  path {
    fill: #7e89a9;
  }
`;

const InfoOverlay = styled.div`
  z-index: 1;
  position: absolute;
  border-radius: 12px;
  width: 300px;
  background: #0b1a38;
  padding: 12px;
  opacity: 0.85;
  left: 0px;
  top: 0px;
  pointer-events: none;
  height: ${props => (props.valueOptions ? '100%' : 'inherit')};

  &&& {
    color: #ffffff;
  }
`;

const UserProperty = ({
  localState,
  persistedState,
  onChange,
  onToggleActive,
  isActive,
  userProperties,
  focusEditor,
  hasRoomToRenderBelow,
}) => {
  const [isMenuOpen, setIsMenuOpen] = useState(isActive);
  const [selectedProperty, setSelectedProperty] = useState(null);
  const [fallbackProperty, setFallbackProperty] = useState(null);
  const [isHovering, setIsHovering] = useState(false);
  const [customValue, setCustomValue] = useState('');

  useEffect(() => {
    if (isActive) {
      setIsMenuOpen(true);
    }
  }, [isActive]);

  /*
   * NOTE: Set value for customValue when fallbackProperty changes
   */
  useEffect(() => {
    const editorState = localState.get('editorState');

    if (!editorState) {
      return;
    }

    const selectionState = editorState.getSelection();
    const isJustCursor = selectionState.isCollapsed();

    if (isJustCursor) {
      return;
    }

    const anchorKey = selectionState.getAnchorKey();
    const currentContent = editorState.getCurrentContent();
    const currentContentBlock = currentContent.getBlockForKey(anchorKey);
    const start = selectionState.getStartOffset();
    const end = selectionState.getEndOffset();
    const blockText = currentContentBlock.getText();
    const selectedText = blockText.slice(start, end);
    setCustomValue(selectedText || blockText);
  }, [fallbackProperty, localState]);

  const toggleDropdown = () => {
    setIsMenuOpen(!isMenuOpen);

    if (isActive) {
      focusEditor();
      setSelectedProperty(null);
      setFallbackProperty(null);
      setCustomValue('');
      animateWhenTogglingOff(() => onToggleActive(!isActive));
    } else {
      onToggleActive(!isActive);
    }
  };

  const handleSetValue = () => {
    const editorState = localState.get('editorState');
    const selectionState = editorState.getSelection();
    const contentState = editorState.getCurrentContent();

    const fallback =
      fallbackProperty === 'custom' ? customValue : fallbackProperty;
    const newValue = fallback
      ? `{{ ${selectedProperty} | "${fallback}" }}`
      : `{{ ${selectedProperty} }}`;

    const newContentState = selectionState.isCollapsed()
      ? Modifier.insertText(contentState, selectionState, newValue)
      : Modifier.replaceText(contentState, selectionState, newValue);

    const newLocalState = localState.set(
      'editorState',
      EditorState.push(editorState, newContentState, 'insert-characters')
    );

    animateWhenTogglingOff(() => onToggleActive(false));

    onChange({
      localState: newLocalState,
      persistedState,
    });
  };

  const buttonProps = getButtonProps(isActive);

  const propertyInputStyle = {
    ...inputStyle,
    width: '100%',
    marginTop: '10px',
  };

  const titleStyles = secondaryMenuTitleStyle;

  // Leave blank if nothing
  if (!userProperties || !userProperties.size) {
    return <div />;
  }

  const userPropertiesDropdown = [
    ...userProperties.map(userProperty => {
      return userProperty.toJS();
    }),
  ].map(userProperty => ({ label: userProperty.name, ...userProperty }));

  const valueOptions = !selectedProperty
    ? null
    : [
        { label: 'No fallback', value: null },
        ...userPropertiesDropdown
          .find(userProperty => userProperty.value === selectedProperty)
          .options.map(option => ({
            label: option.name,
            value: option.name,
          })),
        { label: 'Custom text...', value: 'custom' },
      ];

  const actionsMenuProps = {
    isOpen: isMenuOpen,
    position: hasRoomToRenderBelow ? 'bottom' : 'top',
  };

  const dropdownNodes = isActive ? (
    <ActionsMenu {...actionsMenuProps}>
      {isHovering && (
        <InfoOverlay valueOptions={valueOptions}>
          Personalization allows you to auto-populate a user's information, like
          their name, into your text. You can also set a fallback in case we
          don’t have the user’s information. For example, you can say “Hi Joe”,
          and fallback to “Hi friend”.
        </InfoOverlay>
      )}

      <div style={titleStyles}>
        Personalization
        <InfoIcon
          icon="info-circle"
          onMouseOver={() => setIsHovering(true)}
          onMouseOut={() => setIsHovering(false)}
        />
      </div>
      <DropDownMenu
        className="form-control"
        defaultValue="Choose a property"
        selectedValue={selectedProperty}
        options={userPropertiesDropdown}
        onSelect={setSelectedProperty}
      />
      {valueOptions && (
        <div style={{ marginTop: 10 }}>
          <DropDownMenu
            className="form-control"
            defaultValue="Choose a fallback"
            selectedValue={fallbackProperty}
            options={valueOptions}
            onSelect={setFallbackProperty}
          />
          {fallbackProperty === 'custom' && (
            <FocusableInput
              inputStyles={propertyInputStyle}
              handleChange={setCustomValue}
              placeholder="Type custom text..."
              value={customValue}
            />
          )}
          <button style={editorButtonStyle} onClick={handleSetValue}>
            Insert
          </button>
        </div>
      )}
    </ActionsMenu>
  ) : null;

  return (
    <div>
      <UserPropertyButton
        isMenuButton
        isActive={isActive}
        onClick={toggleDropdown}
        {...buttonProps}
      />
      {dropdownNodes}
    </div>
  );
};

UserProperty.propTypes = {
  localState: PropTypes.instanceOf(Map),
  persistedState: PropTypes.instanceOf(Map),
  onChange: PropTypes.func,
  onToggleActive: PropTypes.func,
  isActive: PropTypes.bool,
  userProperties: PropTypes.instanceOf(List),
  focusEditor: PropTypes.func,
  hasRoomToRenderBelow: PropTypes.bool,
};

export default UserProperty;
