import React from 'react';
import PropTypes from 'prop-types';
import { Map } from 'immutable';
import {
  getButtonProps,
  secondaryMenuTitleStyle,
  inputStyle,
  labelStyle,
} from '../helpers/styles/editor';
import ActionsMenu from '../components/ActionsMenu';
import AccessibilityButton from '../icons/AccessibilityButton';
import { animateWhenTogglingOff } from '../helpers/utils';

export default class Accessibility extends React.Component {
  constructor(props) {
    super(props);

    const { persistedState } = props;

    this.state = {
      altText: persistedState.get('altText') || '',
    };

    this.toggleDropdown = this.toggleDropdown.bind(this);
  }

  UNSAFE_componentWillReceiveProps(nextProps) {
    if (nextProps.isActive !== this.props.isActive) {
      this.setState({
        isMenuOpen: nextProps.isActive,
      });
    }
  }

  toggleDropdown(e) {
    e.preventDefault();
    const { onToggleActive, isActive } = this.props;
    this.setState(({ isMenuOpen }) => ({
      isMenuOpen: !isMenuOpen,
    }));

    if (isActive) {
      animateWhenTogglingOff(() => onToggleActive(!isActive));
    } else {
      onToggleActive(!isActive);
    }
  }

  handleTextInputChange(e) {
    const val = e.currentTarget.value;
    this.setState({ altText: val }, this.handleSave);
  }

  handleSave() {
    const { localState, persistedState, onChange } = this.props;

    const newPersistedState = persistedState.set('altText', this.state.altText);

    onChange({
      localState,
      persistedState: newPersistedState,
    });
  }

  render() {
    const { isMenuOpen, altText } = this.state;
    const { isActive, hasRoomToRenderBelow } = this.props;

    const buttonProps = getButtonProps(isActive);

    const titleStyles = secondaryMenuTitleStyle;

    const actionsMenuProps = {
      isOpen: isMenuOpen,
      position: hasRoomToRenderBelow ? 'bottom' : 'top',
    };

    const dropdownNodes = isActive ? (
      <ActionsMenu {...actionsMenuProps}>
        <div style={titleStyles}>Accessibility Options</div>
        <div style={{ marginTop: 20 }}>
          <div>
            <label style={labelStyle}>Alt Text</label>
            <input
              style={inputStyle}
              value={altText}
              onChange={e => this.handleTextInputChange(e, 'altText')}
            />
          </div>
        </div>
      </ActionsMenu>
    ) : null;

    return (
      <div>
        <a href="#">
          <AccessibilityButton
            isMenuButton
            isActive={isActive}
            onClick={this.toggleDropdown}
            {...buttonProps}
          />
        </a>
        {dropdownNodes}
      </div>
    );
  }
}

Accessibility.propTypes = {
  localState: PropTypes.instanceOf(Map),
  persistedState: PropTypes.instanceOf(Map),
  onChange: PropTypes.func,
  onToggleActive: PropTypes.func,
  isActive: PropTypes.bool,
  hasRoomToRenderBelow: PropTypes.bool,
};
