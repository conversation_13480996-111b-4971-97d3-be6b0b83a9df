import React from 'react';
import PropTypes from 'prop-types';
import { Map } from 'immutable';
import styled from 'styled-components';

import {
  getButtonProps,
  secondaryMenuTitleStyle,
  inputStyle,
  flexColumn,
  labelStyle,
} from '../helpers/styles/editor';
import ActionsMenu from '../components/ActionsMenu';
import ZoomSlider from '../components/ZoomSlider';

import SelectSizeButton from '../icons/SelectSizeButton';
import { animateWhenTogglingOff } from '../helpers/utils';

const Input = styled.input`
  &::placeholder {
    font-style: italic;
    opacity: 0.5;
  }
`;

export default class ImageSize extends React.Component {
  constructor(props) {
    super(props);

    const { persistedState } = props;
    const persistedMinHeight = persistedState.get('minHeight');

    this.state = {
      attributeToEdit: this.props.heroImage ? 'minHeight' : 'width',
      minHeight: !isNaN(persistedMinHeight) ? persistedMinHeight : '',
      width: persistedState.get('width') || '',
      zoom: persistedState.get('zoom') || '',
      isMenuOpen: props.isActive || false,
    };
  }

  UNSAFE_componentWillReceiveProps(nextProps) {
    if (nextProps.isActive !== this.props.isActive) {
      this.setState({
        isMenuOpen: nextProps.isActive,
      });
    }
  }

  toggleDropdown(e) {
    e.preventDefault();
    const { onToggleActive, isActive } = this.props;
    this.setState(({ isMenuOpen }) => ({
      isMenuOpen: !isMenuOpen,
    }));

    if (isActive) {
      animateWhenTogglingOff(() => onToggleActive(!isActive));
    } else {
      onToggleActive(!isActive);
    }
  }

  handleInputChange(e, name) {
    const val = name === 'zoom' ? e.value : e.currentTarget.value;
    const parsedNumber = val && val.length ? Number.parseInt(val) : val;

    if (!isNaN(parsedNumber)) {
      this.setState({ [name]: parsedNumber }, this.handleSave);
    }
  }

  handleSave() {
    const { localState, persistedState, onChange } = this.props;

    const { attributeToEdit } = this.state;

    const newPersistedState = persistedState
      .set(attributeToEdit, this.state[attributeToEdit])
      .set('zoom', this.state.zoom)
      .delete('widthOverride')
      .delete('heightOverride');

    onChange({
      localState,
      persistedState: newPersistedState,
    });
  }

  render() {
    const { attributeToEdit, isMenuOpen } = this.state;
    const { isActive, hasRoomToRenderBelow, heroImage } = this.props;

    const buttonProps = getButtonProps(isActive);

    const attributeText = heroImage ? 'Minimum Height' : 'Width';
    const attributeCurrentValue = this.state[attributeToEdit];

    const titleStyles = secondaryMenuTitleStyle;

    const actionsMenuProps = {
      isOpen: isMenuOpen,
      position: hasRoomToRenderBelow ? 'bottom' : 'top',
    };

    const dropdownNodes = isActive ? (
      <ActionsMenu {...actionsMenuProps}>
        <div style={titleStyles}>
          Set Image {attributeText} (number in pixels)
        </div>
        <div style={{ marginTop: 20 }}>
          <div style={flexColumn}>
            <label style={labelStyle}>{attributeText}:</label>
            <Input
              style={inputStyle}
              value={attributeCurrentValue}
              onChange={e =>
                this.handleInputChange(e, this.state.attributeToEdit)
              }
            />
            {heroImage && (
              <ZoomSlider
                handleChange={e => this.handleInputChange(e, 'zoom')}
                zoom={this.state.zoom}
              />
            )}
          </div>
        </div>
      </ActionsMenu>
    ) : null;

    return (
      <div>
        <SelectSizeButton
          isMenuButton
          isActive={isActive}
          onClick={e => this.toggleDropdown(e)}
          {...buttonProps}
        />
        {dropdownNodes}
      </div>
    );
  }
}

ImageSize.propTypes = {
  heroImage: PropTypes.bool,
  localState: PropTypes.instanceOf(Map),
  persistedState: PropTypes.instanceOf(Map),
  onChange: PropTypes.func,
  onToggleActive: PropTypes.func,
  isActive: PropTypes.bool,
  hasRoomToRenderBelow: PropTypes.bool,
};

ImageSize.defaultProps = {
  heroImage: false,
};
