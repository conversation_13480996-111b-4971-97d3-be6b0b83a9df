import React from 'react';
import PropTypes from 'prop-types';
import {
  getButtonProps,
  secondaryMenuTitleStyle,
  actionsMenuInputStyle,
} from '../helpers/styles/editor';
import ActionsMenu from '../components/ActionsMenu';
import Toggle from '../components/Toggle';
import LinkButton from '../icons/LinkButton';
import { animateWhenTogglingOff } from '../helpers/utils';

export default class Hyperlink extends React.Component {
  constructor(props) {
    super(props);

    this.state = {
      href: props.href || '',
      isNewWindow: props.isNewWindow || false,
      isMenuOpen: props.isActive || false,
    };
  }

  UNSAFE_componentWillReceiveProps(nextProps) {
    const update = {};
    if (nextProps.href !== this.props.href) {
      update.href = nextProps.href;
    }
    if (nextProps.isNewWindow !== this.props.isNewWindow) {
      update.isNewWindow = nextProps.isNewWindow;
    }
    if (nextProps.isActive !== this.props.isActive) {
      update.isMenuOpen = nextProps.isActive;
    }
    if (Object.keys(update).length) {
      this.setState(update);
    }
  }

  toggleDropdown() {
    const { onToggleActive, isActive, focusEditor } = this.props;

    this.setState(({ isMenuOpen }) => ({
      isMenuOpen: !isMenuOpen,
    }));

    if (isActive) {
      focusEditor();
      animateWhenTogglingOff(() => onToggleActive(!isActive));
    } else {
      onToggleActive(!isActive);
    }
  }

  handleIsNewWindow = e => {
    const isNewWindow = e.target.checked;
    this.setState(
      {
        isNewWindow,
      },
      this.handleSave
    );
  };

  handleClick(e) {
    e.target.focus();
  }

  handleHref(e) {
    const href = e.target.value;
    this.setState(
      {
        href,
      },
      this.handleSave
    );
  }

  handleSave() {
    const { onChange } = this.props;
    const { isNewWindow, href } = this.state;

    onChange(href, isNewWindow);
  }

  render() {
    const { isActive, hasRoomToRenderBelow, isUpdatingExistingLink } =
      this.props;
    const { href, isNewWindow, isMenuOpen } = this.state;

    const buttonProps = getButtonProps(isActive);

    const titleStyles = secondaryMenuTitleStyle;

    const row = {
      marginTop: 20,
      display: 'flex',
    };

    const actionsMenuProps = {
      isOpen: isMenuOpen,
      position: hasRoomToRenderBelow ? 'bottom' : 'top',
      // NOTE: possible 55 offset to bottom
    };

    const dropdownNodes = isActive ? (
      <ActionsMenu {...actionsMenuProps}>
        <div style={titleStyles}>
          {isUpdatingExistingLink ? 'Update Link' : 'Link'}
        </div>
        <div>
          <div style={{ ...row, flexDirection: 'column' }}>
            <input
              type="text"
              style={actionsMenuInputStyle}
              value={href}
              onClickCapture={this.handleClick}
              onChange={e => this.handleHref(e)}
            />
          </div>
          <div style={{ ...row, alignItems: 'center' }}>
            <Toggle
              label="Open In New Window"
              checked={isNewWindow}
              onChange={this.handleIsNewWindow}
            />
          </div>
        </div>
      </ActionsMenu>
    ) : null;

    return (
      <div>
        <LinkButton
          isMenuButton
          isActive={isActive}
          onClick={() => this.toggleDropdown()}
          {...buttonProps}
        />
        {dropdownNodes}
      </div>
    );
  }
}

Hyperlink.propTypes = {
  href: PropTypes.string,
  isNewWindow: PropTypes.bool,
  onChange: PropTypes.func,
  onToggleActive: PropTypes.func,
  isActive: PropTypes.bool,
  isUpdatingExistingLink: PropTypes.bool,
  hasRoomToRenderBelow: PropTypes.bool,
  focusEditor: PropTypes.func,
};
