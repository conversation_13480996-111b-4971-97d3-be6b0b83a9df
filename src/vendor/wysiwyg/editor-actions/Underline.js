import React from 'react';
import PropTypes from 'prop-types';
import { Map } from 'immutable';
import { RichUtils } from 'draft-js';

import { getButtonProps } from '../helpers/styles/editor';
import { INLINE_STYLE_TYPE } from '../helpers/constants';
import UnderlineButton from '../icons/UnderlineButton';

export function applyUnderlineToEditorState(editorState) {
  return RichUtils.toggleInlineStyle(editorState, INLINE_STYLE_TYPE.UNDERLINE);
}

export default class Underline extends React.Component {
  handleFormat = () => {
    const { localState, persistedState, onChange } = this.props;
    const newLocalState = localState.set(
      'editorState',
      applyUnderlineToEditorState(localState.get('editorState'))
    );
    onChange({
      localState: newLocalState,
      persistedState,
    });
  };

  render() {
    const { inlineStyle } = this.props;
    const isUnderlined =
      inlineStyle && inlineStyle.has(INLINE_STYLE_TYPE.UNDERLINE);
    const buttonProps = getButtonProps(isUnderlined);

    return (
      <UnderlineButton
        isActive={isUnderlined}
        onClick={this.handleFormat}
        {...buttonProps}
      />
    );
  }
}

Underline.propTypes = {
  localState: PropTypes.instanceOf(Map),
  persistedState: PropTypes.instanceOf(Map),
  onChange: PropTypes.func,
  inlineStyle: PropTypes.object,
};
