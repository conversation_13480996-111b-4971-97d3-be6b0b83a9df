import React from 'react';
import PropTypes from 'prop-types';
import { Map } from 'immutable';
import { Picker } from 'emoji-mart';
import { EditorState } from 'draft-js';
import ActionsMenu from '../components/ActionsMenu';
import { convertBoundingBox } from '../helpers/domHelpers';
import { getButtonProps, emojiPickerStyles } from '../helpers/styles/editor';
import { pickNativeEmoji, pickImageEmoji } from '../helpers/emoji';
import EmojiButton from '../icons/EmojiButton';
import { animateWhenTogglingOff } from '../helpers/utils';

export default class EmojiSelector extends React.Component {
  constructor(props) {
    super(props);
    this.picker = React.createRef();
    this.state = {
      position: Map(),
      isMenuOpen: props.isActive || false,
      dropdownWidth: 440,
    };
  }

  componentDidMount() {
    this.setBoundingBox();
  }

  UNSAFE_componentWillReceiveProps(nextProps) {
    if (nextProps.isActive !== this.props.isActive) {
      this.setState({
        isMenuOpen: nextProps.isActive,
      });
    }
  }

  toggleDropdown() {
    const { onToggleActive, isActive } = this.props;
    this.setState(({ isMenuOpen }) => ({
      isMenuOpen: !isMenuOpen,
    }));

    if (isActive) {
      animateWhenTogglingOff(() => onToggleActive(!isActive));
    } else {
      onToggleActive(!isActive);
    }
  }

  setBoundingBox() {
    if (!this.wrapper) {
      return;
    }
    const position = convertBoundingBox(this.wrapper.getBoundingClientRect());

    if (!position.equals(this.state.position)) {
      this.setState({ position });
    }
  }

  handlePickerClick = e => {
    if (e.target.tagName === 'INPUT') {
      this.picker.current.querySelector('.emoji-mart-search input').focus();
    }
  };

  handlePickEmoji = emoji => {
    const { localState, persistedState, onChange, isTextToolbar } = this.props;

    if (isTextToolbar) {
      const editorState = localState.get('editorState');
      const newLocalState = localState.set(
        'editorState',
        EditorState.push(
          editorState,
          pickNativeEmoji({
            localState,
            emoji,
          }),
          'insert-characters'
        )
      );

      onChange({
        localState: newLocalState,
        persistedState,
      });
    } else {
      onChange({
        localState,
        persistedState: pickImageEmoji({ persistedState, emoji }),
      });
    }
  };

  render() {
    const { isMenuOpen, dropdownWidth } = this.state;
    const { isActive, hasRoomToRenderBelow, isTextToolbar } = this.props;

    const buttonProps = getButtonProps(isActive);

    const actionsMenuProps = {
      isOpen: isMenuOpen,
      position: hasRoomToRenderBelow ? 'bottom' : 'top',
      style: {
        overflow: 'hidden',
        padding: '0',
        width: dropdownWidth,
      },
    };

    const dropdownNodes = isActive ? (
      <ActionsMenu {...actionsMenuProps}>
        {/* NOTE: onClickCapture is needed here to capture the click on the input in the EmojiMart picker. 
            I attempted to pass the ref to the Picker, but there are currently issues with how
            the library handles refs https://github.com/missive/emoji-mart/issues/416
        */}
        <div ref={this.picker} onClickCapture={this.handlePickerClick}>
          <style>{emojiPickerStyles}</style>
          <Picker
            perLine={15}
            color="#23baff"
            native={isTextToolbar}
            set="twitter"
            onClick={this.handlePickEmoji}
            style={{ width: '100%' }}
            title="Select an emoji"
          />
        </div>
      </ActionsMenu>
    ) : null;

    return (
      <div ref={el => (this.wrapper = el)}>
        <EmojiButton
          isMenuButton
          isActive={isActive}
          onClick={() => this.toggleDropdown()}
          {...buttonProps}
        />
        {dropdownNodes}
      </div>
    );
  }
}

EmojiSelector.propTypes = {
  onChange: PropTypes.func,
  onToggleActive: PropTypes.func,
  isActive: PropTypes.bool,
  hasRoomToRenderBelow: PropTypes.bool,
  localState: PropTypes.instanceOf(Map),
  persistedState: PropTypes.instanceOf(Map),
  maxWidth: PropTypes.number,
  isTextToolbar: PropTypes.bool,
};
