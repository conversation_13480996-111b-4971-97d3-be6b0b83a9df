import React from 'react';
import PropTypes from 'prop-types';
import { Map } from 'immutable';
import DropDownMenu from '../components/DropDownMenu';
import { updateInlineStyle, getCurrentStyle } from '../helpers/draft/actions';
import { CUSTOM_STYLE_PREFIX_FONT_LETTER_SPACING } from '../helpers/draft/convert';
import { LabeledDropdownContainer } from '../helpers/styles/editor';

const options = [
  {
    label: 'Default',
    value: 'normal',
  },
  {
    label: '0px',
    value: '0px',
  },
  {
    label: '1px',
    value: '1px',
  },
  {
    label: '2px',
    value: '2px',
  },
  {
    label: '3px',
    value: '3px',
  },
  {
    label: '4px',
    value: '4px',
  },
  {
    label: '5px',
    value: '5px',
  },
  {
    label: '6px',
    value: '6px',
  },
  {
    label: '7px',
    value: '7px',
  },
  {
    label: '8px',
    value: '8px',
  },
  {
    label: '9px',
    value: '9px',
  },
  {
    label: '10px',
    value: '10px',
  },
];

export default class FontLetterSpacing extends React.Component {
  handleFormat = value => {
    const { localState, persistedState, onChange } = this.props;
    const editorState = localState.get('editorState');
    const nextEditorState = updateInlineStyle(
      value,
      editorState,
      CUSTOM_STYLE_PREFIX_FONT_LETTER_SPACING
    );
    const newLocalState = localState.set('editorState', nextEditorState);
    onChange({
      localState: newLocalState,
      persistedState,
    });
  };

  render() {
    const { localState } = this.props;
    const currentValue = getCurrentStyle(
      localState.get('editorState'),
      CUSTOM_STYLE_PREFIX_FONT_LETTER_SPACING
    );
    return (
      <LabeledDropdownContainer>
        <label>Letter space</label>
        <DropDownMenu
          unsearchable
          className="form-control"
          selectedValue={currentValue}
          options={options}
          onSelect={this.handleFormat}
        />
      </LabeledDropdownContainer>
    );
  }
}

FontLetterSpacing.propTypes = {
  localState: PropTypes.instanceOf(Map),
  persistedState: PropTypes.instanceOf(Map),
  onChange: PropTypes.func,
  hasRoomToRenderBelow: PropTypes.bool,
};
