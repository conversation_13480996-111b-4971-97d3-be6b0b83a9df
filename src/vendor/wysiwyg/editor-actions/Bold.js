import React from 'react';
import PropTypes from 'prop-types';
import { Map } from 'immutable';
import { getButtonProps } from '../helpers/styles/editor';
import BoldButton from '../icons/BoldButton';
import { getCurrentStyle, updateInlineStyle } from '../helpers/draft/actions';
import { CUSTOM_STYLE_PREFIX_FONT_WEIGHT } from '../helpers/draft/convert';

function isBoldStyle(editorState) {
  return getCurrentStyle(editorState, CUSTOM_STYLE_PREFIX_FONT_WEIGHT).includes(
    'bold'
  );
}

export function applyBoldToEditorState(editorState, currentValue = null) {
  const isBold =
    currentValue === null ? isBoldStyle(editorState) : currentValue;
  return updateInlineStyle(
    isBold ? 'normal' : 'bold',
    editorState,
    CUSTOM_STYLE_PREFIX_FONT_WEIGHT
  );
}

export default class Bold extends React.Component {
  handleFormat(isBold) {
    const { localState, persistedState, onChange } = this.props;
    const nextEditorState = applyBoldToEditorState(
      localState.get('editorState'),
      isBold
    );
    const newLocalState = localState.set('editorState', nextEditorState);
    onChange({
      localState: newLocalState,
      persistedState,
    });
  }

  render() {
    const { localState } = this.props;
    const isBold = isBoldStyle(localState.get('editorState'));
    const buttonProps = getButtonProps(isBold);

    return (
      <BoldButton
        isActive={isBold}
        onClick={() => this.handleFormat(isBold)}
        {...buttonProps}
      />
    );
  }
}

Bold.propTypes = {
  localState: PropTypes.instanceOf(Map),
  persistedState: PropTypes.instanceOf(Map),
  onChange: PropTypes.func,
};
