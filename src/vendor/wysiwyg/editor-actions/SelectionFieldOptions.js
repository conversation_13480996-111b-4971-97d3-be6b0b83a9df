import React from 'react';
import PropTypes from 'prop-types';
import { Map, List } from 'immutable';
import { escape } from 'html-escaper';

import {
  getButtonProps,
  secondaryMenuTitleStyle,
  checkboxStyle,
  menuTextStyle,
  inputStyle,
  labelStyle,
  InfoIcon,
  InfoOverlay,
  LabelRow,
} from '../helpers/styles/editor';
import ActionsMenu from '../components/ActionsMenu';

import SettingsButton from '../icons/SettingsButton';
import { animateWhenTogglingOff } from '../helpers/utils';

const forceMinSelection = (value, totalItems) => {
  if (value > totalItems) return totalItems;

  return value < 1 ? 1 : value;
};

const forceMaxSelection = (value, totalItems) => {
  return value > totalItems ? totalItems : value;
};

export default class SelectFieldOptions extends React.Component {
  constructor(props) {
    super(props);

    const {
      isRequired,
      fieldType,
      customReportingLabel,
      minSelection,
      maxSelection,
      options = List(),
    } = props.persistedState.toJS();

    const scapedOptions = options.map(escape);

    this.state = {
      isRequired: isRequired || false,
      fieldType: fieldType || 'radio',
      isMenuOpen: false,
      isHovering: false,
      customReportingLabel: customReportingLabel || '',
      minSelection: minSelection || 0,
      options: scapedOptions,
      maxSelection: maxSelection || scapedOptions.length,
    };
  }

  UNSAFE_componentWillReceiveProps(nextProps) {
    const update = {};
    const {
      isRequired,
      fieldType,
      customReportingLabel,
      minSelection,
      maxSelection,
      options = List(),
    } = this.props.persistedState.toJS();
    const {
      isRequired: isRequiredNew,
      fieldType: fieldTypeNew,
      customReportingLabel: customReportingLabelNew,
      minSelection: minSelectionNew,
      maxSelection: maxSelectionNew,
      options: optionsNew = List(),
    } = nextProps.persistedState.toJS();

    if (isRequired !== isRequiredNew) {
      update.isRequired = isRequiredNew;
    }

    if (fieldType !== fieldTypeNew) {
      update.fieldType = fieldTypeNew;
    }

    if (nextProps.isActive !== this.props.isActive) {
      update.isMenuOpen = nextProps.isActive;
    }

    if (customReportingLabel !== customReportingLabelNew) {
      update.customReportingLabel = customReportingLabelNew;
    }

    if (minSelection !== minSelectionNew) {
      update.minSelection = minSelectionNew;
    }

    if (maxSelection !== maxSelectionNew) {
      update.maxSelection = maxSelectionNew;
    }

    if (options.length !== optionsNew.length) {
      update.options = optionsNew;

      // in case the user updates min/max without closing the settings window
      // the user can change the options and the min/max values can be greater than optionsNew
      // in this case we need to adjust min/max
      if (maxSelection > optionsNew.length) {
        update.maxSelection = optionsNew.length;
      }

      if (minSelection > optionsNew.length) {
        update.minSelection = optionsNew.length;
      }
    }

    if (Object.keys(update).length) {
      this.setState(update);
    }
  }

  toggleDropdown() {
    const { onToggleActive, isActive } = this.props;
    this.setState(({ isMenuOpen }) => ({
      isMenuOpen: !isMenuOpen,
    }));

    if (isActive) {
      animateWhenTogglingOff(() => onToggleActive(!isActive));
    } else {
      onToggleActive(!isActive);
    }
  }

  handleIsRequired(e) {
    const isRequired = e.target.checked;

    this.setState(
      prev => ({
        isRequired,
        minSelection: isRequired
          ? forceMinSelection(prev.minSelection, prev.options.length)
          : 0,
      }),
      this.handleSave
    );
  }

  handleSave() {
    const { localState, persistedState, onChange } = this.props;
    const {
      isRequired,
      fieldType,
      customReportingLabel,
      minSelection,
      maxSelection,
    } = this.state;

    const newPersistedState = persistedState
      .set('isRequired', isRequired)
      .set('fieldType', fieldType)
      .set('customReportingLabel', customReportingLabel)
      .set('minSelection', minSelection)
      .set('maxSelection', maxSelection);

    onChange({
      localState,
      persistedState: newPersistedState,
    });
  }

  handleOnChangeCustomReportingLabel(e) {
    this.setState({ customReportingLabel: e.target.value }, this.handleSave);
  }

  handleMinSelection = e => {
    this.setState(prev => {
      const minSelection = forceMinSelection(
        e.target.value,
        prev.options.length
      );

      // The max selection value can't be smaller than the min value
      // in that case we update the maxSelection to be at least equal the min value
      const maxSelection =
        prev.maxSelection < minSelection ? minSelection : prev.maxSelection;

      const isRequired = minSelection > 0;

      return {
        minSelection,
        maxSelection,
        isRequired,
      };
    }, this.handleSave);
  };

  handleMaxSelection = e => {
    if (!e.target.value) {
      this.setState({ maxSelection: 1 }, this.handleSave);
      return;
    }

    this.setState(prev => {
      const value = parseInt(e.target.value);
      const newMaxValue = forceMaxSelection(
        value <= 0 ? 1 : value,
        prev.options.length
      );
      return { maxSelection: newMaxValue };
    }, this.handleSave);
  };

  render() {
    const {
      isRequired,
      isMenuOpen,
      isHovering,
      customReportingLabel,
      minSelection,
      maxSelection,
      options,
    } = this.state;
    const { isActive, hasRoomToRenderBelow, type } = this.props;

    const buttonProps = getButtonProps(isActive);

    const titleStyles = secondaryMenuTitleStyle;

    const row = {
      marginTop: 20,
    };

    const actionsMenuProps = {
      isOpen: isMenuOpen,
      position: hasRoomToRenderBelow ? 'bottom' : 'top',
    };

    const dropdownNodes = isActive ? (
      <ActionsMenu {...actionsMenuProps}>
        <div style={titleStyles}>Select Field Options</div>
        <div>
          <div style={row}>
            <input
              id="field-is-required"
              type="checkbox"
              style={checkboxStyle}
              checked={isRequired}
              onChange={e => this.handleIsRequired(e)}
            />
            <label style={menuTextStyle} htmlFor="field-is-required">
              Required Field
            </label>
          </div>
        </div>

        {type === 'checkbox' && (
          <>
            <LabelRow>
              <label style={labelStyle}>Min Selections</label>
            </LabelRow>
            <input
              style={inputStyle}
              onChange={e => this.handleMinSelection(e)}
              value={minSelection}
              type="number"
              min={isRequired ? 1 : 0}
              max={options.length}
            />
            <LabelRow>
              <label style={labelStyle}>Max Selections</label>
            </LabelRow>
            <input
              style={inputStyle}
              onChange={e => this.handleMaxSelection(e)}
              value={maxSelection}
              min={minSelection}
              max={options.length}
              type="number"
            />
          </>
        )}

        <LabelRow>
          <label style={labelStyle}>Custom reporting label</label>
          <InfoIcon
            icon="info-circle"
            onMouseOver={() => this.setState({ isHovering: true })}
            onMouseOut={() => this.setState({ isHovering: false })}
          />
          {isHovering && (
            <InfoOverlay>
              This label will only be used on survey reports to help you easily
              identify the form question.
            </InfoOverlay>
          )}
        </LabelRow>
        <input
          style={inputStyle}
          placeholder="My survey question"
          onChange={e => this.handleOnChangeCustomReportingLabel(e)}
          value={customReportingLabel}
        />
      </ActionsMenu>
    ) : null;

    return (
      <div>
        <SettingsButton
          isMenuButton
          isActive={isActive}
          onClick={() => this.toggleDropdown()}
          {...buttonProps}
        />
        {dropdownNodes}
      </div>
    );
  }
}

SelectFieldOptions.propTypes = {
  localState: PropTypes.instanceOf(Map),
  persistedState: PropTypes.instanceOf(Map),
  onChange: PropTypes.func,
  onToggleActive: PropTypes.func,
  isActive: PropTypes.bool,
  hasRoomToRenderBelow: PropTypes.bool,
};
