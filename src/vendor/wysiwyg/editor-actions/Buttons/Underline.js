import React from 'react';
import PropTypes from 'prop-types';
import { Map } from 'immutable';

import { getButtonProps } from '../../helpers/styles/editor';
import UnderlineButton from '../../icons/UnderlineButton';

export default class Underline extends React.Component {
  handleFormat = isUnderlined => {
    const { localState, persistedState, onChange, track } = this.props;
    const newPersistedState = persistedState.set('isUnderlined', !isUnderlined);

    track('Builder interaction', {
      name: 'Button - Clicked Underlined',
      component: 'Buttons/Italic',
    });

    onChange({
      localState,
      persistedState: newPersistedState,
    });
  };

  render() {
    const { persistedState } = this.props;
    const isUnderlined = persistedState.get('isUnderlined');
    const buttonProps = getButtonProps(isUnderlined);

    return (
      <UnderlineButton
        isActive={isUnderlined}
        onClick={() => this.handleFormat(isUnderlined)}
        {...buttonProps}
      />
    );
  }
}

Underline.propTypes = {
  localState: PropTypes.instanceOf(Map),
  persistedState: PropTypes.instanceOf(Map),
  onChange: PropTypes.func,
  inlineStyle: PropTypes.object,
};
