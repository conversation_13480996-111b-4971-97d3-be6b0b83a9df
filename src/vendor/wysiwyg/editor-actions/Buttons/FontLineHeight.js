import React from 'react';
import PropTypes from 'prop-types';
import { Map } from 'immutable';

import DropDownMenu from '../../components/DropDownMenu';
import { LabeledDropdownContainer } from '../../helpers/styles/editor';

const options = [
  {
    label: 'Default',
    value: 'inherit',
  },
  {
    label: '0',
    value: '0',
  },
  {
    label: '0.5',
    value: '0.5',
  },
  {
    label: '1',
    value: '1',
  },
  {
    label: '1.5',
    value: '1.5',
  },
  {
    label: '2',
    value: '2',
  },
];

export default class FontLineHeight extends React.Component {
  handleFormat = value => {
    const { localState, persistedState, onChange, track } = this.props;
    const newPersistedState = persistedState.set('lineHeight', value);

    track('Builder interaction', {
      name: 'Button - Clicked Line Height',
      component: 'Buttons/FontLineHeight',
    });

    onChange({
      localState,
      persistedState: newPersistedState,
    });
  };

  render() {
    const { persistedState } = this.props;
    const currentValue = persistedState.get('lineHeight') || '';

    // If currentValue includes "px",
    // (i.e. no em value from our menu was selected, default CSS uses pixels)
    // set "Default" option as the default value
    return (
      <LabeledDropdownContainer>
        <label>Line height</label>
        <DropDownMenu
          unsearchable
          className="form-control"
          selectedValue={currentValue.includes('px') ? 'inherit' : currentValue}
          options={options}
          onSelect={this.handleFormat}
          defaultValue="-"
        />
      </LabeledDropdownContainer>
    );
  }
}

FontLineHeight.propTypes = {
  localState: PropTypes.instanceOf(Map),
  persistedState: PropTypes.instanceOf(Map),
  onChange: PropTypes.func,
  hasRoomToRenderBelow: PropTypes.bool,
  track: PropTypes.func,
};
