import React from 'react';
import PropTypes from 'prop-types';
import { Map } from 'immutable';

import { getButtonProps } from '../../helpers/styles/editor';
import ItalicButton from '../../icons/ItalicButton';

export default class Italic extends React.Component {
  handleFormat(isItalic) {
    const { localState, persistedState, onChange, track } = this.props;
    const newPersistedState = persistedState.set('isItalic', !isItalic);

    track('Builder interaction', {
      name: 'Button - Clicked Italic',
      component: 'Buttons/Italic',
    });

    onChange({
      localState,
      persistedState: newPersistedState,
    });
  }

  render() {
    const { persistedState } = this.props;
    const isItalic = persistedState.get('isItalic');
    const buttonProps = getButtonProps(isItalic);

    return (
      <ItalicButton
        isActive={isItalic}
        onClick={() => this.handleFormat(isItalic)}
        {...buttonProps}
      />
    );
  }
}

Italic.propTypes = {
  localState: PropTypes.instanceOf(Map),
  persistedState: PropTypes.instanceOf(Map),
  onChange: PropTypes.func,
  inlineStyle: PropTypes.object,
  track: PropTypes.func,
};
