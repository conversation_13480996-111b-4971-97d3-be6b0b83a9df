import React from 'react';
import PropTypes from 'prop-types';
import { Map } from 'immutable';

import DropDownMenu from '../../components/DropDownMenu';
import { LabeledDropdownContainer } from '../../helpers/styles/editor';

const options = [
  {
    label: 'Arial',
    value: 'Arial, Helvetica Neue, Helvetica, sans-serif',
    fontFamily: true,
  },
  {
    label: 'Arvo',
    value: 'Arvo, serif',
    fontFamily: true,
  },
  {
    label: 'Droid Sans',
    value: 'Droid Sans, sans-serif',
    fontFamily: true,
  },
  {
    label: 'Futura',
    value: 'Futura, Trebuchet MS, Arial, sans-serif',
    fontFamily: true,
  },
  {
    label: 'Georgia',
    value: 'Georgia, Times, Times New Roman, serif',
    fontFamily: true,
  },
  {
    label: 'Helvetica Neue',
    value: 'Helvetica Neue, Helvetica, Arial, sans-serif',
    fontFamily: true,
  },
  {
    label: '<PERSON><PERSON>b',
    value: '<PERSON><PERSON>b, serif',
    fontFamily: true,
  },
  {
    label: 'La<PERSON>',
    value: 'Lato, sans-serif',
    fontFamily: true,
  },
  {
    label: 'Libre Baskerville',
    value: 'Libre Baskerville, serif',
    fontFamily: true,
  },
  {
    label: 'Lucida Bright',
    value: 'Lucida Bright, Georgia, serif',
    fontFamily: true,
  },
  {
    label: 'Open Sans',
    value: 'Open Sans, sans-serif',
    fontFamily: true,
  },
  {
    label: 'Palatino',
    value:
      'Palatino, Palatino Linotype, Palatino LT STD, Book Antiqua, Georgia, serif',
    fontFamily: true,
  },
  {
    label: 'Tahoma',
    value: 'Tahoma, Verdana, Segoe, sans-serif',
    fontFamily: true,
  },
  {
    label: 'Verdana',
    value: 'Verdana, Geneva, sans-serif',
    fontFamily: true,
  },
];

export default class FontFamily extends React.Component {
  handleFormat = value => {
    const { localState, persistedState, onChange, track } = this.props;
    const newPersistedState = persistedState.set('fontFamily', value);

    track('Builder interaction', {
      name: 'Button - Clicked Font Family',
      component: 'Buttons/FontFamily',
    });

    onChange({
      localState,
      persistedState: newPersistedState,
    });
  };

  render() {
    const { persistedState } = this.props;
    // Sometimes quotes show up when getting font family
    const currentValue = (persistedState.get('fontFamily') || '').replace(
      /["']/g,
      ''
    );

    return (
      <LabeledDropdownContainer>
        <label>Font</label>
        <DropDownMenu
          data-testid="font-family-menu"
          unsearchable
          className="form-control"
          selectedValue={currentValue}
          options={options}
          onSelect={this.handleFormat}
          defaultValue="-"
        />
      </LabeledDropdownContainer>
    );
  }
}

FontFamily.propTypes = {
  localState: PropTypes.instanceOf(Map),
  persistedState: PropTypes.instanceOf(Map),
  onChange: PropTypes.func,
  hasRoomToRenderBelow: PropTypes.bool,
  track: PropTypes.func,
};
