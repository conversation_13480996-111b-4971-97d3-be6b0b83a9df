import React from 'react';
import PropTypes from 'prop-types';
import { Map } from 'immutable';

import DropDownMenu from '../../components/DropDownMenu';
import { LabeledDropdownContainer } from '../../helpers/styles/editor';

const options = [
  {
    label: 'None',
    value: 'none',
  },
  {
    label: 'AA',
    value: 'uppercase',
  },
  {
    label: 'Aa',
    value: 'capitalize',
  },
  {
    label: 'aa',
    value: 'lowercase',
  },
];

export default class FontTextTransform extends React.Component {
  handleFormat = value => {
    const { localState, persistedState, onChange, track } = this.props;
    const newPersistedState = persistedState.set('textTransform', value);

    track('Builder interaction', {
      name: 'Button - Clicked Text Transform',
      component: 'Buttons/FontTextTransform',
    });

    onChange({
      localState,
      persistedState: newPersistedState,
    });
  };

  render() {
    const { persistedState } = this.props;
    const currentValue = persistedState.get('textTransform');

    return (
      <LabeledDropdownContainer>
        <label>Case</label>
        <DropDownMenu
          unsearchable
          className="form-control"
          selectedValue={currentValue}
          options={options}
          onSelect={this.handleFormat}
          defaultValue="-"
        />
      </LabeledDropdownContainer>
    );
  }
}

FontTextTransform.propTypes = {
  localState: PropTypes.instanceOf(Map),
  persistedState: PropTypes.instanceOf(Map),
  onChange: PropTypes.func,
  hasRoomToRenderBelow: PropTypes.bool,
  track: PropTypes.func,
};
