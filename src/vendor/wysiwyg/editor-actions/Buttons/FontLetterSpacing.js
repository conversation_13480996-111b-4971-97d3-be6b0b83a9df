import React from 'react';
import PropTypes from 'prop-types';
import { Map } from 'immutable';

import DropDownMenu from '../../components/DropDownMenu';
import { LabeledDropdownContainer } from '../../helpers/styles/editor';

const options = [
  {
    label: 'Default',
    value: 'normal',
  },
  {
    label: '0px',
    value: '0px',
  },
  {
    label: '1px',
    value: '1px',
  },
  {
    label: '2px',
    value: '2px',
  },
  {
    label: '3px',
    value: '3px',
  },
  {
    label: '4px',
    value: '4px',
  },
  {
    label: '5px',
    value: '5px',
  },
  {
    label: '6px',
    value: '6px',
  },
  {
    label: '7px',
    value: '7px',
  },
  {
    label: '8px',
    value: '8px',
  },
  {
    label: '9px',
    value: '9px',
  },
  {
    label: '10px',
    value: '10px',
  },
];

export default class FontLetterSpacing extends React.Component {
  handleFormat = value => {
    const { localState, persistedState, onChange, track } = this.props;
    const newPersistedState = persistedState.set('letterSpacing', value);

    track('Builder interaction', {
      name: 'Button - Clicked Letter Spacing',
      component: 'Buttons/FontLetterSpacing',
    });

    onChange({
      localState,
      persistedState: newPersistedState,
    });
  };

  render() {
    const { persistedState } = this.props;
    const currentValue = persistedState.get('letterSpacing');

    return (
      <LabeledDropdownContainer>
        <label>Letter space</label>
        <DropDownMenu
          unsearchable
          className="form-control"
          selectedValue={currentValue}
          options={options}
          onSelect={this.handleFormat}
          defaultValue="-"
        />
      </LabeledDropdownContainer>
    );
  }
}

FontLetterSpacing.propTypes = {
  localState: PropTypes.instanceOf(Map),
  persistedState: PropTypes.instanceOf(Map),
  onChange: PropTypes.func,
  hasRoomToRenderBelow: PropTypes.bool,
  track: PropTypes.func,
};
