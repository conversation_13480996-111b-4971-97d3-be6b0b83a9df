import React from 'react';
import PropTypes from 'prop-types';
import { Map } from 'immutable';
import { animateWhenTogglingOff } from 'vendor/wysiwyg/helpers/utils';
import FontSizeMenu from '../FontSize/FontSizeMenu';
import FontSizeInput from '../FontSize/FontSizeInput';

export default class FontSize extends React.Component {
  toggleDropdown = () => {
    const { onToggleActive, isActive } = this.props;

    if (isActive) {
      animateWhenTogglingOff(() => onToggleActive(!isActive));
    } else {
      onToggleActive(!isActive);
    }
  };

  /**
   * change handler for input and menu
   *
   * @param {number} value - selected or input value
   * @returns {void}
   */
  handleFormat = value => {
    const { localState, persistedState, onChange, track } = this.props;
    const newPersistedState = persistedState.set('fontSize', value);

    track('Builder interaction', {
      name: 'Button - Clicked Font Size',
      component: 'Buttons/FontSize',
    });

    onChange({
      localState,
      persistedState: newPersistedState,
    });
  };

  render() {
    const { isActive, hasRoomToRenderBelow, persistedState } = this.props;
    const fontSize = persistedState.get('fontSize');
    const formattedCurrentValue = `${
      fontSize !== undefined && fontSize !== 'inherit' ? fontSize : 14
    }`;

    return (
      <>
        <FontSizeInput
          isActive={isActive}
          onChange={this.handleFormat}
          onToggleMenu={this.toggleDropdown}
          value={formattedCurrentValue}
        />
        {isActive && (
          <FontSizeMenu
            selectedFontSize={formattedCurrentValue}
            hasRoomToRenderBelow={hasRoomToRenderBelow}
            onChange={this.handleFormat}
            onToggleMenu={this.toggleDropdown}
          />
        )}
      </>
    );
  }
}

FontSize.propTypes = {
  localState: PropTypes.instanceOf(Map),
  persistedState: PropTypes.instanceOf(Map),
  onChange: PropTypes.func,
  onToggleActive: PropTypes.func,
  isActive: PropTypes.bool,
  hasRoomToRenderBelow: PropTypes.bool,
  track: PropTypes.func,
};
