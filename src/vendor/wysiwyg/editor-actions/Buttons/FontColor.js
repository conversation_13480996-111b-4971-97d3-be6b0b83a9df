import React from 'react';
import PropTypes from 'prop-types';
import { Map } from 'immutable';
import tinyColor from 'tinycolor2';
import { ColorPicker } from 'ext/components/ColorPicker';
import { generateColor } from 'ext/lib/color';
import ActionsMenu from '../../components/ActionsMenu';

import { animateWhenTogglingOff } from '../../helpers/utils';

import FontColorButton from '../../icons/FontColorButton';

export default class FontColor extends React.Component {
  toggleDropdown = () => {
    const { onToggleActive, isActive } = this.props;

    if (isActive) {
      animateWhenTogglingOff(() => onToggleActive(!isActive));
    } else {
      onToggleActive(!isActive);
    }
  };

  handleColorChange = color => {
    const { localState, persistedState, onChange, track } = this.props;
    const value = generateColor(color);
    const newPersistedState = persistedState.set('fontColor', value);

    track('Builder interaction', {
      name: 'Button - Clicked Font Color',
      component: 'Buttons/FontColor',
    });

    onChange({
      localState,
      persistedState: newPersistedState,
    });
  };

  render() {
    const { isActive, hasRoomToRenderBelow, persistedState } = this.props;
    const currentValue = persistedState.get('fontColor') || '#000';
    const color = tinyColor(currentValue).toRgb();

    return (
      <>
        <FontColorButton
          isMenuButton
          isActive={isActive}
          onClick={() => this.toggleDropdown()}
          hideBackground
          color={currentValue}
          hoverColor={currentValue}
          iconStyle={{
            backgroundColor:
              tinyColor(currentValue).getBrightness() > 240
                ? tinyColor(currentValue).darken(25).toHexString()
                : null,
            borderRadius: '3px',
            border:
              tinyColor(currentValue).getBrightness() > 240
                ? `1px solid ${tinyColor(currentValue)
                    .darken(25)
                    .toHexString()}`
                : null,
          }}
        />
        {isActive && (
          <ActionsMenu
            isOpen
            position={hasRoomToRenderBelow ? 'bottom' : 'top'}
            style={{
              padding: '0px 0px 24px',
              width: '282px',
            }}
          >
            <ColorPicker
              color={color}
              onChangeComplete={this.handleColorChange}
            />
          </ActionsMenu>
        )}
      </>
    );
  }
}

FontColor.propTypes = {
  localState: PropTypes.instanceOf(Map),
  persistedState: PropTypes.instanceOf(Map),
  onChange: PropTypes.func,
  onToggleActive: PropTypes.func,
  isActive: PropTypes.bool,
  hasRoomToRenderBelow: PropTypes.bool,
  track: PropTypes.func,
};
