import React, { useEffect, useRef, Fragment } from 'react';
import styled from 'styled-components';
import PropTypes from 'prop-types';
import AngleInput from 'ext/components/AngleInput';
import RangeInput from 'ext/components/ui/RangeInput';
import { CurrentColor } from 'ext/components/ui';
import useToggle from 'ext/lib/hooks/use-toggle';
import ActionsMenu from 'vendor/wysiwyg/components/ActionsMenu';
import FontIcon from 'vendor/wysiwyg/components/FontAwesome/FontIcon';

import FillOpacity from './FillOpacity';
import {
  Title,
  Header,
  overrideActionsOpenedMenuStyles,
  Body,
  Grid,
  Row,
  SubTitle,
  ChildMenu,
  CurrentColorWrapper,
} from '../styles';
import { COLOR_PICKER_DEFAULT_COLOR } from '../constants';

const Container = styled.div`
  position: relative;
`;

const DropShadow = ({
  hasRoomToRenderBelow,
  onAngleChange,
  onColorChange,
  onBlurChange,
  onDistanceChange,
  onSizeChange,
  initialAngle,
  initialBlur,
  initialColor,
  initialDistance,
  initialSize,
  onGoBack,
  type,
}) => {
  const titleRef = useRef();
  const [isFillOpacityOpened, toggleFillOpacityOpened] = useToggle(false);

  useEffect(() => {
    titleRef.current?.focus();
  }, [titleRef]);

  const Root = hasRoomToRenderBelow ? Fragment : Container;

  return (
    <Root>
      <ActionsMenu
        aria-labelledby="title"
        isOpen
        position={hasRoomToRenderBelow ? 'bottom' : 'top'}
        role="toolbar"
        style={overrideActionsOpenedMenuStyles}
      >
        <Header onClick={onGoBack} onKeyDown={onGoBack}>
          <FontIcon size="1x" icon="angle-left" />
          <Title
            aria-label="Drop shadow opened, click to go back"
            id="title"
            ref={titleRef}
            tabIndex="0"
          >
            {type === 'hover' ? 'Hover ' : ''}Drop shadow
          </Title>
        </Header>

        <Body>
          <Grid>
            <Row
              tabIndex="0"
              role="button"
              aria-label="click to update drop shadow fill & opacity"
              onClick={toggleFillOpacityOpened}
            >
              <SubTitle>Fill & opacity</SubTitle>
              <CurrentColorWrapper>
                <CurrentColor color={initialColor} />
              </CurrentColorWrapper>
            </Row>
          </Grid>

          <RangeInput
            label="Distance"
            max={100}
            min={0}
            onChange={onDistanceChange}
            value={initialDistance}
          />
          <RangeInput
            label="Blur"
            max={100}
            min={0}
            onChange={onBlurChange}
            value={initialBlur}
          />
          <RangeInput
            label="Size"
            max={100}
            min={0}
            onChange={onSizeChange}
            value={initialSize}
          />
          <AngleInput
            onAngleChange={onAngleChange}
            initialValue={initialAngle}
          />
        </Body>
      </ActionsMenu>

      {isFillOpacityOpened && (
        <ChildMenu>
          <FillOpacity
            hasRoomToRenderBelow={hasRoomToRenderBelow}
            currentValue={initialColor ?? COLOR_PICKER_DEFAULT_COLOR}
            displayHeader={false}
            onGoBack={toggleFillOpacityOpened}
            onColorChange={onColorChange}
          />
        </ChildMenu>
      )}
    </Root>
  );
};

DropShadow.propTypes = {
  hasRoomToRenderBelow: PropTypes.bool.isRequired,
  initialAngle: PropTypes.number.isRequired,
  initialBlur: PropTypes.number.isRequired,
  initialColor: PropTypes.string,
  initialDistance: PropTypes.number.isRequired,
  initialSize: PropTypes.number.isRequired,
  onAngleChange: PropTypes.func.isRequired,
  onBlurChange: PropTypes.func.isRequired,
  onColorChange: PropTypes.func.isRequired,
  onDistanceChange: PropTypes.func.isRequired,
  onSizeChange: PropTypes.func.isRequired,
  onGoBack: PropTypes.func.isRequired,
  type: PropTypes.oneOf(['inline', 'hover']),
};

export default DropShadow;
