import React, { useEffect, useRef } from 'react';
import tinycolor from 'tinycolor2';
import PropTypes from 'prop-types';

import { ColorPicker } from 'ext/components/ColorPicker';
import ActionsMenu from 'vendor/wysiwyg/components/ActionsMenu';
import FontIcon from 'vendor/wysiwyg/components/FontAwesome/FontIcon';

import { Title, Header, overrideActionsOpenedMenuStyles } from '../styles';

const FillOpacity = ({
  hasRoomToRenderBelow,
  onGoBack,
  currentValue,
  onColorChange,
  displayHeader = true,
  type,
}) => {
  const titleRef = useRef();

  useEffect(() => {
    titleRef.current?.focus();
  }, [titleRef]);

  return (
    <ActionsMenu
      isOpen
      position={hasRoomToRenderBelow ? 'bottom' : 'top'}
      role="toolbar"
      aria-labelledby="title"
      style={overrideActionsOpenedMenuStyles}
    >
      {displayHeader && (
        <Header onClick={onGoBack} onKeyDown={onGoBack}>
          <FontIcon size="1x" icon="angle-left" />
          <Title
            tabIndex="0"
            id="title"
            aria-label="fill & opacity opened, click to go back"
            ref={titleRef}
          >
            {type === 'hover' ? 'Hover ' : ''}fill & opacity
          </Title>
        </Header>
      )}

      <ColorPicker
        color={tinycolor(currentValue).toRgb()}
        onChangeComplete={onColorChange}
      />
    </ActionsMenu>
  );
};

FillOpacity.propTypes = {
  currentValue: PropTypes.string.isRequired,
  displayHeader: PropTypes.bool,
  onGoBack: PropTypes.func.isRequired,
  onColorChange: PropTypes.func.isRequired,
  hasRoomToRenderBelow: PropTypes.bool.isRequired,
  type: PropTypes.oneOf(['inline', 'hover']),
};

export default FillOpacity;
