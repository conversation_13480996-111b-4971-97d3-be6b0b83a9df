import React, { useCallback, useEffect, useState } from 'react';
import PropTypes from 'prop-types';
import { Map } from 'immutable';
import { animateWhenTogglingOff } from 'vendor/wysiwyg/helpers/utils';
import { getButtonProps } from 'vendor/wysiwyg/helpers/styles/editor';
import AdvancedStylingButton from 'vendor/wysiwyg/icons/AdvancedStylingButton';

import { menuTypes } from './constants';
import MainMenu from './MainMenu';
import BorderColor from './BorderColor';
import FillOpacityContainer from './FillOpacityContainer';
import DropShadowContainer from './DropShadowContainer';

const menuMap = {
  [menuTypes.main]: MainMenu,
  [menuTypes.fillOpacity]: menuProps => (
    <FillOpacityContainer {...menuProps} type="inline" />
  ),
  [menuTypes.dropShadow]: menuProps => (
    <DropShadowContainer {...menuProps} type="inline" />
  ),
  [menuTypes.hoverFillOpacity]: menuProps => (
    <FillOpacityContainer {...menuProps} type="hover" />
  ),
  [menuTypes.hoverDropShadow]: menuProps => (
    <DropShadowContainer {...menuProps} type="hover" />
  ),
  [menuTypes.borderColor]: menuProps => (
    <BorderColor {...menuProps} type="inline" />
  ),
  [menuTypes.hoverBorderColor]: menuProps => (
    <BorderColor {...menuProps} type="hover" />
  ),
};

const ButtonContainerStyles = ({ isActive, onToggleActive, ...menuProps }) => {
  const buttonProps = getButtonProps(isActive);
  const [menuType, setMenuType] = useState(menuTypes.main);

  const toggleDropdown = e => {
    e.preventDefault();

    if (isActive) {
      animateWhenTogglingOff(() => onToggleActive(!isActive));
    } else {
      onToggleActive(!isActive);
    }
  };

  const setEditZoneId = useCallback(
    id => {
      const newLocalState = menuProps.localState.set('editingZoneId', id);
      menuProps.onChange({
        localState: newLocalState,
        persistedState: menuProps.persistedState,
      });
    },
    [menuProps]
  );

  useEffect(() => {
    setEditZoneId(menuType.includes('hover') ? menuProps.zone.get('id') : null);
  }, [setEditZoneId, menuType, menuProps]);

  const Menu = menuMap[menuType];

  return (
    <>
      <AdvancedStylingButton
        isMenuButton
        isActive={isActive}
        onClick={toggleDropdown}
        {...buttonProps}
      />

      {isActive && <Menu {...menuProps} setMenuType={setMenuType} />}
    </>
  );
};

ButtonContainerStyles.propTypes = {
  localState: PropTypes.instanceOf(Map),
  persistedState: PropTypes.instanceOf(Map),
  onChange: PropTypes.func,
  onToggleActive: PropTypes.func,
  isActive: PropTypes.bool,
  hasRoomToRenderBelow: PropTypes.bool,
  zone: PropTypes.instanceOf(Map),
};

export default ButtonContainerStyles;
