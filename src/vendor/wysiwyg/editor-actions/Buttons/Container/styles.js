import styled from 'styled-components';

export const overrideActionsMenuStyles = {
  padding: '0 0 20px 0',
  width: '264px',
};

export const overrideActionsOpenedMenuStyles = {
  ...overrideActionsMenuStyles,
  width: '282px',
};

export const Grid = styled.div`
  display: grid;
  grid-row: auto;
  gap: 25px;
`;

export const Row = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  cursor: pointer;
`;

export const Container = styled.div`
  padding: 10px 16px 0 16px;

  ${Grid} {
    margin-top: 16px;
    gap: 0;

    ${Row}:nth-child(2) {
      margin-bottom: 15px;
    }

    ${Row}:first-child {
      margin-bottom: 25px;
    }
  }
`;

export const Header = styled.div`
  padding: 10px 16px;
  display: grid;
  grid-template-columns: 10px auto;
  gap: 10px;
  cursor: pointer;
  color: var(--background-x-light);
  align-items: center;
`;

export const Body = styled.div`
  padding: 0 24px;
  display: flex;
  flex-direction: column;
  gap: 9px;

  ${Grid} {
    margin: 7px 0 16px 0;
  }
`;

export const Title = styled.h5`
  font-size: var(--x-small);
  font-weight: var(--bold);
  color: var(--background-x-light);
  text-transform: uppercase;
  margin: 0;
  line-height: 180%;

  :focus:not(:focus-visible) {
    outline: none;
  }
`;

export const SubTitle = styled.p`
  font-size: var(--regular);
  font-weight: var(--bold);
  color: var(--background);
  margin: 0;
`;

export const CurrentColorWrapper = styled.div`
  margin-right: 20px;
`;

export const ChildMenu = styled.div`
  margin-left: 300px;
  position: absolute;
  top: 16px;
`;

export const Column = styled.div`
  display: flex;
  gap: 20px;
  align-items: center;
`;

export const Separator = styled.hr`
  border: 1px solid var(--tint-black);
  margin-bottom: 14px;
  margin-top: 24px;
`;
