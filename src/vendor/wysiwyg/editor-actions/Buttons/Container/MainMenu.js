import React, { useCallback, useEffect, useRef } from 'react';
import PropTypes from 'prop-types';
import { Map } from 'immutable';

import InputWithSuffix from 'ext/components/InputWithSuffix';
import { CurrentColor } from 'ext/components/ui';
import ActionsMenu from 'vendor/wysiwyg/components/ActionsMenu';
import { menuTypes } from './constants';
import {
  Title,
  Grid,
  Row,
  SubTitle,
  Container,
  overrideActionsMenuStyles,
  CurrentColorWrapper,
  Column,
  Separator,
} from './styles';

const MainMenu = ({
  hasRoomToRenderBelow,
  setMenuType,
  persistedState,
  localState,
  onChange,
}) => {
  const titleRef = useRef(null);
  const borderColor = persistedState.get('inlineBorderColor');
  const inlineBorderWidth = persistedState.get('inlineBorderWidth');
  const hoverBorderColor = persistedState.get('hoverBorderColor');
  const hoverBorderWidth = persistedState.get('hoverBorderWidth');
  // since we launched thinking only about inline styles
  // we saved the value as backgroundColor
  // but now we need to differentiate between
  // inline and hover styles.
  // so, in order to keep backwards compatibility
  // we first look for backgroundColor
  // which should exist only if the user don't update the color.
  // the next update it won't exist anymore so then we will start looking
  // only for inlineBackgroundColor
  const backgroundColor =
    persistedState.get('backgroundColor') ??
    persistedState.get('inlineBackgroundColor');
  const hoverBackgroundColor = persistedState.get('hoverBackgroundColor');
  const dropShadow = JSON.parse(persistedState.get('inlineDropShadow') || '{}');
  const hoverDropShadow = JSON.parse(
    persistedState.get('hoverDropShadow') || '{}'
  );

  const handleChangeType = useCallback(
    type => () => setMenuType(type),
    [setMenuType]
  );

  useEffect(() => {
    titleRef.current?.focus();
  }, [titleRef]);

  const handleBorderWidthChange = (type, value) => {
    const key = `${type}BorderWidth`;
    if (!value) setPersistedState(key, '');
    else if (value.length < 4) setPersistedState(key, value);
  };

  const setPersistedState = (key, value) => {
    const newPersistedState = persistedState.set(key, value);
    onChange({
      localState,
      persistedState: newPersistedState,
    });
  };

  return (
    <ActionsMenu
      isOpen
      position={hasRoomToRenderBelow ? 'bottom' : 'top'}
      role="toolbar"
      aria-label="Button container styles menu"
      style={overrideActionsMenuStyles}
      shouldHaveScrollBar
    >
      <Container>
        <Title tabIndex="0" ref={titleRef}>
          default state
        </Title>

        <Grid>
          <Row
            tabIndex="0"
            role="button"
            aria-label={`click to update fill & opacity, current color ${
              backgroundColor ?? 'unset'
            }`}
            onClick={handleChangeType(menuTypes.fillOpacity)}
            onKeyDown={handleChangeType(menuTypes.fillOpacity)}
          >
            <SubTitle>Fill & opacity</SubTitle>
            <CurrentColorWrapper>
              <CurrentColor color={backgroundColor} />
            </CurrentColorWrapper>
          </Row>
          <Row
            tabIndex="0"
            role="button"
            aria-label="click to update the button drop shadow"
            onClick={handleChangeType(menuTypes.dropShadow)}
            onKeyDown={handleChangeType(menuTypes.dropShadow)}
          >
            <SubTitle>Drop shadow</SubTitle>
            <CurrentColorWrapper>
              <CurrentColor color={dropShadow?.color} />
            </CurrentColorWrapper>
          </Row>
          <Row>
            <SubTitle>Border</SubTitle>
            <Column>
              <CurrentColor
                tabIndex="0"
                role="button"
                aria-label="click to add border and border color to the button"
                onClick={handleChangeType(menuTypes.borderColor)}
                onKeyDown={handleChangeType(menuTypes.borderColor)}
                color={borderColor}
              />
              <InputWithSuffix
                onChange={value => handleBorderWidthChange('inline', value)}
                value={inlineBorderWidth}
                placeholder="-"
                suffix="px"
              />
            </Column>
          </Row>
        </Grid>
      </Container>
      <Separator />
      <Container>
        <Title>hover state</Title>
        <Grid>
          <Row
            tabIndex="0"
            role="button"
            aria-label={`click to update fill & opacity of the hover state, current color ${
              hoverBackgroundColor ?? 'unset'
            }`}
            onClick={handleChangeType(menuTypes.hoverFillOpacity)}
            onKeyDown={handleChangeType(menuTypes.hoverFillOpacity)}
          >
            <SubTitle>Fill & opacity</SubTitle>
            <CurrentColorWrapper>
              <CurrentColor color={hoverBackgroundColor} />
            </CurrentColorWrapper>
          </Row>
          <Row
            tabIndex="0"
            role="button"
            aria-label="click to update the button hover drop shadow"
            onClick={handleChangeType(menuTypes.hoverDropShadow)}
            onKeyDown={handleChangeType(menuTypes.hoverDropShadow)}
          >
            <SubTitle>Drop shadow</SubTitle>
            <CurrentColorWrapper>
              <CurrentColor color={hoverDropShadow?.color} />
            </CurrentColorWrapper>
          </Row>
          <Row>
            <SubTitle>Border</SubTitle>
            <Column>
              <CurrentColor
                tabIndex="0"
                role="button"
                aria-label="click to add border and border color to the button on hover"
                onClick={handleChangeType(menuTypes.hoverBorderColor)}
                onKeyDown={handleChangeType(menuTypes.hoverBorderColor)}
                color={hoverBorderColor}
              />
              <InputWithSuffix
                onChange={value => handleBorderWidthChange('hover', value)}
                value={hoverBorderWidth}
                placeholder="-"
                suffix="px"
                onFocus={() => setPersistedState('inputHoverLock', 'true')}
                onBlur={() => setPersistedState('inputHoverLock', '')}
              />
            </Column>
          </Row>
        </Grid>
      </Container>
    </ActionsMenu>
  );
};

MainMenu.propTypes = {
  localState: PropTypes.instanceOf(Map).isRequired,
  persistedState: PropTypes.instanceOf(Map).isRequired,
  onChange: PropTypes.func.isRequired,
  hasRoomToRenderBelow: PropTypes.bool.isRequired,
  setMenuType: PropTypes.func.isRequired,
};

export default MainMenu;
