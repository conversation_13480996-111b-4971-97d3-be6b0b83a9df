import React from 'react';
import PropTypes from 'prop-types';
import { Map } from 'immutable';
import throttle from 'lodash.throttle';

import { generateColor } from 'ext/lib/color';

import { menuTypes } from './constants';
import DropShadow from './shared/DropShadow';

const THROTTLE_TIME_IN_MS = 150;
const DEFAULT_RADIX = 10;

const forceZeroIfEmpty = value => (value === '' ? 0 : value);

const DropShadowContainer = ({
  localState,
  persistedState,
  onChange,
  hasRoomToRenderBelow,
  setMenuType,
  type,
}) => {
  const getHandleFactory = (persistedKey, key, formatValue = value => value) =>
    throttle(value => {
      const dropShadow = JSON.parse(persistedState.get(persistedKey) ?? `{}`);

      dropShadow[key] = formatValue(value);

      const newPersistedState = persistedState.set(
        persistedKey,
        JSON.stringify(dropShadow)
      );

      onChange({
        localState,
        persistedState: newPersistedState,
      });
    }, THROTTLE_TIME_IN_MS);

  const persistedKey = `${type}DropShadow`;
  const dropShadow = JSON.parse(persistedState.get(persistedKey) ?? '{}');

  return (
    <DropShadow
      initialAngle={parseInt(dropShadow?.angle ?? 0, DEFAULT_RADIX)}
      initialDistance={parseInt(dropShadow?.distance ?? 0, DEFAULT_RADIX)}
      initialBlur={parseInt(dropShadow?.blur ?? 0, DEFAULT_RADIX)}
      initialSize={parseInt(dropShadow?.suze ?? 0, DEFAULT_RADIX)}
      initialColor={dropShadow?.color}
      hasRoomToRenderBelow={hasRoomToRenderBelow}
      setMenuType={setMenuType}
      onAngleChange={getHandleFactory(persistedKey, 'angle')}
      onDistanceChange={getHandleFactory(
        persistedKey,
        'distance',
        forceZeroIfEmpty
      )}
      onBlurChange={getHandleFactory(persistedKey, 'blur', forceZeroIfEmpty)}
      onSizeChange={getHandleFactory(persistedKey, 'size', forceZeroIfEmpty)}
      onColorChange={getHandleFactory(persistedKey, 'color', color =>
        generateColor(color)
      )}
      onGoBack={() => setMenuType(menuTypes.main)}
      type={type}
    />
  );
};

DropShadowContainer.propTypes = {
  localState: PropTypes.instanceOf(Map).isRequired,
  persistedState: PropTypes.instanceOf(Map).isRequired,
  onChange: PropTypes.func.isRequired,
  hasRoomToRenderBelow: PropTypes.bool.isRequired,
  setMenuType: PropTypes.func.isRequired,
  type: PropTypes.oneOf(['inline', 'hover']),
};

export default DropShadowContainer;
