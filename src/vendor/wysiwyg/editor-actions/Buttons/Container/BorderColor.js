import React, { useEffect, useRef } from 'react';
import tinycolor from 'tinycolor2';
import PropTypes from 'prop-types';
import { Map } from 'immutable';

import { generateColor } from 'ext/lib/color';
import { ColorPicker } from 'ext/components/ColorPicker';
import ActionsMenu from 'vendor/wysiwyg/components/ActionsMenu';
import FontIcon from 'vendor/wysiwyg/components/FontAwesome/FontIcon';

import { Title, Header, overrideActionsOpenedMenuStyles } from './styles';
import { menuTypes, BORDER_DEFAULT_COLOR } from './constants';

const BorderColor = ({
  hasRoomToRenderBelow,
  setMenuType,
  localState,
  persistedState,
  onChange,
  type,
}) => {
  const titleRef = useRef();

  useEffect(() => {
    titleRef.current?.focus();
  }, [titleRef]);

  const handleChangeComplete = color => {
    const value = generateColor(color);
    const newPersistedState = persistedState.set(`${type}BorderColor`, value);
    onChange({
      localState,
      persistedState: newPersistedState,
    });
  };

  const handleGoBack = () => {
    setMenuType(menuTypes.main);
  };

  const currentValue =
    persistedState.get(`${type}BorderColor`) ?? BORDER_DEFAULT_COLOR;
  const color = tinycolor(currentValue).toRgb();

  return (
    <ActionsMenu
      isOpen
      position={hasRoomToRenderBelow ? 'bottom' : 'top'}
      role="toolbar"
      aria-labelledby="title"
      style={overrideActionsOpenedMenuStyles}
    >
      <Header onClick={handleGoBack} onKeyDown={handleGoBack}>
        <FontIcon size="1x" icon="angle-left" />
        <Title
          tabIndex="0"
          id="title"
          aria-label={`${type} border color opened, click to go back`}
          ref={titleRef}
        >
          border
        </Title>
      </Header>

      <ColorPicker color={color} onChangeComplete={handleChangeComplete} />
    </ActionsMenu>
  );
};

BorderColor.propTypes = {
  localState: PropTypes.instanceOf(Map).isRequired,
  persistedState: PropTypes.instanceOf(Map).isRequired,
  onChange: PropTypes.func.isRequired,
  hasRoomToRenderBelow: PropTypes.bool.isRequired,
  setMenuType: PropTypes.func.isRequired,
  type: PropTypes.oneOf(['inline', 'hover']),
};

export default BorderColor;
