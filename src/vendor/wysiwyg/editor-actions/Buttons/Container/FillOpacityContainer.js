import React, { useEffect, useRef } from 'react';
import PropTypes from 'prop-types';
import { Map } from 'immutable';

import { generateColor } from 'ext/lib/color';
import FillOpacity from './shared/FillOpacity';

import { COLOR_PICKER_DEFAULT_COLOR, menuTypes } from './constants';

const FillOpacityContainer = ({
  hasRoomToRenderBelow,
  setMenuType,
  localState,
  persistedState,
  onChange,
  type,
}) => {
  const titleRef = useRef();
  const persistedKey = `${type}BackgroundColor`;
  const hasOldBackgroundColor = persistedState.get('backgroundColor');

  useEffect(() => {
    titleRef.current?.focus();
  }, [titleRef]);

  const handleOnColorChange = color => {
    const value = generateColor(color);
    const newPersistedState = persistedState.set(persistedKey, value);

    if (hasOldBackgroundColor) {
      persistedState.delete('backgroundColor');
    }

    onChange({
      localState,
      persistedState: newPersistedState,
    });
  };

  // since we launched thinking only about inline styles
  // we save the value as backgroundColor
  // but now we need to differentiate between
  // inline and hover styles.
  // so, in order to keep backwards compatibility
  // we first check if hasOldBackgroundColor to show the current value
  // and if hasOldBackgroundColor is true, in the first update color call we remove it.
  // check line 30
  const currentValue = hasOldBackgroundColor
    ? persistedState.get('backgroundColor')
    : persistedState.get(persistedKey) ?? COLOR_PICKER_DEFAULT_COLOR;

  return (
    <FillOpacity
      hasRoomToRenderBelow={hasRoomToRenderBelow}
      currentValue={currentValue}
      onGoBack={() => setMenuType(menuTypes.main)}
      onColorChange={handleOnColorChange}
      type={type}
    />
  );
};

FillOpacityContainer.propTypes = {
  localState: PropTypes.instanceOf(Map).isRequired,
  persistedState: PropTypes.instanceOf(Map).isRequired,
  onChange: PropTypes.func.isRequired,
  hasRoomToRenderBelow: PropTypes.bool.isRequired,
  setMenuType: PropTypes.func.isRequired,
  type: PropTypes.oneOf(['inline', 'hover']),
};

export default FillOpacityContainer;
