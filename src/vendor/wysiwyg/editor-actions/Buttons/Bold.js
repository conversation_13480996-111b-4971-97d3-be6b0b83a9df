import React from 'react';
import PropTypes from 'prop-types';
import { Map } from 'immutable';
import { getButtonProps } from '../../helpers/styles/editor';
import BoldButton from '../../icons/BoldButton';

export default class Bold extends React.Component {
  handleFormat(isBold) {
    const { localState, persistedState, onChange, track } = this.props;
    const newPersistedState = persistedState.set('isBold', !isBold);

    track('Builder interaction', {
      name: 'Button - Clicked Bold',
      component: 'Buttons/Bold',
    });

    onChange({
      localState,
      persistedState: newPersistedState,
    });
  }

  render() {
    const { persistedState } = this.props;
    const isBold = persistedState.get('isBold');
    const buttonProps = getButtonProps(isBold);

    return (
      <BoldButton
        isActive={isBold}
        onClick={() => this.handleFormat(isBold)}
        {...buttonProps}
      />
    );
  }
}

Bold.propTypes = {
  localState: PropTypes.instanceOf(Map),
  persistedState: PropTypes.instanceOf(Map),
  onChange: PropTypes.func,
  track: PropTypes.func,
};
