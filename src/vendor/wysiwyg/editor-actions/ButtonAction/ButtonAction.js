import React from 'react';
import PropTypes from 'prop-types';
import { Map, List } from 'immutable';
import debounce from 'lodash.debounce';
import { connect } from 'react-redux';
import { isValidURl } from 'ext/lib/url';
import { sortList } from 'ext/lib/sort-list';
import { Alert } from '@appcues/sonar';
import { flowShape } from 'entities/flows';
import {
  getButtonProps,
  inputStyle,
  menuTextStyle,
  secondaryMenuTitleStyle,
  flexColumn,
  RadioGroup,
  Option,
  OptionItem,
  OptionItemBody,
  OptionsContainer,
  InputContainer,
  ErrorMessage,
} from '../../helpers/styles/editor';
import {
  BUTTON_ACTION_TYPES,
  BUTTON_ACTIONS_WITH_DATA_STEP_ATTRS,
  DEFAULT_USER_PROPS,
  BUTTON_ACTION_TYPES_LIST,
} from '../../helpers/constants';
import {
  isTimestamp,
  animateWhenTogglingOff,
  getActionsByPatternType,
} from '../../helpers/utils';
import { getUserPropertyType } from '../../helpers/customProperties';
import ActionsMenu from '../../components/ActionsMenu';
import DropDownMenu from '../../components/DropDownMenu';
import Radio from '../../components/Radio';
import Toggle from '../../components/Toggle';

import ActionButton from '../../icons/ActionButton';
import UserProperties from './UserProperties';
import AddProfileAttribute from './AddProfileAttribute';
import StepContext from '../../step-context';

const triggerFlowURLStatus = {
  onThisUrl: 'on_this_url',
  onAnotherUrl: 'on_another_url',
};

const WITH_CUSTOM_OPTIONS_TYPES = new Set([
  BUTTON_ACTION_TYPES.APPCUES,
  BUTTON_ACTION_TYPES.URL,
  BUTTON_ACTION_TYPES.CUSTOM_PAGE,
]);

class ButtonAction extends React.Component {
  constructor(props) {
    super(props);

    const href = props.href || '';

    this.state = {
      href,
      isHrefValid: isValidURl(href),
      isNewWindow: props.isNewWindow || false,
      markCurrentFlowAsComplete:
        props.markCurrentFlowAsComplete ||
        props.buttonActionType === BUTTON_ACTION_TYPES.URL,
      isMenuOpen: props.isActive || false,
      stepIndex: 0,
      flowId: '',
      eventName: '',
      trackEvent: false,
      userPropertiesToUpdate: Map({}),
      updateUserProperties: false,
      userPropertyDropdownOptions: [],
      triggerFlowURLStatus: triggerFlowURLStatus.onThisUrl,
    };
  }

  componentDidMount() {
    const { userProperties: userPropertyList, buttonActionType } = this.props;

    this.setState({
      buttonActionType: buttonActionType || this.getActionsList()[0].value,
      userPropertyDropdownOptions: [
        ...userPropertyList
          .map(userProperty => {
            return userProperty.toJS();
          })
          .filter(userProperty => {
            // exclude any fields that may be a date
            const isValidField =
              !userProperty.value.startsWith('_') &&
              userProperty.options &&
              userProperty.options.length > 0
                ? !isTimestamp(userProperty.options[0].name)
                : false;

            return (
              !DEFAULT_USER_PROPS.includes(userProperty.value) && isValidField
            );
          }),
      ].map(userProperty => ({
        label: userProperty.name,
        type: getUserPropertyType(userProperty.options),
        ...userProperty,
      })),
    });
  }

  UNSAFE_componentWillReceiveProps(nextProps) {
    const update = {};
    const { persistedState } = nextProps;

    [
      'href',
      'isNewWindow',
      'stepIndex',
      'buttonActionType',
      'flowId',
      'markCurrentFlowAsComplete',
      'eventName',
      'trackEvent',
      'userPropertiesToUpdate',
      'updateUserProperties',
      'triggerFlowURLStatus',
    ].forEach(property => {
      const persistedStateVal = persistedState.get(property);

      if (nextProps[property] !== this.props[property]) {
        update[property] = nextProps[property];
      }
      if (persistedStateVal !== undefined) {
        update[property] = persistedStateVal;
      }
    });

    if (nextProps.isActive !== this.props.isActive) {
      this.setState({
        isMenuOpen: nextProps.isActive,
      });
    }

    if (Object.keys(update).length) {
      this.setState({
        ...update,
        isHrefValid: isValidURl(update.href),
      });
    }
  }

  toggleDropdown() {
    const { onToggleActive, isActive } = this.props;

    this.setState(({ isMenuOpen }) => ({
      isMenuOpen: !isMenuOpen,
    }));

    if (isActive) {
      animateWhenTogglingOff(() => onToggleActive(!isActive));
    } else {
      onToggleActive(!isActive);
    }
  }

  handleHref(e) {
    const href = e.target.value;

    this.setState(
      {
        href,
        isHrefValid: isValidURl(href),
      },
      this.saveAction
    );
  }

  handleClick(e) {
    e.target.focus();
  }

  handleMarkCurrentFlowAsComplete(e) {
    const markCurrentFlowAsComplete = e.target.checked;
    this.setState(
      {
        markCurrentFlowAsComplete,
      },
      this.saveAction
    );
  }

  handleAddEvent(e) {
    const { value } = e.target;
    this.setState(
      {
        eventName: value,
      },
      this.saveAction
    );
  }

  handleIsNewWindow(e) {
    const isNewWindow = e.target.checked;
    this.setState(
      {
        isNewWindow,
      },
      this.saveAction
    );
  }

  handleStepIndex(e) {
    const { value } = e.target;

    this.setState(
      {
        stepIndex: value - 1,
      },
      this.saveAction
    );
  }

  handleAction(value) {
    const markCurrentFlowAsComplete = value === BUTTON_ACTION_TYPES.URL;

    this.setState(
      {
        buttonActionType: value,
        markCurrentFlowAsComplete,
      },
      this.saveAction
    );
  }

  handleAppcuesShow(flowId) {
    const { triggerFlowURLStatus: urlStatus } = this.state;
    const href =
      urlStatus === triggerFlowURLStatus.onThisUrl
        ? ''
        : this.props.flows[flowId].previewUrl;

    this.setState(
      {
        buttonActionType: BUTTON_ACTION_TYPES.APPCUES,
        href,
        isHrefValid: isValidURl(href),
        flowId,
      },
      this.saveAction
    );
  }

  handleTrackEvent(e) {
    const trackEvent = e.target.checked;
    this.setState(
      {
        trackEvent,
      },
      this.saveAction
    );
  }

  handleUpdateUserProperties(e) {
    const updateUserProperties = e.target.checked;
    this.setState(
      {
        updateUserProperties,
      },
      this.saveAction
    );
  }

  handleOnTriggerFlowURLTypeChange(e) {
    const newStatus = e.target.value;
    const { flowId } = this.state;
    const href =
      newStatus === triggerFlowURLStatus.onThisUrl
        ? ''
        : this.props.flows[flowId].previewUrl;

    this.setState(
      {
        triggerFlowURLStatus: newStatus,
        href,
        isHrefValid: isValidURl(href),
      },
      this.saveAction
    );
  }

  debouncedUpdateUserProperty = debounce(this.updateUserProperty, 500);

  updateUserProperty(key, val) {
    const { userPropertiesToUpdate } = this.state;
    this.setState(
      {
        userPropertiesToUpdate: userPropertiesToUpdate.set(key, val),
      },
      this.saveAction
    );
  }

  debouncedDeleteUserProperty = debounce(this.deleteUserProperty, 500);

  deleteUserProperty(key) {
    const { userPropertiesToUpdate } = this.state;
    this.setState(
      { userPropertiesToUpdate: userPropertiesToUpdate.delete(key) },
      this.saveAction
    );
  }

  getHrefInputBorderColor() {
    return {
      borderColor: !this.state.isHrefValid ? 'var(--warning)' : 'transparent',
    };
  }

  getPersistedStateByButtonActionType(
    buttonActionType,
    persistedState,
    state = {}
  ) {
    const { stepIndex } = this.state;

    if (buttonActionType === BUTTON_ACTION_TYPES.END_FLOW) {
      return persistedState
        .set('buttonActionType', buttonActionType)
        .set('markCurrentFlowAsComplete', state.markCurrentFlowAsComplete)
        .set('eventName', state.eventName)
        .set('trackEvent', state.trackEvent)
        .set('userPropertiesToUpdate', state.userPropertiesToUpdate)
        .set('updateUserProperties', state.updateUserProperties)
        .delete('href')
        .delete('flowId')
        .delete('stepIndex')
        .delete('isNewWindow');
    }

    if (BUTTON_ACTIONS_WITH_DATA_STEP_ATTRS.includes(buttonActionType)) {
      return persistedState
        .set('buttonActionType', buttonActionType)
        .delete('markCurrentFlowAsComplete')
        .set('eventName', state.eventName)
        .set('trackEvent', state.trackEvent)
        .set('userPropertiesToUpdate', state.userPropertiesToUpdate)
        .set('updateUserProperties', state.updateUserProperties)
        .delete('href')
        .delete('flowId')
        .delete('stepIndex')
        .delete('isNewWindow');
    }

    if (
      buttonActionType === BUTTON_ACTION_TYPES.CUSTOM_PAGE &&
      stepIndex !== undefined
    ) {
      return persistedState
        .set('buttonActionType', buttonActionType)
        .set('stepIndex', stepIndex)
        .delete('markCurrentFlowAsComplete')
        .set('eventName', state.eventName)
        .set('trackEvent', state.trackEvent)
        .set('userPropertiesToUpdate', state.userPropertiesToUpdate)
        .set('updateUserProperties', state.updateUserProperties)
        .delete('href')
        .delete('flowId')
        .delete('isNewWindow');
    }

    if (buttonActionType === BUTTON_ACTION_TYPES.URL) {
      return persistedState
        .set('buttonActionType', buttonActionType)
        .set('href', state.href)
        .set('isNewWindow', state.isNewWindow)
        .set('markCurrentFlowAsComplete', state.markCurrentFlowAsComplete)
        .set('eventName', state.eventName)
        .set('trackEvent', state.trackEvent)
        .set('userPropertiesToUpdate', state.userPropertiesToUpdate)
        .set('updateUserProperties', state.updateUserProperties)
        .delete('stepIndex')
        .delete('flowId');
    }

    if (buttonActionType === BUTTON_ACTION_TYPES.APPCUES) {
      return persistedState
        .set('buttonActionType', buttonActionType)
        .set('flowId', state.flowId)
        .delete('markCurrentFlowAsComplete')
        .set('eventName', state.eventName)
        .set('trackEvent', state.trackEvent)
        .set('userPropertiesToUpdate', state.userPropertiesToUpdate)
        .set('updateUserProperties', state.updateUserProperties)
        .set('triggerFlowURLStatus', state.triggerFlowURLStatus)
        .set('href', state.href)
        .delete('isNewWindow')
        .delete('stepIndex');
    }

    return persistedState;
  }

  saveAction() {
    const { localState, persistedState, onChange, track } = this.props;
    const {
      isNewWindow,
      href,
      buttonActionType,
      markCurrentFlowAsComplete,
      flowId,
      eventName,
      trackEvent,
      userPropertiesToUpdate,
      updateUserProperties,
      triggerFlowURLStatus,
    } = this.state;

    const newPersistedState = this.getPersistedStateByButtonActionType(
      buttonActionType,
      persistedState,
      {
        href,
        isNewWindow,
        markCurrentFlowAsComplete,
        flowId,
        eventName,
        trackEvent,
        userPropertiesToUpdate,
        updateUserProperties,
        triggerFlowURLStatus,
      }
    );
    track('Builder interaction', {
      name: 'Button - action set',
      actionType: buttonActionType,
    });

    onChange({
      localState,
      persistedState: newPersistedState,
    });
  }

  getActionButtonText() {
    const { buttonActionType } = this.state;

    if (buttonActionType) {
      return BUTTON_ACTION_TYPES_LIST.find(it => it.value === buttonActionType)
        .label;
    }

    return '';
  }

  getActionsList() {
    const { patternType } = this.props;
    const { buttonActionType } = this.state;
    const { PREVIOUS_PAGE } = BUTTON_ACTION_TYPES;

    const { isFirstStep } = this.context;

    const getActionsParams =
      isFirstStep && buttonActionType !== PREVIOUS_PAGE
        ? [`${patternType}-first`, [PREVIOUS_PAGE]]
        : [patternType];

    return getActionsByPatternType(...getActionsParams);
  }

  render() {
    const { isActive, hasRoomToRenderBelow, numPages } = this.props;
    const {
      href,
      isHrefValid,
      isNewWindow,
      isMenuOpen,
      buttonActionType,
      stepIndex,
      flowId,
      markCurrentFlowAsComplete,
      eventName,
      trackEvent,
      userPropertiesToUpdate,
      updateUserProperties,
      userPropertyDropdownOptions,
    } = this.state;

    const buttonProps = getButtonProps(isActive);

    const hasMoreThanOneStep = numPages > 1;

    const actionsMenuProps = {
      isOpen: isMenuOpen,
      position: hasRoomToRenderBelow ? 'bottom' : 'top',
      style: {
        ...flexColumn,
        width: '400px',
        maxHeight: '338px',
        overflow: 'auto',
      },
    };

    const dropdownNodes = isActive ? (
      <ActionsMenu {...actionsMenuProps}>
        <OptionsContainer>
          <div style={secondaryMenuTitleStyle}>Button action</div>

          <InputContainer
            selected={!WITH_CUSTOM_OPTIONS_TYPES.has(buttonActionType)}
          >
            <DropDownMenu
              className="form-control"
              id="button-action-menu"
              unsearchable
              selectedValue={buttonActionType}
              options={this.getActionsList()}
              onSelect={value => this.handleAction(value)}
            />
          </InputContainer>

          {buttonActionType === BUTTON_ACTION_TYPES.URL && (
            <>
              <InputContainer selected>
                <AddProfileAttribute
                  userProperties={userPropertyDropdownOptions}
                >
                  {({ handleOnBlur, handleOnChange, inputRef }) => (
                    <>
                      <input
                        ref={inputRef}
                        id="url-input-field"
                        type="text"
                        style={{
                          ...inputStyle,
                          ...this.getHrefInputBorderColor(),
                        }}
                        defaultValue={href || ''}
                        onBlur={handleOnBlur}
                        onClickCapture={this.handleClick}
                        onChange={handleOnChange(this.handleHref.bind(this))}
                        placeholder="URL"
                      />

                      {!isHrefValid && (
                        <ErrorMessage>{href} is not a valid URL</ErrorMessage>
                      )}
                    </>
                  )}
                </AddProfileAttribute>
              </InputContainer>

              <Option>
                <OptionItem>
                  <Toggle
                    label="Open in new window"
                    checked={isNewWindow}
                    onChange={this.handleIsNewWindow.bind(this)}
                  />
                </OptionItem>
                <OptionItem>
                  <Toggle
                    label="Mark flow complete"
                    checked={markCurrentFlowAsComplete}
                    onChange={this.handleMarkCurrentFlowAsComplete.bind(this)}
                  />
                </OptionItem>
              </Option>
            </>
          )}

          {buttonActionType === BUTTON_ACTION_TYPES.CUSTOM_PAGE && (
            <InputContainer selected>
              <input
                onClickCapture={this.handleClick}
                type="number"
                min={1}
                max={numPages}
                value={hasMoreThanOneStep ? stepIndex + 1 : 1}
                disabled={!hasMoreThanOneStep}
                style={inputStyle}
                onChange={e => this.handleStepIndex(e)}
              />
              <p
                style={{
                  ...menuTextStyle,
                  marginTop: '8px',
                  lineHeight: '16px',
                }}
              >
                {`This group contains ${numPages === 1 ? 'only' : ''} ${
                  numPages || 'an unknown number of'
                } step${hasMoreThanOneStep ? 's' : ''}. ${
                  hasMoreThanOneStep
                    ? 'Set a number to this button to direct users to that specific step.'
                    : ''
                }`}
              </p>
            </InputContainer>
          )}

          {buttonActionType === BUTTON_ACTION_TYPES.APPCUES && (
            <>
              <InputContainer selected>
                <DropDownMenu
                  className="form-control"
                  selectedValue={flowId}
                  searchPlaceholder="Select a flow..."
                  typeahead
                  unsearchable
                  options={sortList(
                    Object.values(this.props.flows),
                    this.props.flow.id
                  ).map(it => ({
                    label: it.name,
                    value: it.id,
                  }))}
                  onSelect={value => this.handleAppcuesShow(value)}
                />
              </InputContainer>

              {flowId && (
                <RadioGroup>
                  <Radio
                    name="urlType"
                    value={triggerFlowURLStatus.onThisUrl}
                    onChange={this.handleOnTriggerFlowURLTypeChange.bind(this)}
                    checked={
                      this.state.triggerFlowURLStatus ===
                      triggerFlowURLStatus.onThisUrl
                    }
                  >
                    On this URL
                  </Radio>
                  <Radio
                    name="urlType"
                    value={triggerFlowURLStatus.onAnotherUrl}
                    onChange={this.handleOnTriggerFlowURLTypeChange.bind(this)}
                    checked={
                      this.state.triggerFlowURLStatus ===
                      triggerFlowURLStatus.onAnotherUrl
                    }
                  >
                    On another URL
                  </Radio>
                </RadioGroup>
              )}

              {this.state.triggerFlowURLStatus ===
                triggerFlowURLStatus.onAnotherUrl &&
                flowId && (
                  <InputContainer selected>
                    <AddProfileAttribute
                      userProperties={userPropertyDropdownOptions}
                    >
                      {({ handleOnBlur, handleOnChange, inputRef }) => (
                        <>
                          <input
                            ref={inputRef}
                            type="text"
                            style={{
                              ...inputStyle,
                              ...this.getHrefInputBorderColor(),
                            }}
                            defaultValue={href || ''}
                            onClickCapture={this.handleClick}
                            onChange={handleOnChange(
                              this.handleHref.bind(this)
                            )}
                            onBlur={handleOnBlur}
                          />

                          {!isHrefValid && (
                            <ErrorMessage>
                              {href} is not a valid URL
                            </ErrorMessage>
                          )}
                        </>
                      )}
                    </AddProfileAttribute>
                  </InputContainer>
                )}
            </>
          )}

          {buttonActionType === BUTTON_ACTION_TYPES.END_FLOW && (
            <Option>
              <OptionItem>
                <Toggle
                  label="Mark flow complete"
                  checked={markCurrentFlowAsComplete}
                  onChange={this.handleMarkCurrentFlowAsComplete.bind(this)}
                />
              </OptionItem>
            </Option>
          )}

          <Option>
            <OptionItem>
              <Toggle
                label="Track event"
                checked={trackEvent}
                onChange={this.handleTrackEvent.bind(this)}
              />

              {trackEvent && (
                <OptionItemBody>
                  <input
                    type="text"
                    placeholder="Enter an event name to track"
                    value={eventName}
                    style={inputStyle}
                    onChange={e => this.handleAddEvent(e)}
                  />
                </OptionItemBody>
              )}
            </OptionItem>

            <OptionItem>
              <Toggle
                label="Update user properties"
                checked={updateUserProperties}
                onChange={this.handleUpdateUserProperties.bind(this)}
              />

              {updateUserProperties && (
                <UserProperties
                  userPropertyDropdownOptions={userPropertyDropdownOptions}
                  userPropertiesToUpdate={userPropertiesToUpdate}
                  onUpdate={this.debouncedUpdateUserProperty.bind(this)}
                  onDelete={this.debouncedDeleteUserProperty.bind(this)}
                />
              )}
            </OptionItem>
          </Option>
          {buttonActionType === BUTTON_ACTION_TYPES.APPCUES && (
            <Alert.Root variant="info">
              <Alert.Message>
                <Alert.Description>
                  Heads up: the next Flow will trigger when this Flow is live,
                  but will not trigger in preview mode.
                </Alert.Description>
              </Alert.Message>
            </Alert.Root>
          )}
        </OptionsContainer>
      </ActionsMenu>
    ) : null;

    return (
      <div>
        <ActionButton
          isMenuButton
          isActive={isActive}
          onClick={() => this.toggleDropdown()}
          text={this.getActionButtonText()}
          {...buttonProps}
        />
        {dropdownNodes}
      </div>
    );
  }
}

ButtonAction.contextType = StepContext;

ButtonAction.propTypes = {
  href: PropTypes.string,
  isNewWindow: PropTypes.bool,
  buttonActionType: PropTypes.string,
  localState: PropTypes.instanceOf(Map),
  persistedState: PropTypes.instanceOf(Map),
  userProperties: PropTypes.instanceOf(List),
  onChange: PropTypes.func,
  onToggleActive: PropTypes.func,
  markCurrentFlowAsComplete: PropTypes.bool,
  isActive: PropTypes.bool,
  hasRoomToRenderBelow: PropTypes.bool,
  numPages: PropTypes.number,
  track: PropTypes.func,
  flows: PropTypes.objectOf(
    PropTypes.shape({
      id: PropTypes.string,
      name: PropTypes.string,
    })
  ),
  patternType: PropTypes.string,
  flow: flowShape,
};

const mapStateToProps = state => {
  return {
    flows: state.flows.flows,
    flow: state.flows.flow,
  };
};

export default connect(mapStateToProps, null)(ButtonAction);
