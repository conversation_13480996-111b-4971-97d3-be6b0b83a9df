import React from 'react';
import PropTypes from 'prop-types';
import styled from 'styled-components';
import {
  inputStyle,
  flexJustifyContentSpaceBetween,
  smallInputStyle,
} from '../../helpers/styles/editor';
import FontIcon from '../../components/FontAwesome/FontIcon';
import DropDownMenu from '../../components/DropDownMenu';

const CloseIcon = styled(FontIcon)`
  cursor: pointer;
  path {
    fill: var(--background-x-light);
  }
  position: absolute;
  right: -7;
  width: 1em;

  && {
    display: none;
  }
`;

const Container = styled.div`
  align-items: center,
  cursor: pointer;
  display: flex,
  justify-content: space-between,
  margin-bottom: 5px,
  position: relative,
  width: 100%,  
  

  ${CloseIcon} {
    display: none;
  }

  &:hover > ${CloseIcon} {
    display: block;
  }
`;

const UpdatedUserProperty = ({
  keyValue,
  name,
  type,
  selectedValue,
  onChange,
  onDelete,
}) => {
  return (
    <Container
      key={keyValue}
      style={{
        ...flexJustifyContentSpaceBetween,
        alignItems: 'center',
        position: 'relative',
        width: '100%',
        marginBottom: '5px',
      }}
    >
      <div style={{ flexBasis: '50%' }}>
        <DropDownMenu
          className="form-control"
          defaultValue={name}
          disabled
          options={[]}
          selectedValue={keyValue}
        />
      </div>

      {type !== 'checkbox' ? (
        <input
          type={type}
          style={{
            ...inputStyle,
            ...smallInputStyle,
            marginLeft: 8,
            height: 40,
            flexBasis: '50%',
          }}
          defaultValue={selectedValue}
          onChange={event => {
            const val = event.target.value;
            if (val.trim() === '') {
              onDelete(keyValue);
            } else {
              onChange(keyValue, val);
            }
          }}
        />
      ) : (
        <div style={{ marginLeft: 8, flexBasis: '50%' }}>
          <DropDownMenu
            className="form-control"
            options={[
              { label: 'True', value: true },
              { label: 'False', value: false },
            ]}
            selectedValue={selectedValue}
            onSelect={value => onChange(keyValue, value)}
          />
        </div>
      )}

      <CloseIcon
        icon="times-circle"
        onClick={() => {
          onDelete(keyValue);
        }}
      />
    </Container>
  );
};

UpdatedUserProperty.propTypes = {
  keyValue: PropTypes.string,
  name: PropTypes.string,
  type: PropTypes.string,
  selectedValue: PropTypes.oneOfType([PropTypes.string, PropTypes.bool]),
  onChange: PropTypes.func,
  onDelete: PropTypes.func,
};

export default UpdatedUserProperty;
