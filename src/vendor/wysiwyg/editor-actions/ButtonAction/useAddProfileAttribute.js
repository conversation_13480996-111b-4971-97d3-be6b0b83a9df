import { useRef, useState, useCallback } from 'react';

const CURLY_BRACKETS_SIZE = 2;
const MAX_LENGTH_TO_OPEN_ON_RIGHT = 20;
const isDoubleOpenCurlyBrackets = new RegExp('{{', 'gmi');

// this will guarantee that the onChange event is going to be triggered
// after we change the input value
const addInputValue = (input, value) => {
  const inputValueSetter = Object.getOwnPropertyDescriptor(
    window.HTMLInputElement.prototype,
    'value'
  ).set;
  inputValueSetter.call(input, value);
  input.dispatchEvent(new Event('input', { bubbles: true }));
};

const useAddProfileAttribute = () => {
  const lastCursorPositionIndex = useRef();
  const inputRef = useRef();
  const [state, setState] = useState({
    shouldRenderPortal: false,
    styles: {},
  });

  const openPortal = useCallback(() => {
    const { current: input } = inputRef;

    if (state.shouldRenderPortal || !input) return;

    const {
      selectionEnd,
      value: { length },
    } = input;
    const positionPrefix =
      selectionEnd > length / 2 && length > MAX_LENGTH_TO_OPEN_ON_RIGHT
        ? ''
        : '-';

    setState({
      shouldRenderPortal: true,
      styles: {
        left: `${positionPrefix}94px`,
        position: 'absolute',
        top: '-10px',
      },
    });

    input.setSelectionRange(selectionEnd, selectionEnd);
  }, [state.shouldRenderPortal]);

  const closeMenu = useCallback(() => {
    setState({
      shouldRenderPortal: false,
      styles: {},
    });
  }, [setState]);

  const handleInsertValue = useCallback(
    propertyValue => {
      const { current: input } = inputRef;
      const { value, selectionEnd } = input;
      const optionString = `${propertyValue}}}`;
      const newSelectionEnd = selectionEnd + optionString.length;

      addInputValue(
        input,
        value.slice(0, selectionEnd) + optionString + value.slice(selectionEnd)
      );

      input.focus();
      input.setSelectionRange(newSelectionEnd, newSelectionEnd);
      closeMenu();
    },
    [closeMenu]
  );

  const handleOnChange = useCallback(
    onChangeCallback => e => {
      e.persist();
      const lastTwoLetters = e.target.value.slice(-2);

      if (isDoubleOpenCurlyBrackets.test(lastTwoLetters)) {
        openPortal();
      }

      onChangeCallback(e);
    },
    [openPortal]
  );

  const handleOnBlur = useCallback(e => {
    lastCursorPositionIndex.current = e.target.selectionStart;
  }, []);

  const handleExternalClick = useCallback(() => {
    const { current: input } = inputRef;

    if (!input) return;

    // the input onBlur will always be called before the link click
    // setting the lastCursorPositionIndex value
    // we force the link event click to the next event loop cycle
    // just for security sake
    setTimeout(() => {
      // first insert {{ into the same position the cursor was
      // eslint-disable-next-line unicorn/prefer-spread
      const value = input.value.split('');
      const { current: lastCursorIndex = 0 } = lastCursorPositionIndex;
      // splice will mutate the value that is why we create a `value`
      // variable so we don't mutate the input.value ref directly
      // while at line 92, join will return a new ref anyway
      value.splice(lastCursorIndex, 0, '{{');

      // add the new value
      // force focus and cursor position
      addInputValue(input, value.join(''));
      input.focus();
      input.setSelectionRange(
        lastCursorIndex,
        lastCursorIndex + CURLY_BRACKETS_SIZE
      );

      openPortal();
    }, 0);
  }, [openPortal, inputRef, lastCursorPositionIndex]);

  return {
    handleOnBlur,
    handleOnChange,
    handleExternalClick,
    handleInsertValue,
    inputRef,
    ...state,
  };
};

export default useAddProfileAttribute;
