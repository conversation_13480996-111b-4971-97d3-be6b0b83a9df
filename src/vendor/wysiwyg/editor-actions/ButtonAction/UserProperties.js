import React, { useState, useMemo, useEffect, useCallback } from 'react';
import PropTypes from 'prop-types';
import { Map } from 'immutable';
import styled from 'styled-components';
import {
  inputStyle,
  flexColumn,
  flexJustifyContentSpaceBetween,
  OptionItemBody,
  smallInputStyle,
} from '../../helpers/styles/editor';
import DropDownMenu from '../../components/DropDownMenu';
import UpdatedUserProperty from './UpdatedUserProperty';

const HelpText = styled.div`
  font-size: var(--regular);
  padding: 20px;
  text-align: center;

  a {
    color: var(--secondary);
  }
`;

const UserProperties = ({
  userPropertyDropdownOptions,
  userPropertiesToUpdate,
  onUpdate,
  onDelete,
}) => {
  const [selectedProperty, setSelectedProperty] = useState(null);
  const [remainingUserProperties, setRemainingUserProperties] = useState([]);

  useEffect(() => {
    setRemainingUserProperties(
      userPropertyDropdownOptions.filter(
        property => userPropertiesToUpdate.get(property.value) == null
      )
    );
    setSelectedProperty(null);
  }, [userPropertyDropdownOptions, userPropertiesToUpdate]);

  const getUserProperty = useCallback(
    key => {
      const property = userPropertyDropdownOptions.find(
        option => option.value === key
      );

      return {
        name: property && property.name,
        type: !key ? 'text' : property && property.type,
      };
    },
    [userPropertyDropdownOptions]
  );

  const propertyType = useMemo(() => {
    return getUserProperty(selectedProperty).type;
  }, [selectedProperty, getUserProperty]);

  return (
    <>
      {/* If there are dropdown options available, render UI for setting user properties. Otherwise, render message */}
      {userPropertyDropdownOptions.length > 0 ? (
        <OptionItemBody>
          {userPropertiesToUpdate.size > 0 && (
            <div style={{ ...flexColumn, alignItems: 'center' }}>
              {Object.keys(userPropertiesToUpdate.toJS()).map(key => {
                const { name, type } = getUserProperty(key);
                const selectedValue = userPropertiesToUpdate.get(key);

                return (
                  <UpdatedUserProperty
                    key={key}
                    name={name}
                    type={type}
                    keyValue={key}
                    selectedValue={selectedValue}
                    onChange={onUpdate}
                    onDelete={onDelete}
                  />
                );
              })}
            </div>
          )}

          {remainingUserProperties.length > 0 && (
            <div style={{ ...flexColumn, alignItems: 'center' }}>
              <div
                style={{
                  ...flexJustifyContentSpaceBetween,
                  alignItems: 'center',
                  position: 'relative',
                  width: '100%',
                }}
              >
                <div
                  aria-label="User property dropdown"
                  style={{ flexBasis: '50%' }}
                >
                  <DropDownMenu
                    className="form-control"
                    defaultValue="Choose property"
                    options={remainingUserProperties}
                    selectedValue={selectedProperty}
                    onSelect={value => setSelectedProperty(value)}
                  />
                </div>
                {propertyType !== 'checkbox' && (
                  <input
                    type={propertyType}
                    key={selectedProperty}
                    disabled={!selectedProperty}
                    style={{
                      ...inputStyle,
                      ...smallInputStyle,
                      marginLeft: 8,
                      height: 40,
                      flexBasis: '50%',
                    }}
                    placeholder="Set value"
                    onChange={event => {
                      const val = event.target.value;
                      if (val.trim() !== '') {
                        onUpdate(selectedProperty, event.target.value);
                      }
                    }}
                  />
                )}
                {propertyType === 'checkbox' && (
                  <div style={{ marginLeft: 8, flexBasis: '50%' }}>
                    <DropDownMenu
                      className="form-control"
                      defaultValue="Set value"
                      options={[
                        { label: 'True', value: true },
                        { label: 'False', value: false },
                      ]}
                      onSelect={value => onUpdate(selectedProperty, value)}
                    />
                  </div>
                )}
              </div>
            </div>
          )}
        </OptionItemBody>
      ) : (
        <HelpText aria-label="help-text">
          <a
            href="https://docs.appcues.com/article/48-install-overview"
            target="_blank"
            rel="noreferrer"
          >
            Install Appcues
          </a>{' '}
          to see your user properties.
        </HelpText>
      )}
    </>
  );
};

UserProperties.propTypes = {
  userPropertyDropdownOptions: PropTypes.instanceOf(Array),
  userPropertiesToUpdate: PropTypes.instanceOf(Map),
  onUpdate: PropTypes.func,
  onDelete: PropTypes.func,
};

export default UserProperties;
