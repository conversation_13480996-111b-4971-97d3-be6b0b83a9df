import React from 'react';
import styled from 'styled-components';
import PropTypes from 'prop-types';

import FontIcon from 'ext/components/ui/FontIcon';
import Tether from 'ext/components/Tether';

import useAddProfileAttribute from './useAddProfileAttribute';

const Wrapper = styled.div`
  display: flex;
  flex-direction: column;
  gap: 10px;
  position: relative;
`;

const Button = styled.button`
  color: var(--secondary);
  cursor: pointer;
  font-size: var(--regular);
  font-weight: var(--normal);
  width: max-content;
  display: inline-flex;
  align-items: center;
  gap: 6px;
  background: transparent;
  border: none;
  padding: 0;
`;

const MenuList = styled.ul`
  background-color: var(--white);
  border-radius: 5px;
  box-shadow: 0px 12px 34px rgba(71, 88, 114, 0.2);
  display: flex;
  flex-direction: column;
  list-style: none;
  max-height: 138px;
  overflow-x: hidden;
  overflow-y: auto;
  padding: 0;
  width: 148px;
  z-index: 150;
`;

const Item = styled.li`
  color: var(--add-profile-item-color);
  cursor: pointer;
  font-size: var(--regular);
  font-weight: var(--normal);
  padding: 8px 8px 8px 24px;

  &:hover {
    background: var(--add-profile-item-hover-bg);
  }
`;

const AddProfileAttribute = ({ children, userProperties }) => {
  const {
    handleOnBlur,
    handleOnChange,
    handleExternalClick,
    handleInsertValue,
    inputRef,
    shouldRenderPortal,
    styles,
  } = useAddProfileAttribute();

  const attachment = ({ ref }) => (
    <MenuList style={styles} ref={ref}>
      {userProperties.map(prop => (
        <Item key={prop.value} onClick={() => handleInsertValue(prop.value)}>
          {prop.label}
        </Item>
      ))}
    </MenuList>
  );

  return (
    <Wrapper>
      <Tether
        allowedPlacements={['bottom']}
        attachment={attachment}
        visible={shouldRenderPortal}
        offset={{ y: 0 }}
        placement="bottom"
        wrapped
      >
        {children({
          handleOnBlur,
          handleOnChange,
          inputRef,
        })}
      </Tether>

      <Button onClick={handleExternalClick}>
        <FontIcon size="sm" icon="plus-circle" />
        Add profile attribute to URL
      </Button>
    </Wrapper>
  );
};

AddProfileAttribute.propTypes = {
  children: PropTypes.func.isRequired,
  userProperties: PropTypes.arrayOf(
    PropTypes.shape({
      value: PropTypes.string.isRequired,
      label: PropTypes.string.isRequired,
    })
  ),
};

export default AddProfileAttribute;
