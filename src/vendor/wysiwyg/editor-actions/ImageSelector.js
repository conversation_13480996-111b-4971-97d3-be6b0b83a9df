import React, { useState } from 'react';
import PropTypes from 'prop-types';
import FileUploadButton from '../icons/FileUploadButton';
import { getButtonProps } from '../helpers/styles/editor';
import ActionsMenu from '../components/ActionsMenu';

const ImageSelector = ({ options }) => {
  const [isSelectorOpen, setIsSelectorOpen] = useState(false);

  const buttonProps = getButtonProps(false);

  return (
    <div className="image-selector">
      <FileUploadButton
        onClick={() => setIsSelectorOpen(!isSelectorOpen)}
        {...buttonProps}
      />
      {isSelectorOpen && (
        <ActionsMenu style={{ padding: '0px', width: '180px' }}>
          {options.map(({ label, Component, props }, index) => (
            <ActionsMenu.MenuItem key={`image-selector-${label ?? index}`}>
              <Component {...props} setIsSelectorOpen={setIsSelectorOpen} />
            </ActionsMenu.MenuItem>
          ))}
        </ActionsMenu>
      )}
    </div>
  );
};

ImageSelector.propTypes = {
  options: PropTypes.arrayOf(
    PropTypes.shape({
      label: PropTypes.string.isRequired,
      Component: PropTypes.elementType.isRequired,
      props: PropTypes.object,
    })
  ).isRequired,
};

export default ImageSelector;
