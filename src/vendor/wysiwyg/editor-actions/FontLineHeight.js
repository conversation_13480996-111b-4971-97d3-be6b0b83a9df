import React from 'react';
import PropTypes from 'prop-types';
import { EditorState, Modifier } from 'draft-js';
import { Map } from 'immutable';
import DropDownMenu from '../components/DropDownMenu';
import { LabeledDropdownContainer } from '../helpers/styles/editor';

const options = [
  {
    label: 'Default',
    value: 'inherit',
  },
  {
    label: '0',
    value: '0',
  },
  {
    label: '0.5',
    value: '0.5',
  },
  {
    label: '1',
    value: '1',
  },
  {
    label: '1.5',
    value: '1.5',
  },
  {
    label: '2',
    value: '2',
  },
];

export default class FontLineHeight extends React.Component {
  handleFormat = value => {
    const { localState, persistedState, onChange, blockData } = this.props;
    const editorState = localState.get('editorState');
    const newContentState = Modifier.setBlockData(
      editorState.getCurrentContent(),
      editorState.getSelection(),
      {
        ...blockData,
        lineHeight: value,
      }
    );

    const newLocalState = localState.set(
      'editorState',
      EditorState.push(editorState, newContentState, 'change-block-data')
    );

    onChange({
      localState: newLocalState,
      persistedState,
    });
  };

  render() {
    const { blockData } = this.props;

    const lineHeight =
      blockData && blockData.lineHeight ? blockData.lineHeight : 'inherit';

    // If currentValue includes "px",
    // (i.e. no em value from our menu was selected, default CSS uses pixels)
    // set "Default" option as the default value
    return (
      <LabeledDropdownContainer>
        <label>Line height</label>
        <DropDownMenu
          unsearchable
          className="form-control"
          selectedValue={lineHeight.includes('px') ? 'inherit' : lineHeight}
          options={options}
          onSelect={this.handleFormat}
        />
      </LabeledDropdownContainer>
    );
  }
}

FontLineHeight.propTypes = {
  localState: PropTypes.instanceOf(Map),
  persistedState: PropTypes.instanceOf(Map),
  onChange: PropTypes.func,
  hasRoomToRenderBelow: PropTypes.bool,
  blockData: PropTypes.object,
};
