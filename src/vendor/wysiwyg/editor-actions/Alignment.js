import React from 'react';
import PropTypes from 'prop-types';
import { Map } from 'immutable';
import ActionsMenu from '../components/ActionsMenu';
import { ALIGNMENT_TYPE } from '../helpers/constants';
import { animateWhenTogglingOff } from '../helpers/utils';
import { getButtonProps } from '../helpers/styles/editor';
import BaseAlignment from '../icons/BaseAlignment';

export default class Alignment extends React.Component {
  toggleDropdown = () => {
    const { onToggleActive, isActive } = this.props;

    if (isActive) {
      animateWhenTogglingOff(() => onToggleActive(!isActive));
    } else {
      onToggleActive(!isActive);
    }
  };

  handleFormat = textAlign => {
    const { localState, persistedState, onChange, customTrack } = this.props;

    customTrack && customTrack();

    onChange({
      textAlign,
      localState,
      persistedState,
    });

    this.toggleDropdown();
  };

  render() {
    const {
      isActive,
      hasRoomToRenderBelow,
      blockData,
      persistedState,
      AlignmentIcon,
      defaultTextAlign,
    } = this.props;
    const persistedTextAlign = persistedState
      ? persistedState.get('textAlign')
      : null;
    const textAlign =
      blockData && blockData.textAlign
        ? blockData.textAlign
        : persistedTextAlign;
    const buttonProps = getButtonProps(isActive);
    const secondaryButtonProps = getButtonProps(false);

    return (
      <>
        <AlignmentIcon
          isMenuButton
          isActive={isActive}
          textAlign={textAlign || defaultTextAlign}
          onClick={() => this.toggleDropdown()}
          hideBackground={buttonProps.hideBackground}
          color={buttonProps.color}
          clickColor={buttonProps.clickColor}
          activeColor={buttonProps.activeColor}
          hoverColor={buttonProps.hoverColor}
        />
        {isActive && (
          <ActionsMenu
            isOpen
            position={hasRoomToRenderBelow ? 'bottom' : 'top'}
            style={{
              padding: '0px 4px',
              width: '48px',
            }}
          >
            <ActionsMenu.MenuItem
              onClick={() => this.handleFormat(ALIGNMENT_TYPE.LEFT)}
              data-testid="alignment-left"
            >
              <AlignmentIcon
                isActive={textAlign === ALIGNMENT_TYPE.LEFT}
                textAlign={ALIGNMENT_TYPE.LEFT}
                hideBackground={secondaryButtonProps.hideBackground}
                color={secondaryButtonProps.color}
                clickColor={secondaryButtonProps.clickColor}
                activeColor={secondaryButtonProps.activeColor}
                hoverColor={secondaryButtonProps.hoverColor}
              />
            </ActionsMenu.MenuItem>
            <ActionsMenu.MenuItem
              onClick={() => this.handleFormat(ALIGNMENT_TYPE.CENTER)}
              data-testid="alignment-center"
            >
              <AlignmentIcon
                isActive={textAlign === ALIGNMENT_TYPE.CENTER}
                textAlign={ALIGNMENT_TYPE.CENTER}
                hideBackground={secondaryButtonProps.hideBackground}
                color={secondaryButtonProps.color}
                clickColor={secondaryButtonProps.clickColor}
                activeColor={secondaryButtonProps.activeColor}
                hoverColor={secondaryButtonProps.hoverColor}
              />
            </ActionsMenu.MenuItem>
            <ActionsMenu.MenuItem
              onClick={() => this.handleFormat(ALIGNMENT_TYPE.RIGHT)}
              data-testid="alignment-right"
            >
              <AlignmentIcon
                isActive={textAlign === ALIGNMENT_TYPE.RIGHT}
                textAlign={ALIGNMENT_TYPE.RIGHT}
                hideBackground={secondaryButtonProps.hideBackground}
                color={secondaryButtonProps.color}
                clickColor={secondaryButtonProps.clickColor}
                activeColor={secondaryButtonProps.activeColor}
                hoverColor={secondaryButtonProps.hoverColor}
              />
            </ActionsMenu.MenuItem>
          </ActionsMenu>
        )}
      </>
    );
  }
}

Alignment.propTypes = {
  onChange: PropTypes.func,
  localState: PropTypes.instanceOf(Map),
  persistedState: PropTypes.instanceOf(Map),
  onToggleActive: PropTypes.func,
  isActive: PropTypes.bool,
  hasRoomToRenderBelow: PropTypes.bool,
  blockData: PropTypes.shape({
    textAlign: PropTypes.oneOf([
      ALIGNMENT_TYPE.LEFT,
      ALIGNMENT_TYPE.CENTER,
      ALIGNMENT_TYPE.RIGHT,
    ]),
  }),
  AlignmentIcon: PropTypes.elementType,
  defaultTextAlign: PropTypes.oneOf(['right', 'center', 'left']),
  customTrack: PropTypes.func,
};

Alignment.defaultProps = {
  AlignmentIcon: BaseAlignment,
  defaultTextAlign: 'left',
};
