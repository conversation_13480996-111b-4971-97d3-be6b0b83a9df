import React, { useState } from 'react';
import PropTypes from 'prop-types';
import styled from 'styled-components';

const FormGroup = styled.div`
  display: flex;
  flex-direction: column;
`;

const Label = styled.label`
  color: #666;
  font-size: var(--regular);
  font-weight: var(--bold);
  padding-bottom: 5px;
`;

const Input = styled.input`
  width: 4em;
  outline: none;
  display: flex;
  border-radius: 4px;
  line-height: 24px;
  font-size: var(--regular);
  color: #666;
  background-color: #f5f7fa;
  border: none;
  padding: 0.75em 0.5em;
  text-align: center;
`;

const NumberInput = ({ id, label, value, onChange }) => {
  const [staged, setStaged] = useState(value || 0);

  const onClick = e => {
    // We try to keep the focus on the Editor component at all times, which
    // causes issues for any inputs used for styling text. We need to explicitly
    // ffocus the underlying element to enable the user to type in the input.
    // This SHOULD change in the future as we clean up the focus/blur logic for
    // the editor.
    e.target.focus();
  };

  const handleInput = e => {
    const parsedNumber = Number.parseInt(e.currentTarget.value || '', 10);
    setStaged(e.currentTarget.value);
    if (!isNaN(parsedNumber)) {
      onChange(parsedNumber);
    }
  };

  return (
    <FormGroup>
      <Label htmlFor={id}>{label}</Label>
      <Input
        type="number"
        id={id}
        value={staged}
        onChange={handleInput}
        onClick={onClick}
      />
    </FormGroup>
  );
};

NumberInput.propTypes = {
  id: PropTypes.string,
  label: PropTypes.string,
  value: PropTypes.number,
  onChange: PropTypes.func,
};

export default NumberInput;
