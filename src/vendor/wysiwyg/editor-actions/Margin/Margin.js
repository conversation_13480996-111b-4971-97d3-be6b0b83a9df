import React from 'react';
import PropTypes from 'prop-types';
import { Map } from 'immutable';
import styled from 'styled-components';
import { getButtonProps } from '../../helpers/styles/editor';
import ActionsMenu from '../../components/ActionsMenu';
import MarginButton from '../../icons/MarginButton';
import { animateWhenTogglingOff } from '../../helpers/utils';
import NumberInput from './NumberInput';

// Had to add a new &'s to increase the specificity of this styled component's
// selector. It was being override by a selector from a stylesheet loaded in the
// builder.
const Heading = styled.div`
  &&& {
    text-transform: uppercase;
    margin-bottom: 16px;
    line-height: 100%;
    letter-spacing: 0.05em;
    color: #7e89a9;
    font-weight: var(--bold);
    font-size: var(--x-small);
  }
`;

const Row = styled.div`
  display: flex;
  flex-direction: row;
  justify-content: space-between;
`;

const activeButtonProps = getButtonProps(true);
const inactiveButtonProps = getButtonProps(false);

const Margin = ({
  persistedState,
  localState,
  onChange,
  onToggleActive,
  isActive,
  hasRoomToRenderBelow,
}) => {
  const buttonProps = isActive ? activeButtonProps : inactiveButtonProps;

  const toggleDropdown = e => {
    e.preventDefault();

    if (isActive) {
      animateWhenTogglingOff(() => onToggleActive(!isActive));
    } else {
      onToggleActive(!isActive);
    }
  };

  const handleInputChange = (value, name) => {
    const newPersistedState = persistedState.set(name, value);
    onChange({
      localState,
      persistedState: newPersistedState,
    });
  };

  return (
    <div>
      <MarginButton
        isMenuButton
        isActive={isActive}
        onClick={toggleDropdown}
        {...buttonProps}
      />
      {isActive && (
        <ActionsMenu
          isOpen={isActive}
          position={hasRoomToRenderBelow ? 'bottom' : 'top'}
          role="toolbar"
          aria-label="margin-settings"
        >
          <Heading>Margin</Heading>
          <Row>
            <NumberInput
              id="margin-top"
              label="Top"
              value={persistedState.get('marginTop') || 0}
              onChange={val => handleInputChange(val, 'marginTop')}
            />
            <NumberInput
              id="margin-right"
              label="Right"
              value={persistedState.get('marginRight') || 0}
              onChange={val => handleInputChange(val, 'marginRight')}
            />
            <NumberInput
              id="margin-bottom"
              label="Bottom"
              value={persistedState.get('marginBottom') || 0}
              onChange={val => handleInputChange(val, 'marginBottom')}
            />
            <NumberInput
              id="margin-left"
              label="Left"
              value={persistedState.get('marginLeft') || 0}
              onChange={val => handleInputChange(val, 'marginLeft')}
            />
          </Row>
        </ActionsMenu>
      )}
    </div>
  );
};

Margin.propTypes = {
  localState: PropTypes.instanceOf(Map),
  persistedState: PropTypes.instanceOf(Map),
  onChange: PropTypes.func,
  onToggleActive: PropTypes.func,
  isActive: PropTypes.bool,
  hasRoomToRenderBelow: PropTypes.bool,
};

export default Margin;
