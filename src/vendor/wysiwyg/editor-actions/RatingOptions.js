import React from 'react';
import PropTypes from 'prop-types';
import { Map } from 'immutable';

import {
  getButtonProps,
  secondaryMenuTitleStyle,
  menuTextStyle,
  checkboxStyle,
  inputStyle,
  labelStyle,
  InfoIcon,
  InfoOverlay,
  LabelRow,
} from '../helpers/styles/editor';
import ActionsMenu from '../components/ActionsMenu';

import SettingsButton from '../icons/SettingsButton';
import DropDownMenu from '../components/DropDownMenu';
import { animateWhenTogglingOff } from '../helpers/utils';

export default class RatingOptions extends React.Component {
  constructor(props) {
    super(props);

    const { isRequired, numOptions, customReportingLabel } =
      props.persistedState.toJS();

    this.state = {
      isRequired: isRequired || false,
      numOptions: numOptions || 11,
      isMenuOpen: false,
      isHovering: false,
      customReportingLabel: customReportingLabel || '',
    };
  }

  UNSAFE_componentWillReceiveProps(nextProps) {
    const update = {};
    const { isRequired, numOptions, customReportingLabel } =
      this.props.persistedState.toJS();
    const {
      isRequired: isRequiredNew,
      numOptions: numOptionsNew,
      customReportingLabel: customReportingLabelNew,
    } = nextProps.persistedState.toJS();

    if (isRequired !== isRequiredNew) {
      update.isRequired = isRequiredNew;
    }
    if (customReportingLabel !== customReportingLabelNew) {
      update.customReportingLabel = customReportingLabelNew;
    }
    if (numOptions !== numOptionsNew) {
      update.numOptions = numOptionsNew;
    }
    if (nextProps.isActive !== this.props.isActive) {
      update.isMenuOpen = nextProps.isActive;
    }
    if (Object.keys(update).length) {
      this.setState(update);
    }
  }

  toggleDropdown() {
    const { onToggleActive, isActive } = this.props;
    this.setState(({ isMenuOpen }) => ({
      isMenuOpen: !isMenuOpen,
    }));

    if (isActive) {
      animateWhenTogglingOff(() => onToggleActive(!isActive));
    } else {
      onToggleActive(!isActive);
    }
  }

  handleRatingLimit(numOptions) {
    this.setState(
      {
        numOptions,
      },
      this.handleSave
    );
  }

  handleIsRequired(e) {
    const isRequired = e.target.checked;
    this.setState(
      {
        isRequired,
      },
      this.handleSave
    );
  }

  handleSave() {
    const { localState, persistedState, onChange } = this.props;
    const { isRequired, numOptions, customReportingLabel } = this.state;

    const newPersistedState = persistedState
      .set('numOptions', numOptions)
      .set('isRequired', isRequired)
      .set('customReportingLabel', customReportingLabel);

    onChange({
      localState,
      persistedState: newPersistedState,
    });
  }

  handleOnChangeCustomReportingLabel(e) {
    this.setState({ customReportingLabel: e.target.value }, this.handleSave);
  }

  render() {
    const {
      isRequired,
      numOptions,
      isMenuOpen,
      isHovering,
      customReportingLabel,
    } = this.state;
    const { isActive, hasRoomToRenderBelow } = this.props;

    const buttonProps = getButtonProps(isActive);

    const titleStyles = secondaryMenuTitleStyle;

    const row = {
      marginTop: 20,
    };
    const allOptions = [...new Array(10)].map((_, i) => ({
      label: i + 1,
      value: i + 2,
    }));

    const actionsMenuProps = {
      isOpen: isMenuOpen,
      position: hasRoomToRenderBelow ? 'bottom' : 'top',
    };

    const dropdownNodes = isActive ? (
      <ActionsMenu {...actionsMenuProps}>
        <div style={titleStyles}>Rating Options</div>
        <div>
          <div style={row}>
            <input
              id="field-is-required"
              type="checkbox"
              style={checkboxStyle}
              checked={isRequired}
              onChange={e => this.handleIsRequired(e)}
            />
            <label style={menuTextStyle} htmlFor="field-is-required">
              Required Field
            </label>
          </div>
          <div style={row}>
            <DropDownMenu
              className="form-control"
              label="Rating limit"
              unsearchable
              selectedValue={numOptions}
              options={allOptions}
              onSelect={value => this.handleRatingLimit(value)}
            />
          </div>
          <LabelRow>
            <label style={labelStyle}>Custom reporting label</label>
            <InfoIcon
              icon="info-circle"
              onMouseOver={() => this.setState({ isHovering: true })}
              onMouseOut={() => this.setState({ isHovering: false })}
            />
            {isHovering && (
              <InfoOverlay>
                This label will only be used on survey reports to help you
                easily identify the form question.
              </InfoOverlay>
            )}
          </LabelRow>
          <input
            style={inputStyle}
            placeholder="My survey question"
            onChange={e => this.handleOnChangeCustomReportingLabel(e)}
            value={customReportingLabel}
          />
        </div>
      </ActionsMenu>
    ) : null;

    return (
      <div>
        <SettingsButton
          isMenuButton
          isActive={isActive}
          onClick={() => this.toggleDropdown()}
          {...buttonProps}
        />
        {dropdownNodes}
      </div>
    );
  }
}

RatingOptions.propTypes = {
  localState: PropTypes.instanceOf(Map),
  persistedState: PropTypes.instanceOf(Map),
  onChange: PropTypes.func,
  onToggleActive: PropTypes.func,
  isActive: PropTypes.bool,
  hasRoomToRenderBelow: PropTypes.bool,
};
