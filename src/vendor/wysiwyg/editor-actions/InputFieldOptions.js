import React from 'react';
import PropTypes from 'prop-types';
import { Map } from 'immutable';

import {
  getButtonProps,
  secondaryMenuTitleStyle,
  inputStyle,
  labelStyle,
  menuTextStyle,
  checkboxStyle,
  InfoIcon,
  InfoOverlay,
  LabelRow,
} from '../helpers/styles/editor';
import { INPUT_TYPES, INPUT_TYPES_LIST } from '../helpers/constants';
import ActionsMenu from '../components/ActionsMenu';
import DropDownMenu from '../components/DropDownMenu';
import SettingsButton from '../icons/SettingsButton';
import { animateWhenTogglingOff } from '../helpers/utils';

export default class InputFieldOptions extends React.Component {
  constructor(props) {
    super(props);

    this.state = {
      isRequired: props.persistedState.get('isRequired') || false,
      maxLength: props.persistedState.get('maxLength') || '',
      isMenuOpen: props.isActive || false,
      selectedInputType:
        props.persistedState.get('inputType') || INPUT_TYPES.TEXT,
      customReportingLabel:
        props.persistedState.get('customReportingLabel') || '',
      isHovering: false,
    };

    this.handleSelectInputType = this.handleSelectInputType.bind(this);
  }

  UNSAFE_componentWillReceiveProps(nextProps) {
    if (nextProps.isActive !== this.props.isActive) {
      this.setState({
        isMenuOpen: nextProps.isActive,
      });
    }
  }

  toggleDropdown() {
    const { onToggleActive, isActive } = this.props;
    this.setState(({ isMenuOpen }) => ({
      isMenuOpen: !isMenuOpen,
    }));

    if (isActive) {
      animateWhenTogglingOff(() => onToggleActive(!isActive));
    } else {
      onToggleActive(!isActive);
    }
  }

  handleIsRequired(e) {
    const isRequired = e.target.checked;
    this.setState(
      {
        isRequired,
      },
      this.handleSave
    );
  }

  handleClick(e) {
    e.target.focus();
  }

  handleSelectInputType(value) {
    this.setState(
      {
        selectedInputType: value,
      },
      this.handleSave
    );
  }

  handleMaxLength(e) {
    const maxLength = +e.target.value;
    this.setState(
      {
        maxLength,
      },
      this.handleSave
    );
  }

  handleSave() {
    const { localState, persistedState, onChange } = this.props;
    const { isRequired, maxLength, selectedInputType, customReportingLabel } =
      this.state;

    const newPersistedState = persistedState
      .set('isRequired', isRequired)
      .set('maxLength', maxLength)
      .set('inputType', selectedInputType || INPUT_TYPES.TEXT)
      .set('customReportingLabel', customReportingLabel);

    onChange({
      localState,
      persistedState: newPersistedState,
    });
  }

  handleOnChangeCustomReportingLabel(e) {
    this.setState({ customReportingLabel: e.target.value }, this.handleSave);
  }

  render() {
    const {
      isRequired,
      maxLength,
      isMenuOpen,
      selectedInputType,
      isHovering,
      customReportingLabel,
    } = this.state;
    const { isActive, hasRoomToRenderBelow } = this.props;

    const buttonProps = getButtonProps(isActive);

    const titleStyles = secondaryMenuTitleStyle;

    const row = {
      marginTop: 20,
      display: 'flex',
    };

    const actionsMenuProps = {
      isOpen: isMenuOpen,
      position: hasRoomToRenderBelow ? 'bottom' : 'top',
    };

    const dropdownNodes = isActive ? (
      <ActionsMenu {...actionsMenuProps}>
        <div style={titleStyles}>Text Field Options</div>

        <div style={row}>
          <input
            id="field-is-required"
            type="checkbox"
            style={checkboxStyle}
            checked={isRequired}
            onChange={e => this.handleIsRequired(e)}
          />
          <label style={menuTextStyle} htmlFor="field-is-required">
            Required Field
          </label>
        </div>
        <div style={{ marginTop: 20 }}>
          <DropDownMenu
            className="form-control"
            label="Type"
            unsearchable
            selectedValue={selectedInputType}
            options={INPUT_TYPES_LIST}
            onSelect={this.handleSelectInputType}
          />
        </div>
        <div style={{ ...row, flexDirection: 'column' }}>
          <label htmlFor="field-max-length" style={labelStyle}>
            Maximum Length
          </label>
          <input
            id="field-max-length"
            style={inputStyle}
            type="number"
            min="0"
            max="1000"
            step="1"
            value={maxLength}
            className="form-control"
            placeholder="None (Unlimited)"
            onChange={e => this.handleMaxLength(e)}
            onClick={e => this.handleClick(e)}
          />
        </div>
        <LabelRow>
          <label style={labelStyle}>Custom reporting label</label>
          <InfoIcon
            icon="info-circle"
            onMouseOver={() => this.setState({ isHovering: true })}
            onMouseOut={() => this.setState({ isHovering: false })}
          />
          {isHovering && (
            <InfoOverlay>
              This label will only be used on survey reports to help you easily
              identify the form question.
            </InfoOverlay>
          )}
        </LabelRow>
        <input
          style={inputStyle}
          placeholder="My survey question"
          onChange={e => this.handleOnChangeCustomReportingLabel(e)}
          value={customReportingLabel}
        />
      </ActionsMenu>
    ) : null;

    return (
      <div>
        <SettingsButton
          isMenuButton
          isActive={isActive}
          onClick={() => this.toggleDropdown()}
          {...buttonProps}
        />
        {dropdownNodes}
      </div>
    );
  }
}

InputFieldOptions.propTypes = {
  localState: PropTypes.instanceOf(Map),
  persistedState: PropTypes.instanceOf(Map),
  onChange: PropTypes.func,
  onToggleActive: PropTypes.func,
  isActive: PropTypes.bool,
  hasRoomToRenderBelow: PropTypes.bool,
};
