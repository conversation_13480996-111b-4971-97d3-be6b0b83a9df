import React from 'react';
import PropTypes from 'prop-types';
import { Map } from 'immutable';
import { ImageryGallery, useImagery } from '@appcues/imagery-kit';
import { getPortalRootLight } from 'ext/lib/document';
import { getButtonProps, ActionsItem } from '../helpers/styles/editor';
import ImageButton from '../icons/ImageButton';

const ImageGallery = ({
  localState,
  persistedState,
  onChange,
  maxWidth,
  setIsSelectorOpen,
  isHero,
}) => {
  const { setIsGalleryOpen } = useImagery();
  const $portal = getPortalRootLight();

  const handleUpload = imageDetails => {
    const { url, width } = imageDetails;
    const urlWithoutProtocol = url.replace(/^https?:\/\//i, '//');

    let newPersistedState = persistedState
      .set('url', urlWithoutProtocol)
      .set('width', width)
      .set('textAlign', 'center');

    if (isHero) {
      newPersistedState = newPersistedState.set('backgroundType', 'url');
    }

    // Make sure the uploaded image does not have a larger size than available
    if (maxWidth && width > maxWidth) {
      newPersistedState = newPersistedState.set('widthOverride', maxWidth);
    }
    const newLocalState = localState.set('isUploading', false);

    onChange({
      localState: newLocalState,
      persistedState: newPersistedState,
    });

    setIsSelectorOpen(false);
  };

  const buttonProps = getButtonProps(false);

  return (
    <>
      <ActionsItem onClick={() => setIsGalleryOpen(true)} hasBorder>
        <ImageButton
          ariaLabel="Select from gallery"
          title="Select from gallery"
          {...buttonProps}
        />
        <span>Select from gallery</span>
      </ActionsItem>
      <ImageryGallery maxSize={5} container={$portal} onSelect={handleUpload} />
    </>
  );
};

ImageGallery.propTypes = {
  localState: PropTypes.instanceOf(Map),
  persistedState: PropTypes.instanceOf(Map),
  onChange: PropTypes.func,
  maxWidth: PropTypes.number,
  setIsSelectorOpen: PropTypes.func,
};

export default ImageGallery;
