import React from 'react';
import PropTypes from 'prop-types';
import { Map, is } from 'immutable';
import sanitizeHtml from 'sanitize-html';

import { secondaryMenuTitleStyle } from '../helpers/styles/editor';
import ActionsMenu from '../components/ActionsMenu';

const MENU_HEIGHT_ALLOWANCE = 400;

export function scriptingFilter(node) {
  const isNodeAScript = node.tag.toLowerCase() === 'script';
  const eventHandlerPairs = Object.entries(node.attribs || {}).filter(([k]) =>
    k.startsWith('on')
  );
  const hasCustomEventHandlers =
    eventHandlerPairs.length &&
    !eventHandlerPairs.every(([, v]) =>
      // Matches on variations of Appcues.show() and Appcues.track()
      /^window\.(parent\.)?Appcues\.(show|track)\([\S\s]*?\);?$/.test(v)
    );
  const doesNodeHaveJavascriptContent = Object.keys(node.attribs || {}).some(
    key => node.attribs[key].startsWith('javascript:')
  );
  return (
    isNodeAScript || hasCustomEventHandlers || doesNodeHaveJavascriptContent
  );
}

export default class Code extends React.Component {
  constructor(props) {
    super(props);

    this.state = {
      hasRoomToRenderBelow: true,
      hasCheckedPosition: false,
      content: '',
      isSaved: true,
    };
  }

  componentDidMount() {
    const content = this.props.persistedState.get('content') || '';

    if (this._menuTitle) {
      this.setAceEditorPosition();
    }

    this.setState({
      content,
    });
  }

  shouldComponentUpdate(nextProps, nextState) {
    if (!this.state.hasCheckedPosition && this._html) {
      return true;
    }
    if (this.state.hasRoomToRenderBelow !== nextState.hasRoomToRenderBelow) {
      return true;
    }

    if (!is(this.props.persistedState, nextProps.persistedState)) {
      return true;
    }
    if (!is(this.props.localState, nextProps.localState)) {
      return true;
    }
    if (this.state.isSaved !== nextState.isSaved) {
      return true;
    }
    if (this.state.content !== nextState.content) {
      return true;
    }

    return false;
  }

  componentDidUpdate() {
    if (!this.state.hasCheckedPosition && this._html) {
      this.setAceEditorPosition();
    }
  }

  setAceEditorPosition() {
    const updates = { hasCheckedPosition: true };
    const hasRoomToRenderBelow =
      window.innerHeight -
        this._menuTitle.parentElement.getBoundingClientRect().top >
      MENU_HEIGHT_ALLOWANCE;
    updates.hasRoomToRenderBelow = hasRoomToRenderBelow;

    this.setState({ ...updates });
  }

  handleSave() {
    const {
      localState,
      persistedState,
      onChange,
      sanitizeHtmlConfig,
      overrideSanitizeHtmlConfig,
    } = this.props;
    const { content, isSaved } = this.state;

    const cleanHtml = sanitizeHtml(content, {
      ...(overrideSanitizeHtmlConfig || sanitizeHtmlConfig.toJS()),
      exclusiveFilter: scriptingFilter,
    }).replace(/[\t ]+\/>/g, '/>');
    // before checking that cleanHtml is equal trimmedContent, we need to
    // we remove from the original content the spaces within the style attribute,
    // as well as the last trailing semicolon. Because the sanitizeHtml function
    // will apply these changes, making the equality check fail.
    const trimmedContent = content
      .replace(/[\t ]+\/>/g, '/>')
      .replace(/style="([^"]*)"/g, (_, styleContent) => {
        const cleanedStyle = styleContent
          .replace(/\s+/g, '') // Removes all spaces
          .replace(/;$/, ''); // Removes the last trailing semicolon
        return `style="${cleanedStyle}"`;
      });
    let newLocalState;

    if (cleanHtml !== trimmedContent) {
      if (isSaved) {
        this.setState({ isSaved: false });
      }

      newLocalState = localState.set('content', content);
    } else if (!isSaved) {
      this.setState({ isSaved: true });
    }

    const newPersistedState = persistedState.set('content', cleanHtml);
    newLocalState = newLocalState || localState.set('content', cleanHtml);
    onChange({
      localState: newLocalState,
      persistedState: newPersistedState,
    });
  }

  render() {
    const { title } = this.props;
    const { isSaved, hasRoomToRenderBelow } = this.state;
    const titleStyles = secondaryMenuTitleStyle;

    const actionsMenuProps = {
      className: 'html-menu',
      isOpen: true,
      position: hasRoomToRenderBelow ? 'bottom' : 'top',
      style: {
        backgroundColor: '#272822',
        left: '50%',
        transform: 'translate3d(-50%, 0, 0)',
        width: 'auto',
      },
    };

    return (
      <ActionsMenu {...actionsMenuProps} isCode>
        <div ref={el => (this._menuTitle = el)} style={titleStyles}>
          {title}
        </div>
        <textarea
          style={{
            background: 'transparent',
            padding: '12px',
            border: '2px solid rgba(255,255,255,0.3)',
            borderRadius: '4px',
            color: 'white',
            width: '400px',
            height: '160px',
            outline: 'none',
            fontSize: 'var(--regular)',
            fontFamily: 'Courier, monospace',
          }}
          value={this.state.content}
          onChange={e => {
            this.setState({ content: e.target.value }, this.handleSave);
          }}
        />
        <div style={{ textAlign: 'right', marginTop: 10 }}>
          <span
            style={{
              color: 'rgba(255, 255, 255, 0.4)',
              fontSize: 'var(--xx-small)',
              fontStyle: 'italic',
              marginRight: 7,
            }}
          >
            {isSaved ? (
              <span>Saved</span>
            ) : (
              <span style={{ color: 'rgba(255, 69, 0, 0.8)' }}>
                Warning — HTML is probably invalid
              </span>
            )}
          </span>
        </div>
      </ActionsMenu>
    );
  }
}

Code.propTypes = {
  localState: PropTypes.instanceOf(Map),
  persistedState: PropTypes.instanceOf(Map),
  onChange: PropTypes.func,
  title: PropTypes.string,
  isActive: PropTypes.bool,
  sanitizeHtmlConfig: PropTypes.instanceOf(Map),
  shouldDisableXSS: PropTypes.bool,
  overrideSanitizeHtmlConfig: PropTypes.object,
  hasRoomToRenderBelow: PropTypes.bool,
};
