import React from 'react';

import IconButton from './IconButton';

export default class FontColorButton extends React.PureComponent {
  render() {
    return (
      <IconButton
        ariaLabel="font color"
        pathNode={
          <path
            fillRule="evenodd"
            clipRule="evenodd"
            d="M6 4C4.89543 4 4 4.89543 4 6V18C4 19.1046 4.89543 20 6 20H18C19.1046 20 20 19.1046 20 18V6C20 4.89543 19.1046 4 18 4H6ZM10.7953 8.22L8.11297 15.7865C8.07605 15.8907 8.15329 16 8.26377 16H9.52898C9.5944 16 9.65323 15.9602 9.67753 15.8994L10.4134 14.06H13.3412L14.0771 15.8994C14.1014 15.9602 14.1602 16 14.2256 16H15.4908C15.6013 16 15.6785 15.8907 15.6416 15.7865L12.9593 8.22C12.9451 8.15333 12.9062 8.1 12.8426 8.06C12.779 8.02 12.7118 8 12.6411 8H11.1135C11.0428 8 10.9756 8.02 10.912 8.06C10.8483 8.1 10.8095 8.15333 10.7953 8.22ZM10.912 12.48L11.8774 9.49332L12.8426 12.48H10.912Z"
          />
        }
        viewBox="0 0 24 24"
        {...this.props}
      />
    );
  }
}
