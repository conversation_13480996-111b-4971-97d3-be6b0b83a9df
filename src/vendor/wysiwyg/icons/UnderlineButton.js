import React from 'react';

import IconButton from './IconButton';

const UnderlineButton = props => (
  <IconButton
    ariaLabel="underline"
    pathNode={
      <path d="M12 16.125C13.4688 16.125 14.6563 15.75 15.5312 14.9375C16.4062 14.0938 16.875 12.9062 16.875 11.4062V6H17.7812C17.9063 6 18 5.96875 18.125 5.875C18.2188 5.78125 18.2813 5.65625 18.2813 5.5V4.5C18.2813 4.375 18.2188 4.25 18.125 4.15625C18 4.0625 17.9063 4 17.7812 4H13.5C13.3438 4 13.2188 4.0625 13.125 4.15625C13.0312 4.25 13 4.375 13 4.5V5.5C13 5.65625 13.0312 5.78125 13.125 5.875C13.2188 5.96875 13.3438 6 13.5 6H14.375V11.375C14.375 12.2188 14.1562 12.8438 13.75 13.25C13.3125 13.6875 12.75 13.875 12 13.875C11.25 13.875 10.6562 13.6875 10.25 13.25C9.8125 12.8438 9.625 12.2188 9.625 11.4062V6H10.5312C10.6562 6 10.75 5.96875 10.875 5.875C10.9688 5.78125 11.0312 5.65625 11.0312 5.5V4.5C11.0312 4.375 10.9688 4.25 10.875 4.15625C10.75 4.0625 10.6562 4 10.5312 4H6.21875C6.09375 4 5.96875 4.0625 5.875 4.15625C5.75 4.25 5.71875 4.375 5.71875 4.5V5.5C5.71875 5.65625 5.75 5.78125 5.875 5.875C5.96875 5.96875 6.09375 6 6.21875 6H7.125V11.4062C7.125 12.9375 7.5625 14.125 8.5 14.9375C9.34375 15.75 10.5 16.125 12 16.125ZM5.5 18C5.34375 18 5.21875 18.0625 5.125 18.1563C5.03125 18.25 5 18.375 5 18.5V19.5C5 19.6563 5.03125 19.7813 5.125 19.875C5.21875 19.9688 5.34375 20 5.5 20H18.5C18.625 20 18.75 19.9688 18.8438 19.875C18.9375 19.7813 19 19.6563 19 19.5V18.5C19 18.375 18.9375 18.25 18.8438 18.1563C18.75 18.0625 18.625 18 18.5 18H5.5Z" />
    }
    viewBox="0 0 24 24"
    {...props}
  />
);

export default UnderlineButton;
