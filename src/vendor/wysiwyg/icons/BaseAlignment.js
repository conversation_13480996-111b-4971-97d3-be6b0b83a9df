import React from 'react';
import PropTypes from 'prop-types';
import { ALIGNMENT_TYPE } from '../helpers/constants';
import ActionsMenu from '../components/ActionsMenu';

export default class BaseAlignment extends React.PureComponent {
  getPathNode(textAlign) {
    switch (textAlign) {
      case ALIGNMENT_TYPE.LEFT:
        return (
          <path d="M14.1053 5.96491C14.1053 6.12866 14.0526 6.26316 13.9474 6.36842C13.8421 6.47368 13.7076 6.52632 13.5439 6.52632H4.5614C4.39766 6.52632 4.26316 6.47368 4.15789 6.36842C4.05263 6.26316 4 6.12866 4 5.96491V4.5614C4 4.39766 4.05263 4.26316 4.15789 4.15789C4.26316 4.05263 4.39766 4 4.5614 4H13.5439C13.7076 4 13.8421 4.05263 13.9474 4.15789C14.0526 4.26316 14.1053 4.39766 14.1053 4.5614V5.96491ZM4 9.05263C4 8.88889 4.05263 8.75439 4.15789 8.64912C4.26316 8.54386 4.39766 8.49123 4.5614 8.49123H19.1579C19.3216 8.49123 19.4561 8.54386 19.5614 8.64912C19.6667 8.75439 19.7193 8.88889 19.7193 9.05263V10.4561C19.7193 10.6199 19.6667 10.7544 19.5614 10.8596C19.4561 10.9649 19.3216 11.0175 19.1579 11.0175H4.5614C4.39766 11.0175 4.26316 10.9649 4.15789 10.8596C4.05263 10.7544 4 10.6199 4 10.4561V9.05263ZM4.5614 20C4.39766 20 4.26316 19.9474 4.15789 19.8421C4.05263 19.7368 4 19.6023 4 19.4386V18.0351C4 17.8713 4.05263 17.7368 4.15789 17.6316C4.26316 17.5263 4.39766 17.4737 4.5614 17.4737H19.1579C19.3216 17.4737 19.4561 17.5263 19.5614 17.6316C19.6667 17.7368 19.7193 17.8713 19.7193 18.0351V19.4386C19.7193 19.6023 19.6667 19.7368 19.5614 19.8421C19.4561 19.9474 19.3216 20 19.1579 20H4.5614ZM13.5439 12.9825C13.7076 12.9825 13.8421 13.0351 13.9474 13.1404C14.0526 13.2456 14.1053 13.3801 14.1053 13.5439V14.9474C14.1053 15.1111 14.0526 15.2456 13.9474 15.3509C13.8421 15.4561 13.7076 15.5088 13.5439 15.5088H4.5614C4.39766 15.5088 4.26316 15.4561 4.15789 15.3509C4.05263 15.2456 4 15.1111 4 14.9474V13.5439C4 13.3801 4.05263 13.2456 4.15789 13.1404C4.26316 13.0351 4.39766 12.9825 4.5614 12.9825H13.5439Z" />
        );

      case ALIGNMENT_TYPE.CENTER:
        return (
          <path d="M16.3509 5.96491C16.3509 6.12866 16.2982 6.26316 16.193 6.36842C16.0877 6.47368 15.9532 6.52632 15.7895 6.52632H7.92982C7.76608 6.52632 7.63158 6.47368 7.52632 6.36842C7.42105 6.26316 7.36842 6.12866 7.36842 5.96491V4.5614C7.36842 4.39766 7.42105 4.26316 7.52632 4.15789C7.63158 4.05263 7.76608 4 7.92982 4H15.7895C15.9532 4 16.0877 4.05263 16.193 4.15789C16.2982 4.26316 16.3509 4.39766 16.3509 4.5614V5.96491ZM4.5614 11.0175C4.39766 11.0175 4.26316 10.9649 4.15789 10.8596C4.05263 10.7544 4 10.6199 4 10.4561V9.05263C4 8.88889 4.05263 8.75439 4.15789 8.64912C4.26316 8.54386 4.39766 8.49123 4.5614 8.49123H19.1579C19.3216 8.49123 19.4561 8.54386 19.5614 8.64912C19.6667 8.75439 19.7193 8.88889 19.7193 9.05263V10.4561C19.7193 10.6199 19.6667 10.7544 19.5614 10.8596C19.4561 10.9649 19.3216 11.0175 19.1579 11.0175H4.5614ZM4.5614 20C4.39766 20 4.26316 19.9474 4.15789 19.8421C4.05263 19.7368 4 19.6023 4 19.4386V18.0351C4 17.8713 4.05263 17.7368 4.15789 17.6316C4.26316 17.5263 4.39766 17.4737 4.5614 17.4737H19.1579C19.3216 17.4737 19.4561 17.5263 19.5614 17.6316C19.6667 17.7368 19.7193 17.8713 19.7193 18.0351V19.4386C19.7193 19.6023 19.6667 19.7368 19.5614 19.8421C19.4561 19.9474 19.3216 20 19.1579 20H4.5614ZM15.7895 12.9825C15.9532 12.9825 16.0877 13.0351 16.193 13.1404C16.2982 13.2456 16.3509 13.3801 16.3509 13.5439V14.9474C16.3509 15.1111 16.2982 15.2456 16.193 15.3509C16.0877 15.4561 15.9532 15.5088 15.7895 15.5088H7.92982C7.76608 15.5088 7.63158 15.4561 7.52632 15.3509C7.42105 15.2456 7.36842 15.1111 7.36842 14.9474V13.5439C7.36842 13.3801 7.42105 13.2456 7.52632 13.1404C7.63158 13.0351 7.76608 12.9825 7.92982 12.9825H15.7895Z" />
        );

      case ALIGNMENT_TYPE.RIGHT:
        return (
          <path d="M9.61403 4.5614C9.61403 4.39766 9.66667 4.26316 9.77193 4.15789C9.87719 4.05263 10.0117 4 10.1754 4H19.1579C19.3216 4 19.4561 4.05263 19.5614 4.15789C19.6667 4.26316 19.7193 4.39766 19.7193 4.5614V5.96491C19.7193 6.12866 19.6667 6.26316 19.5614 6.36842C19.4561 6.47368 19.3216 6.52632 19.1579 6.52632H10.1754C10.0117 6.52632 9.87719 6.47368 9.77193 6.36842C9.66667 6.26316 9.61403 6.12866 9.61403 5.96491V4.5614ZM4.5614 11.0175C4.39766 11.0175 4.26316 10.9649 4.15789 10.8596C4.05263 10.7544 4 10.6199 4 10.4561V9.05263C4 8.88889 4.05263 8.75439 4.15789 8.64912C4.26316 8.54386 4.39766 8.49123 4.5614 8.49123H19.1579C19.3216 8.49123 19.4561 8.54386 19.5614 8.64912C19.6667 8.75439 19.7193 8.88889 19.7193 9.05263V10.4561C19.7193 10.6199 19.6667 10.7544 19.5614 10.8596C19.4561 10.9649 19.3216 11.0175 19.1579 11.0175H4.5614ZM4.5614 20C4.39766 20 4.26316 19.9474 4.15789 19.8421C4.05263 19.7368 4 19.6023 4 19.4386V18.0351C4 17.8713 4.05263 17.7368 4.15789 17.6316C4.26316 17.5263 4.39766 17.4737 4.5614 17.4737H19.1579C19.3216 17.4737 19.4561 17.5263 19.5614 17.6316C19.6667 17.7368 19.7193 17.8713 19.7193 18.0351V19.4386C19.7193 19.6023 19.6667 19.7368 19.5614 19.8421C19.4561 19.9474 19.3216 20 19.1579 20H4.5614ZM10.1754 15.5088C10.0117 15.5088 9.87719 15.4561 9.77193 15.3509C9.66667 15.2456 9.61403 15.1111 9.61403 14.9474V13.5439C9.61403 13.3801 9.66667 13.2456 9.77193 13.1404C9.87719 13.0351 10.0117 12.9825 10.1754 12.9825H19.1579C19.3216 12.9825 19.4561 13.0351 19.5614 13.1404C19.6667 13.2456 19.7193 13.3801 19.7193 13.5439V14.9474C19.7193 15.1111 19.6667 15.2456 19.5614 15.3509C19.4561 15.4561 19.3216 15.5088 19.1579 15.5088H10.1754Z" />
        );

      default:
    }
  }

  render() {
    const { textAlign } = this.props;
    const label = `align ${textAlign}`;

    return (
      <ActionsMenu.IconButton
        ariaLabel={label}
        pathNode={this.getPathNode(textAlign)}
        viewBox="0 0 24 24"
        {...this.props}
      />
    );
  }
}

BaseAlignment.propTypes = {
  textAlign: PropTypes.oneOf([
    ALIGNMENT_TYPE.LEFT,
    ALIGNMENT_TYPE.CENTER,
    ALIGNMENT_TYPE.RIGHT,
  ]),
};
