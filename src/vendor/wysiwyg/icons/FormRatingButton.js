import React from 'react';

import { colors } from '../helpers/styles/editor';
import BlockButton from './BlockButton';

export default class FormRatingButton extends React.PureComponent {
  render() {
    return (
      <BlockButton
        ariaLabel="rating options"
        pathNode={
          <path
            fillRule="evenodd"
            clipRule="evenodd"
            d="M10.9941 4.61306C11.4089 3.78464 12.5912 3.78465 13.006 4.61306L14.8846 8.3649L19.0753 8.96874C20.0027 9.10237 20.3699 10.2448 19.694 10.8937L16.6728 13.7946L17.3869 17.8989C17.5464 18.8159 16.587 19.5189 15.7607 19.0905L12 17.1409L8.23934 19.0905C7.41307 19.5189 6.45367 18.8159 6.6132 17.8989L7.32728 13.7946L4.30604 10.8937C3.63018 10.2448 3.99737 9.10237 4.92477 8.96874L9.11547 8.3649L10.9941 4.61306ZM12 5.39594L10.2039 8.98299C10.039 9.31245 9.72312 9.54025 9.35843 9.5928L5.35651 10.1694L8.241 12.939C8.51094 13.1982 8.63432 13.5747 8.57018 13.9434L7.88793 17.8647L11.4822 16.0013C11.8069 15.833 12.1931 15.833 12.5178 16.0013L16.1121 17.8647L15.4299 13.9434C15.3657 13.5747 15.4891 13.1982 15.7591 12.939L18.6435 10.1694L14.6416 9.5928C14.2769 9.54025 13.9611 9.31246 13.7961 8.98299L12 5.39594Z"
            fill={colors.apc_purple}
          />
        }
        viewBox="0 0 24 24"
        {...this.props}
      />
    );
  }
}
