import React from 'react';

import IconButton from './IconButton';

export default class ItalicButton extends React.PureComponent {
  render() {
    return (
      <IconButton
        ariaLabel="italic"
        pathNode={
          <path d="M13.4708 17.7143C13.6374 17.7143 13.7803 17.7857 13.8994 17.9286C14.0184 18.0714 14.0541 18.2262 14.0065 18.3929L13.7922 19.5357C13.7684 19.6786 13.7029 19.7917 13.5958 19.875C13.4886 19.9583 13.3755 20 13.2565 20H7.57792C7.38745 20 7.23864 19.9286 7.13149 19.7857C7.02435 19.6429 6.98268 19.4881 7.00649 19.3214L7.25649 18.1786C7.2803 18.0357 7.34578 17.9226 7.45292 17.8393C7.56006 17.756 7.67316 17.7143 7.79221 17.7143H9.22078L11.4351 6.28571H10.2208C10.0303 6.28571 9.88149 6.21429 9.77435 6.07143C9.66721 5.92857 9.62554 5.77381 9.64935 5.60714L9.89935 4.46429C9.92316 4.32143 9.98268 4.20833 10.0779 4.125C10.1732 4.04167 10.2922 4 10.4351 4H16.1494C16.3398 4 16.4886 4.07143 16.5958 4.21429C16.7029 4.35714 16.7446 4.5119 16.7208 4.67857L16.4708 5.82143C16.447 5.96429 16.3815 6.07738 16.2744 6.16071C16.1672 6.24405 16.0541 6.28571 15.9351 6.28571H14.4708L12.2565 17.7143H13.4708Z" />
        }
        viewBox="0 0 24 24"
        {...this.props}
      />
    );
  }
}
