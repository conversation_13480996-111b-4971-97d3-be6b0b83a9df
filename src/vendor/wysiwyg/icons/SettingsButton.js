import React from 'react';

import IconButton from './IconButton';

export default class SettingsButton extends React.PureComponent {
  render() {
    return (
      <IconButton
        ariaLabel="settings"
        pathNode={
          <path d="M17.9062 13.0937L19.2187 13.875C19.302 13.9166 19.3593 13.9791 19.3906 14.0625C19.4218 14.1458 19.427 14.2291 19.4062 14.3125C19.052 15.4166 18.4791 16.3958 17.6875 17.25C17.625 17.3125 17.552 17.3541 17.4687 17.375C17.3854 17.3958 17.302 17.3854 17.2187 17.3437L15.9062 16.5625C15.3437 17.0416 14.7083 17.4062 14 17.6562V19.1875C14 19.2708 13.9739 19.3489 13.9218 19.4218C13.8698 19.4948 13.802 19.5416 13.7187 19.5625C12.5729 19.8125 11.4271 19.8125 10.2812 19.5625C10.1979 19.5416 10.1302 19.4948 10.0781 19.4218C10.026 19.3489 9.99997 19.2708 9.99997 19.1875V17.6562C9.29164 17.4062 8.65623 17.0416 8.09373 16.5625L6.78123 17.3437C6.6979 17.3854 6.61457 17.3958 6.53123 17.375C6.4479 17.3541 6.37498 17.3125 6.31248 17.25C5.52082 16.3958 4.9479 15.4166 4.59374 14.3125C4.5729 14.2291 4.57811 14.1458 4.60936 14.0625C4.64061 13.9791 4.6979 13.9166 4.78124 13.875L6.09373 13.0937C5.96873 12.3646 5.96873 11.6354 6.09373 10.9062L4.78124 10.125C4.6979 10.0833 4.64061 10.0208 4.60936 9.93749C4.57811 9.85415 4.5729 9.77082 4.59374 9.68749C4.9479 8.58332 5.52082 7.60416 6.31248 6.74999C6.37498 6.68749 6.4479 6.64583 6.53123 6.62499C6.61457 6.60416 6.6979 6.61458 6.78123 6.65624L8.09373 7.43749C8.65623 6.95833 9.29164 6.59374 9.99997 6.34374V4.8125C9.99997 4.72917 10.026 4.65104 10.0781 4.57812C10.1302 4.50521 10.1979 4.45833 10.2812 4.4375C11.4271 4.1875 12.5729 4.1875 13.7187 4.4375C13.802 4.45833 13.8698 4.50521 13.9218 4.57812C13.9739 4.65104 14 4.72917 14 4.8125V6.34374C14.7083 6.59374 15.3437 6.95833 15.9062 7.43749L17.2187 6.65624C17.302 6.61458 17.3854 6.60416 17.4687 6.62499C17.552 6.64583 17.625 6.68749 17.6875 6.74999C18.4791 7.60416 19.052 8.58332 19.4062 9.68749C19.427 9.77082 19.4218 9.85415 19.3906 9.93749C19.3593 10.0208 19.302 10.0833 19.2187 10.125L17.9062 10.9062C18.0312 11.6354 18.0312 12.3646 17.9062 13.0937ZM14.5 12C14.5 11.3125 14.2552 10.7239 13.7656 10.2344C13.276 9.74478 12.6875 9.49999 12 9.49999C11.3125 9.49999 10.7239 9.74478 10.2343 10.2344C9.74477 10.7239 9.49998 11.3125 9.49998 12C9.49998 12.6875 9.74477 13.276 10.2343 13.7656C10.7239 14.2552 11.3125 14.5 12 14.5C12.6875 14.5 13.276 14.2552 13.7656 13.7656C14.2552 13.276 14.5 12.6875 14.5 12Z" />
        }
        viewBox="0 0 24 24"
        {...this.props}
      />
    );
  }
}
