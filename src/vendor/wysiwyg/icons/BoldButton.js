import React from 'react';

import IconButton from './IconButton';

export default class BoldButton extends React.PureComponent {
  render() {
    return (
      <IconButton
        ariaLabel="bold"
        pathNode={
          <path d="M15.6071 11.5714C16.3929 11.8333 17.0119 12.2976 17.4643 12.9643C17.9167 13.631 18.1429 14.4286 18.1429 15.3571C18.1429 16.4048 17.8452 17.3274 17.25 18.125C16.6548 18.9226 15.8929 19.4524 14.9643 19.7143C14.3214 19.9048 13.5238 20 12.5714 20H5.57143C5.40476 20 5.26786 19.9464 5.16071 19.8393C5.05357 19.7321 5 19.5952 5 19.4286V18.2857C5 18.119 5.05357 17.9821 5.16071 17.875C5.26786 17.7679 5.40476 17.7143 5.57143 17.7143H6.75V6.32143H5.57143C5.40476 6.32143 5.26786 6.26786 5.16071 6.16071C5.05357 6.05357 5 5.91667 5 5.75V4.57143C5 4.40476 5.05357 4.26786 5.16071 4.16071C5.26786 4.05357 5.40476 4 5.57143 4H12.2143C12.8095 4 13.3095 4.02381 13.7143 4.07143C14.1905 4.14286 14.6429 4.2619 15.0714 4.42857C15.8333 4.71429 16.4286 5.19048 16.8571 5.85714C17.2857 6.52381 17.5 7.28571 17.5 8.14286C17.5 8.88095 17.3333 9.55357 17 10.1607C16.6667 10.7679 16.2024 11.2381 15.6071 11.5714ZM9.78571 6.46429V10.5H12.5357C13.131 10.5 13.5952 10.3155 13.9286 9.94643C14.2619 9.57738 14.4286 9.08333 14.4286 8.46429C14.4286 8.05952 14.3512 7.70238 14.1964 7.39286C14.0417 7.08333 13.8214 6.85714 13.5357 6.71429C13.2024 6.54762 12.7619 6.46429 12.2143 6.46429H9.78571ZM13.8214 17.3571C14.2024 17.2143 14.506 16.9405 14.7321 16.5357C14.9583 16.131 15.0714 15.6786 15.0714 15.1786C15.0714 14.4881 14.869 13.9405 14.4643 13.5357C14.0595 13.131 13.5119 12.9286 12.8214 12.9286H9.78571V17.5357H12.6786C13.1548 17.5357 13.5357 17.4762 13.8214 17.3571Z" />
        }
        viewBox="0 0 24 24"
        {...this.props}
      />
    );
  }
}
