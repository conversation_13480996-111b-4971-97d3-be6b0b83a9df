import React from 'react';

import IconButton from './IconButton';

export default class MarginButton extends React.PureComponent {
  render() {
    return (
      <IconButton
        ariaLabel="margin"
        pathNode={
          <path d="M7.55556 7.80556C7.55556 7.66748 7.66748 7.55556 7.80556 7.55556H16.1944C16.3325 7.55556 16.4444 7.66748 16.4444 7.80556V16.1944C16.4444 16.3325 16.3325 16.4444 16.1944 16.4444H7.80556C7.66748 16.4444 7.55556 16.3325 7.55556 16.1944V7.80556ZM6.20455 4.42678C6.04706 4.26929 6.1586 4 6.38133 4H17.5219C17.7402 4 17.8537 4.26015 17.7051 4.42011L16.5186 5.69789C16.4713 5.74883 16.4049 5.77778 16.3354 5.77778H7.65911C7.5928 5.77778 7.52922 5.75144 7.48233 5.70455L6.20455 4.42678ZM6.38133 20C6.1586 20 6.04706 19.7307 6.20455 19.5732L7.48233 18.2954C7.52922 18.2486 7.5928 18.2222 7.65911 18.2222H16.3354C16.4049 18.2222 16.4713 18.2512 16.5186 18.3021L17.7051 19.5799C17.8537 19.7398 17.7402 20 17.5219 20H6.38133ZM4 6.38133C4 6.1586 4.26929 6.04706 4.42678 6.20455L5.70455 7.48233C5.75144 7.52922 5.77778 7.5928 5.77778 7.65911V16.3354C5.77778 16.4049 5.74883 16.4713 5.69789 16.5186L4.42011 17.7051C4.26015 17.8537 4 17.7402 4 17.5219V6.38133ZM19.873 17.4917C19.873 17.7144 19.6037 17.826 19.4462 17.6685L18.1685 16.3907C18.1216 16.3438 18.0952 16.2802 18.0952 16.2139V7.53759C18.0952 7.46807 18.1242 7.40169 18.1751 7.35439L19.4529 6.16788C19.6129 6.01935 19.873 6.13279 19.873 6.35108V17.4917Z" />
        }
        viewBox="0 0 24 24"
        {...this.props}
      />
    );
  }
}
