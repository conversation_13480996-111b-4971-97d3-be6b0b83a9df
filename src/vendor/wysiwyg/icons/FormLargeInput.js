import React from 'react';

import { colors } from '../helpers/styles/editor';
import BlockButton from './BlockButton';

export default class FormLargeInput extends React.PureComponent {
  render() {
    return (
      <BlockButton
        ariaLabel="large input"
        pathNode={
          <path
            fillRule="evenodd"
            clipRule="evenodd"
            d="M17.8375 5.06932C17.7449 4.97685 17.5949 4.9769 17.5024 5.06943L10.9311 11.6407C10.8867 11.6852 10.8617 11.7455 10.8617 11.8083V13.9C10.8617 14.0309 10.9678 14.137 11.0987 14.137H13.1904C13.2532 14.137 13.3135 14.112 13.358 14.0676L19.9306 7.495C19.975 7.45053 20 7.39021 20 7.32732C20 7.26443 19.975 7.20412 19.9305 7.15968L17.8375 5.06932ZM4.17357 8.06714C4.2847 7.95601 4.43543 7.89357 4.59259 7.89357H9.46623C9.79351 7.89357 10.0588 8.15889 10.0588 8.48617C10.0588 8.81345 9.79351 9.07876 9.46623 9.07876H5.18518V18.7239H16.9964V13.9013C16.9964 13.574 17.2617 13.3087 17.589 13.3087C17.9162 13.3087 18.1815 13.574 18.1815 13.9013V19.3165C18.1815 19.6437 17.9162 19.9091 17.589 19.9091H4.59259C4.26531 19.9091 4 19.6437 4 19.3165V8.48617C4 8.329 4.06243 8.17827 4.17357 8.06714Z"
            fill={colors.apc_purple}
          />
        }
        viewBox="0 0 24 24"
        {...this.props}
      />
    );
  }
}
