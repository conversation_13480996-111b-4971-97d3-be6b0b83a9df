import React from 'react';

import { colors } from '../helpers/styles/editor';
import BlockButton from './BlockButton';

export default class VideoButton extends React.PureComponent {
  render() {
    return (
      <BlockButton
        ariaLabel="video"
        pathNode={
          <path
            fillRule="evenodd"
            clipRule="evenodd"
            d="M18.75 12C18.75 15.7279 15.7279 18.75 12 18.75C8.27208 18.75 5.25 15.7279 5.25 12C5.25 8.27208 8.27208 5.25 12 5.25C15.7279 5.25 18.75 8.27208 18.75 12ZM20 12C20 16.4183 16.4183 20 12 20C7.58172 20 4 16.4183 4 12C4 7.58172 7.58172 4 12 4C16.4183 4 20 7.58172 20 12ZM14.8434 12C14.8434 12.165 14.762 12.3193 14.6259 12.4126L11.4542 14.5842C11.3011 14.6891 11.1026 14.7005 10.9385 14.614C10.7744 14.5275 10.6717 14.3572 10.6717 14.1717V9.82831C10.6717 9.6428 10.7744 9.47254 10.9385 9.38602C11.1026 9.2995 11.3011 9.31095 11.4542 9.41575L14.6259 11.5874C14.762 11.6807 14.8434 11.835 14.8434 12ZM14.3434 12L11.1717 9.82831V9.82833L14.3433 12L11.1717 14.1717V14.1717L14.3434 12Z"
            fill={colors.apc_purple}
          />
        }
        viewBox="0 0 24 24"
        {...this.props}
      />
    );
  }
}
