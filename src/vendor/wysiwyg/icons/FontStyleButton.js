import React from 'react';
import PropTypes from 'prop-types';
import { TEXT_STYLE_TYPE } from '../helpers/constants';
import { colors } from '../helpers/styles/editor';
import IconButton from './IconButton';

export default class FontStyleButton extends React.PureComponent {
  render() {
    const { blockType } = this.props;
    let path = (
      <g>
        <path
          d="M24.896 10.608H26.832V22H25.296L19.152 14.016V22H17.232V10.608H18.768L24.896 18.576V10.608ZM32.8301 22.128C32.0088 22.128 31.2835 21.9573 30.6541 21.616C30.0355 21.2747 29.5555 20.7893 29.2141 20.16C28.8835 19.52 28.7181 18.7733 28.7181 17.92C28.7181 17.0667 28.8835 16.3253 29.2141 15.696C29.5555 15.056 30.0355 14.5653 30.6541 14.224C31.2835 13.8827 32.0088 13.712 32.8301 13.712C33.6408 13.712 34.3555 13.8827 34.9741 14.224C35.5928 14.5653 36.0675 15.056 36.3981 15.696C36.7395 16.3253 36.9101 17.0667 36.9101 17.92C36.9101 18.7733 36.7395 19.52 36.3981 20.16C36.0675 20.7893 35.5928 21.2747 34.9741 21.616C34.3555 21.9573 33.6408 22.128 32.8301 22.128ZM32.8141 20.56C33.4968 20.56 34.0141 20.3413 34.3661 19.904C34.7288 19.456 34.9101 18.7947 34.9101 17.92C34.9101 17.056 34.7288 16.4 34.3661 15.952C34.0035 15.4933 33.4915 15.264 32.8301 15.264C32.1581 15.264 31.6408 15.4933 31.2781 15.952C30.9155 16.4 30.7341 17.056 30.7341 17.92C30.7341 18.7947 30.9101 19.456 31.2621 19.904C31.6248 20.3413 32.1421 20.56 32.8141 20.56ZM42.9869 13.728C43.2962 13.728 43.5629 13.7707 43.7869 13.856L43.7709 15.68C43.4295 15.5413 43.0775 15.472 42.7149 15.472C42.0322 15.472 41.5095 15.6693 41.1469 16.064C40.7949 16.4587 40.6189 16.9813 40.6189 17.632V22H38.6349V16.208C38.6349 15.3547 38.5922 14.592 38.5069 13.92H40.3789L40.5389 15.344C40.7415 14.8213 41.0615 14.4213 41.4989 14.144C41.9362 13.8667 42.4322 13.728 42.9869 13.728ZM54.4365 13.712C55.3538 13.712 56.0365 13.9893 56.4845 14.544C56.9325 15.088 57.1565 15.92 57.1565 17.04V22H55.1565V17.104C55.1565 16.464 55.0498 16.0053 54.8365 15.728C54.6338 15.44 54.2925 15.296 53.8125 15.296C53.2578 15.296 52.8205 15.4933 52.5005 15.888C52.1912 16.272 52.0365 16.8053 52.0365 17.488V22H50.0365V17.104C50.0365 16.4747 49.9245 16.016 49.7005 15.728C49.4872 15.44 49.1458 15.296 48.6765 15.296C48.1218 15.296 47.6845 15.4933 47.3645 15.888C47.0445 16.272 46.8845 16.8053 46.8845 17.488V22H44.9005V16.208C44.9005 15.3547 44.8578 14.592 44.7725 13.92H46.6445L46.7885 15.2C47.0232 14.72 47.3592 14.352 47.7965 14.096C48.2445 13.84 48.7565 13.712 49.3325 13.712C50.5698 13.712 51.3752 14.224 51.7485 15.248C52.0045 14.768 52.3672 14.3947 52.8365 14.128C53.3165 13.8507 53.8498 13.712 54.4365 13.712ZM67.0191 13.92V22H65.0511V20.704C64.8058 21.152 64.4538 21.504 63.9951 21.76C63.5365 22.0053 63.0138 22.128 62.4271 22.128C61.7231 22.128 61.0991 21.9573 60.5551 21.616C60.0111 21.2747 59.5898 20.7893 59.2911 20.16C58.9925 19.5307 58.8431 18.8 58.8431 17.968C58.8431 17.136 58.9925 16.4 59.2911 15.76C59.6005 15.1093 60.0271 14.608 60.5711 14.256C61.1151 13.8933 61.7338 13.712 62.4271 13.712C63.0138 13.712 63.5365 13.84 63.9951 14.096C64.4538 14.3413 64.8058 14.688 65.0511 15.136V13.92H67.0191ZM62.9711 20.56C63.6325 20.56 64.1445 20.3307 64.5071 19.872C64.8698 19.4133 65.0511 18.768 65.0511 17.936C65.0511 17.0827 64.8698 16.4267 64.5071 15.968C64.1445 15.5093 63.6271 15.28 62.9551 15.28C62.2938 15.28 61.7765 15.52 61.4031 16C61.0405 16.4693 60.8591 17.1253 60.8591 17.968C60.8591 18.8 61.0405 19.44 61.4031 19.888C61.7765 20.336 62.2991 20.56 62.9711 20.56ZM69.1974 22V10.224H71.1814V22H69.1974Z"
          fill={colors.apc_gray_5}
        />
      </g>
    );
    let viewBox = '0 0 90 32';
    let width = 90;

    switch (blockType) {
      case TEXT_STYLE_TYPE.HEADER_ONE:
        path = (
          <g>
            <path
              d="M25.104 10.608H27.152V22H25.104V17.056H19.264V22H17.232V10.608H19.264V15.376H25.104V10.608ZM34.7266 20.32H37.2066V22H30.1826V20.32H32.6786V12.976L30.3586 14.368V12.528L33.5266 10.608H34.7266V20.32Z"
              fill={colors.apc_gray_5}
            />
          </g>
        );
        viewBox = '0 0 55 32';
        width = 55;
        break;

      case TEXT_STYLE_TYPE.HEADER_TWO:
        path = (
          <g>
            <path
              d="M25.104 10.608H27.152V22H25.104V17.056H19.264V22H17.232V10.608H19.264V15.376H25.104V10.608ZM37.1746 20.32V22H29.5266V20.48L33.3986 16.304C33.8573 15.8027 34.1933 15.344 34.4066 14.928C34.62 14.512 34.7266 14.1013 34.7266 13.696C34.7266 13.1733 34.572 12.7787 34.2626 12.512C33.964 12.2453 33.532 12.112 32.9666 12.112C31.964 12.112 30.9346 12.5067 29.8786 13.296L29.1906 11.792C29.6386 11.3867 30.2146 11.0667 30.9186 10.832C31.6226 10.5867 32.332 10.464 33.0466 10.464C34.188 10.464 35.0893 10.7467 35.7506 11.312C36.4226 11.8667 36.7586 12.6187 36.7586 13.568C36.7586 14.2187 36.6146 14.832 36.3266 15.408C36.0493 15.984 35.58 16.6293 34.9186 17.344L32.1186 20.32H37.1746Z"
              fill={colors.apc_gray_5}
            />{' '}
          </g>
        );
        viewBox = '0 0 55 32';
        width = 55;
        break;

      case TEXT_STYLE_TYPE.HEADER_THREE:
        path = (
          <g>
            <path
              d="M25.104 10.608H27.152V22H25.104V17.056H19.264V22H17.232V10.608H19.264V15.376H25.104V10.608ZM34.9506 16.144C35.6226 16.3253 36.1346 16.656 36.4866 17.136C36.8493 17.616 37.0306 18.208 37.0306 18.912C37.0306 19.904 36.6626 20.688 35.9266 21.264C35.2013 21.84 34.22 22.128 32.9826 22.128C32.2146 22.128 31.4733 22.0107 30.7586 21.776C30.0546 21.5413 29.4733 21.216 29.0146 20.8L29.7186 19.296C30.732 20.0853 31.7933 20.48 32.9026 20.48C33.628 20.48 34.1666 20.336 34.5186 20.048C34.8813 19.76 35.0626 19.3227 35.0626 18.736C35.0626 18.16 34.8813 17.7387 34.5186 17.472C34.156 17.1947 33.596 17.056 32.8386 17.056H31.3346V15.408H32.5666C34.0386 15.408 34.7746 14.848 34.7746 13.728C34.7746 13.2053 34.6146 12.8053 34.2946 12.528C33.9746 12.2507 33.5266 12.112 32.9506 12.112C31.9266 12.112 30.9026 12.5067 29.8786 13.296L29.1906 11.792C29.6493 11.3867 30.2253 11.0667 30.9186 10.832C31.6226 10.5867 32.3426 10.464 33.0786 10.464C34.1986 10.464 35.0893 10.7413 35.7506 11.296C36.4226 11.84 36.7586 12.5707 36.7586 13.488C36.748 14.1173 36.5826 14.6667 36.2626 15.136C35.9533 15.5947 35.516 15.9307 34.9506 16.144Z"
              fill={colors.apc_gray_5}
            />
          </g>
        );
        viewBox = '0 0 55 32';
        width = 55;
        break;

      case TEXT_STYLE_TYPE.HEADER_FOUR:
        path = (
          <g>
            <path
              d="M25.104 10.608H27.152V22H25.104V17.056H19.264V22H17.232V10.608H19.264V15.376H25.104V10.608ZM37.6066 18.16V19.776H35.9906V22H33.9746V19.776H29.0146V18.24L34.3426 10.608H35.9906V18.16H37.6066ZM30.9666 18.16H33.9746V13.824L30.9666 18.16Z"
              fill={colors.apc_gray_5}
            />
          </g>
        );
        viewBox = '0 0 55 32';
        width = 55;
        break;

      case TEXT_STYLE_TYPE.HEADER_FIVE:
        path = (
          <g>
            <path
              d="M25.104 10.608H27.152V22H25.104V17.056H19.264V22H17.232V10.608H19.264V15.376H25.104V10.608ZM33.7986 14.72C34.5133 14.72 35.1426 14.8747 35.6866 15.184C36.2306 15.4827 36.652 15.9093 36.9506 16.464C37.2493 17.008 37.3986 17.6373 37.3986 18.352C37.3986 19.0987 37.228 19.76 36.8866 20.336C36.5453 20.9013 36.0546 21.344 35.4146 21.664C34.7746 21.9733 34.0333 22.128 33.1906 22.128C32.476 22.128 31.772 22.0107 31.0786 21.776C30.3853 21.5307 29.8146 21.2053 29.3666 20.8L30.0546 19.296C31.0893 20.0853 32.1453 20.48 33.2226 20.48C33.9373 20.48 34.4813 20.304 34.8546 19.952C35.2386 19.5893 35.4306 19.0773 35.4306 18.416C35.4306 17.7867 35.2333 17.2853 34.8386 16.912C34.4546 16.528 33.9373 16.336 33.2866 16.336C32.8386 16.336 32.4173 16.4213 32.0226 16.592C31.628 16.752 31.2813 16.992 30.9826 17.312H29.7346V10.608H36.8706V12.208H31.7506V15.312C32.3266 14.9173 33.0093 14.72 33.7986 14.72Z"
              fill={colors.apc_gray_5}
            />
          </g>
        );
        viewBox = '0 0 55 32';
        width = 55;
        break;

      default:
        break;
    }

    return (
      <IconButton
        ariaLabel="font style"
        pathNode={path}
        viewBox={viewBox}
        iconStyle={{
          width,
          height: 32,
        }}
        textStyleButton
        {...this.props}
      />
    );
  }
}
FontStyleButton.propTypes = {
  blockType: PropTypes.string,
};
