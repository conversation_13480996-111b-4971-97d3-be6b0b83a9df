import React from 'react';

import { colors } from '../helpers/styles/editor';
import BlockButton from './BlockButton';

export default class CodeButton extends React.PureComponent {
  render() {
    return (
      <BlockButton
        ariaLabel="add html block"
        pathNode={
          <svg
            width="24"
            height="24"
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <mask id="path-1-inside-1" fill="white">
              <path
                fillRule="evenodd"
                clipRule="evenodd"
                d="M10.0423 16.8194C9.95292 17.1528 10.1508 17.4955 10.4842 17.5849C10.8176 17.6742 11.1603 17.4763 11.2497 17.1429L14.0736 6.60374C14.163 6.27032 13.9651 5.92761 13.6317 5.83827C13.2983 5.74893 12.9556 5.9468 12.8662 6.28021L10.0423 16.8194ZM7.88158 7.99022C8.12566 8.2343 8.12566 8.63003 7.88158 8.8741L4.88388 11.8718L7.88158 14.8695C8.12566 15.1136 8.12566 15.5093 7.88158 15.7534C7.6375 15.9975 7.24178 15.9975 6.9977 15.7534L3.55806 12.3137C3.31398 12.0697 3.31398 11.6739 3.55806 11.4299L6.9977 7.99022C7.24178 7.74614 7.6375 7.74614 7.88158 7.99022ZM16.1184 7.99022C15.8743 8.2343 15.8743 8.63003 16.1184 8.8741L19.1161 11.8718L16.1184 14.8695C15.8743 15.1136 15.8743 15.5093 16.1184 15.7534C16.3625 15.9975 16.7582 15.9975 17.0023 15.7534L20.4419 12.3137C20.686 12.0697 20.686 11.6739 20.4419 11.4299L17.0023 7.99022C16.7582 7.74614 16.3625 7.74614 16.1184 7.99022Z"
              />
            </mask>
            <path
              fillRule="evenodd"
              clipRule="evenodd"
              d="M10.0423 16.8194C9.95292 17.1528 10.1508 17.4955 10.4842 17.5849C10.8176 17.6742 11.1603 17.4763 11.2497 17.1429L14.0736 6.60374C14.163 6.27032 13.9651 5.92761 13.6317 5.83827C13.2983 5.74893 12.9556 5.9468 12.8662 6.28021L10.0423 16.8194ZM7.88158 7.99022C8.12566 8.2343 8.12566 8.63003 7.88158 8.8741L4.88388 11.8718L7.88158 14.8695C8.12566 15.1136 8.12566 15.5093 7.88158 15.7534C7.6375 15.9975 7.24178 15.9975 6.9977 15.7534L3.55806 12.3137C3.31398 12.0697 3.31398 11.6739 3.55806 11.4299L6.9977 7.99022C7.24178 7.74614 7.6375 7.74614 7.88158 7.99022ZM16.1184 7.99022C15.8743 8.2343 15.8743 8.63003 16.1184 8.8741L19.1161 11.8718L16.1184 14.8695C15.8743 15.1136 15.8743 15.5093 16.1184 15.7534C16.3625 15.9975 16.7582 15.9975 17.0023 15.7534L20.4419 12.3137C20.686 12.0697 20.686 11.6739 20.4419 11.4299L17.0023 7.99022C16.7582 7.74614 16.3625 7.74614 16.1184 7.99022Z"
              fill={colors.apc_purple}
            />
            <path
              d="M10.4842 17.5849L10.1607 18.7923L10.1607 18.7923L10.4842 17.5849ZM10.0423 16.8194L8.83485 16.4959L8.83485 16.4959L10.0423 16.8194ZM11.2497 17.1429L10.0423 16.8194L11.2497 17.1429ZM14.0736 6.60374L12.8662 6.28021L12.8662 6.28021L14.0736 6.60374ZM13.6317 5.83827L13.9552 4.63086L13.6317 5.83827ZM12.8662 6.28021L14.0736 6.60374V6.60374L12.8662 6.28021ZM7.88158 7.99022L8.76546 7.10634L8.76546 7.10634L7.88158 7.99022ZM4.88388 11.8718L4 10.9879C3.76558 11.2223 3.63388 11.5403 3.63388 11.8718C3.63388 12.2033 3.76558 12.5213 4 12.7557L4.88388 11.8718ZM7.88158 14.8695L8.76546 13.9856H8.76546L7.88158 14.8695ZM6.9977 15.7534L7.88158 14.8695H7.88158L6.9977 15.7534ZM3.55806 12.3137L2.67417 13.1976L3.55806 12.3137ZM3.55806 11.4299L4.44194 12.3137H4.44194L3.55806 11.4299ZM6.9977 7.99022L6.11381 7.10634L6.11381 7.10634L6.9977 7.99022ZM16.1184 8.8741L17.0023 7.99022V7.99022L16.1184 8.8741ZM16.1184 7.99022L15.2345 7.10634V7.10634L16.1184 7.99022ZM19.1161 11.8718L20 12.7557C20.4882 12.2675 20.4882 11.4761 20 10.9879L19.1161 11.8718ZM16.1184 14.8695L17.0023 15.7534L17.0023 15.7534L16.1184 14.8695ZM16.1184 15.7534L17.0023 14.8695L17.0023 14.8695L16.1184 15.7534ZM17.0023 15.7534L16.1184 14.8695L17.0023 15.7534ZM20.4419 12.3137L21.3258 13.1976L20.4419 12.3137ZM20.4419 11.4299L21.3258 10.546V10.546L20.4419 11.4299ZM17.0023 7.99022L17.8862 7.10634V7.10634L17.0023 7.99022ZM10.8077 16.3775C11.1411 16.4668 11.339 16.8095 11.2497 17.1429L8.83485 16.4959C8.56684 17.4961 9.16043 18.5242 10.1607 18.7923L10.8077 16.3775ZM10.0423 16.8194C10.1316 16.486 10.4743 16.2881 10.8077 16.3775L10.1607 18.7923C11.1609 19.0603 12.1891 18.4667 12.4571 17.4664L10.0423 16.8194ZM12.8662 6.28021L10.0423 16.8194L12.4571 17.4664L15.281 6.92726L12.8662 6.28021ZM13.3082 7.04568C12.9747 6.95634 12.7769 6.61363 12.8662 6.28021L15.281 6.92726C15.5491 5.92701 14.9555 4.89888 13.9552 4.63086L13.3082 7.04568ZM14.0736 6.60374C13.9843 6.93715 13.6416 7.13502 13.3082 7.04568L13.9552 4.63086C12.955 4.36285 11.9268 4.95644 11.6588 5.95669L14.0736 6.60374ZM11.2497 17.1429L14.0736 6.60374L11.6588 5.95669L8.83485 16.4959L11.2497 17.1429ZM8.76546 9.75799C9.4977 9.02575 9.4977 7.83857 8.76546 7.10634L6.9977 8.8741C6.75362 8.63003 6.75362 8.2343 6.9977 7.99022L8.76546 9.75799ZM5.76777 12.7557L8.76546 9.75799L6.9977 7.99022L4 10.9879L5.76777 12.7557ZM8.76546 13.9856L5.76777 10.9879L4 12.7557L6.9977 15.7534L8.76546 13.9856ZM8.76546 16.6373C9.4977 15.905 9.4977 14.7178 8.76546 13.9856L6.9977 15.7534C6.75362 15.5093 6.75362 15.1136 6.9977 14.8695L8.76546 16.6373ZM6.11381 16.6373C6.84605 17.3695 8.03323 17.3695 8.76546 16.6373L6.9977 14.8695C7.24178 14.6254 7.6375 14.6254 7.88158 14.8695L6.11381 16.6373ZM2.67417 13.1976L6.11381 16.6373L7.88158 14.8695L4.44194 11.4299L2.67417 13.1976ZM2.67418 10.546C1.94194 11.2782 1.94194 12.4654 2.67417 13.1976L4.44194 11.4299C4.68602 11.6739 4.68602 12.0697 4.44194 12.3137L2.67418 10.546ZM6.11381 7.10634L2.67417 10.546L4.44194 12.3137L7.88158 8.8741L6.11381 7.10634ZM8.76546 7.10634C8.03323 6.3741 6.84605 6.3741 6.11381 7.10634L7.88158 8.8741C7.6375 9.11818 7.24178 9.11818 6.9977 8.8741L8.76546 7.10634ZM17.0023 7.99022C17.2464 8.2343 17.2464 8.63003 17.0023 8.8741L15.2345 7.10634C14.5023 7.83857 14.5023 9.02575 15.2345 9.75799L17.0023 7.99022ZM20 10.9879L17.0023 7.99022L15.2345 9.75799L18.2322 12.7557L20 10.9879ZM17.0023 15.7534L20 12.7557L18.2322 10.9879L15.2345 13.9856L17.0023 15.7534ZM17.0023 14.8695C17.2464 15.1136 17.2464 15.5093 17.0023 15.7534L15.2345 13.9856C14.5023 14.7178 14.5023 15.905 15.2345 16.6373L17.0023 14.8695ZM16.1184 14.8695C16.3625 14.6254 16.7582 14.6254 17.0023 14.8695L15.2345 16.6373C15.9668 17.3695 17.154 17.3695 17.8862 16.6373L16.1184 14.8695ZM19.5581 11.4299L16.1184 14.8695L17.8862 16.6373L21.3258 13.1976L19.5581 11.4299ZM19.5581 12.3137C19.314 12.0697 19.314 11.6739 19.5581 11.4299L21.3258 13.1976C22.0581 12.4654 22.0581 11.2782 21.3258 10.546L19.5581 12.3137ZM16.1184 8.8741L19.5581 12.3137L21.3258 10.546L17.8862 7.10634L16.1184 8.8741ZM17.0023 8.8741C16.7582 9.11818 16.3625 9.11818 16.1184 8.8741L17.8862 7.10634C17.154 6.3741 15.9668 6.3741 15.2345 7.10634L17.0023 8.8741Z"
              fill={colors.apc_purple}
              mask="url(#path-1-inside-1)"
            />
          </svg>
        }
        viewBox="0 0 24 24"
        {...this.props}
      />
    );
  }
}
