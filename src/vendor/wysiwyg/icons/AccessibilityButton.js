import React from 'react';

import IconButton from './IconButton';

export default class AccessibilityButton extends React.PureComponent {
  render() {
    return (
      <IconButton
        ariaLabel="accessibility"
        title="accessibility"
        pathNode={
          <path d="M12 5.29032C10.7742 5.29032 9.65054 5.5914 8.62903 6.19355C7.60753 6.7957 6.7957 7.60753 6.19355 8.62903C5.5914 9.65054 5.29032 10.7742 5.29032 12C5.29032 13.2258 5.5914 14.3495 6.19355 15.371C6.7957 16.3925 7.60753 17.2043 8.62903 17.8065C9.65054 18.4086 10.7742 18.7097 12 18.7097C13.2258 18.7097 14.3495 18.4086 15.371 17.8065C16.3925 17.2043 17.2043 16.3925 17.8065 15.371C18.4086 14.3495 18.7097 13.2258 18.7097 12C18.7097 10.7742 18.4086 9.65054 17.8065 8.62903C17.2043 7.60753 16.3925 6.7957 15.371 6.19355C14.3495 5.5914 13.2258 5.29032 12 5.29032ZM12 4C13.4409 4 14.7742 4.36022 16 5.08065C17.2258 5.80107 18.1989 6.77419 18.9194 8C19.6398 9.22581 20 10.5591 20 12C20 13.4409 19.6398 14.7742 18.9194 16C18.1989 17.2258 17.2258 18.1989 16 18.9194C14.7742 19.6398 13.4409 20 12 20C10.5591 20 9.22581 19.6398 8 18.9194C6.77419 18.1989 5.80107 17.2258 5.08065 16C4.36022 14.7742 4 13.4409 4 12C4 10.5591 4.36022 9.22581 5.08065 8C5.80107 6.77419 6.77419 5.80107 8 5.08065C9.22581 4.36022 10.5591 4 12 4ZM12 5.80645C13.1183 5.80645 14.1505 6.08602 15.0968 6.64516C16.043 7.2043 16.7957 7.95699 17.3548 8.90323C17.914 9.84946 18.1935 10.8817 18.1935 12C18.1935 13.1183 17.914 14.1505 17.3548 15.0968C16.7957 16.043 16.043 16.7957 15.0968 17.3548C14.1505 17.914 13.1183 18.1935 12 18.1935C10.8817 18.1935 9.84946 17.914 8.90323 17.3548C7.95699 16.7957 7.2043 16.043 6.64516 15.0968C6.08602 14.1505 5.80645 13.1183 5.80645 12C5.80645 10.8817 6.08602 9.84946 6.64516 8.90323C7.2043 7.95699 7.95699 7.2043 8.90323 6.64516C9.84946 6.08602 10.8817 5.80645 12 5.80645ZM12 7.22581C11.6774 7.22581 11.4032 7.33871 11.1774 7.56452C10.9516 7.79032 10.8387 8.06452 10.8387 8.3871C10.8387 8.70968 10.9516 8.98387 11.1774 9.20968C11.4032 9.43548 11.6774 9.54839 12 9.54839C12.3226 9.54839 12.5968 9.43548 12.8226 9.20968C13.0484 8.98387 13.1613 8.70968 13.1613 8.3871C13.1613 8.06452 13.0484 7.79032 12.8226 7.56452C12.5968 7.33871 12.3226 7.22581 12 7.22581ZM15.8065 10.3871C15.9355 10.3656 16.0376 10.2903 16.1129 10.1613C16.1882 10.0323 16.2097 9.89785 16.1774 9.75806C16.1452 9.61828 16.0699 9.51075 15.9516 9.43548C15.8333 9.36022 15.6989 9.34409 15.5484 9.3871C14.4731 9.64516 13.6344 9.80645 13.0323 9.87097C12.3441 9.95699 11.6559 9.95699 10.9677 9.87097C10.3656 9.80645 9.52688 9.64516 8.45161 9.3871C8.30107 9.34409 8.16667 9.36022 8.04839 9.43548C7.93011 9.51075 7.85484 9.61828 7.82258 9.75806C7.79032 9.89785 7.81183 10.0269 7.8871 10.1452C7.96237 10.2634 8.06452 10.3441 8.19355 10.3871C9.33333 10.6452 10.2151 10.8172 10.8387 10.9032C10.8387 12.2581 10.7634 13.3226 10.6129 14.0968C10.5269 14.5699 10.3656 15.1075 10.129 15.7097L10.0323 15.9032C9.98925 16.0538 10 16.2043 10.0645 16.3548C10.129 16.5054 10.2366 16.6075 10.3871 16.6613C10.5376 16.7151 10.6828 16.7097 10.8226 16.6452C10.9624 16.5806 11.0645 16.4839 11.129 16.3548L11.2258 16.129C11.3763 15.7204 11.4946 15.3871 11.5806 15.129C11.6882 14.7419 11.7742 14.3011 11.8387 13.8065H12.1613C12.2258 14.3011 12.3118 14.7419 12.4194 15.129C12.5054 15.3871 12.6237 15.7204 12.7742 16.129L12.871 16.3548C12.9355 16.4839 13.0376 16.5806 13.1774 16.6452C13.3172 16.7097 13.4624 16.7151 13.6129 16.6613C13.7634 16.6075 13.871 16.5054 13.9355 16.3548C14 16.2043 14.0108 16.0538 13.9677 15.9032L13.871 15.7097C13.6344 15.1075 13.4731 14.5699 13.3871 14.0968C13.2366 13.3226 13.1613 12.2581 13.1613 10.9032C13.7849 10.8172 14.6667 10.6452 15.8065 10.3871Z" />
        }
        viewBox="0 0 24 24"
        {...this.props}
      />
    );
  }
}
