import React from 'react';

import IconButton from './IconButton';

export default class SelectSizeButton extends React.PureComponent {
  render() {
    return (
      <IconButton
        ariaLabel="set dimensions"
        pathNode={
          <path
            fillRule="evenodd"
            clipRule="evenodd"
            d="M14.4199 4.11008C14.3483 4.18346 14.2768 4.29354 14.2768 4.44031V5.90803C14.2768 6.01811 14.3483 6.12819 14.4199 6.20157C14.4914 6.27496 14.5987 6.34835 14.706 6.34835L15.8909 6.34835L12 10.3397L13.6186 12L17.7107 7.8026V9.43055C17.7107 9.54063 17.7823 9.65071 17.8538 9.72409C17.9253 9.79748 18.0326 9.87087 18.14 9.87087H19.5708C19.7138 9.87087 19.8211 9.79748 19.8927 9.72409C19.9642 9.65071 20 9.54063 20 9.43055V4.88063C20 4.62378 19.9285 4.40362 19.7496 4.25685C19.6065 4.07339 19.3919 4 19.1415 4H14.706C14.5987 4 14.4914 4.03669 14.4199 4.11008ZM9.58013 19.8899C9.65167 19.8165 9.72321 19.7065 9.72321 19.5597V18.092C9.72321 17.9819 9.65167 17.8718 9.58013 17.7984C9.50859 17.725 9.40128 17.6517 9.29397 17.6517H8.10906L12 13.6603L10.3814 12L6.28929 16.1974V14.5694C6.28929 14.4594 6.21775 14.3493 6.14621 14.2759C6.07467 14.2025 5.96736 14.1291 5.86004 14.1291H4.42924C4.28616 14.1291 4.17885 14.2025 4.10731 14.2759C4.03577 14.3493 4 14.4594 4 14.5694V19.1194C4 19.3762 4.07154 19.5964 4.25039 19.7431C4.39347 19.9266 4.60809 20 4.85848 20H9.29397C9.40128 20 9.50859 19.9633 9.58013 19.8899Z"
          />
        }
        viewBox="0 0 24 24"
        {...this.props}
      />
    );
  }
}
