import React from 'react';

import IconButton from './IconButton';

export default class EmojiButton extends React.PureComponent {
  render() {
    return (
      <IconButton
        ariaLabel="emoji"
        pathNode={
          <path d="M12 4C7.58065 4 4 7.58065 4 12C4 16.4194 7.58065 20 12 20C16.4194 20 20 16.4194 20 12C20 7.58065 16.4194 4 12 4ZM14.5806 9.41935C15.1516 9.41935 15.6129 9.88064 15.6129 10.4516C15.6129 11.0226 15.1516 11.4839 14.5806 11.4839C14.0097 11.4839 13.5484 11.0226 13.5484 10.4516C13.5484 9.88064 14.0097 9.41935 14.5806 9.41935ZM9.41935 9.41935C9.99032 9.41935 10.4516 9.88064 10.4516 10.4516C10.4516 11.0226 9.99032 11.4839 9.41935 11.4839C8.84839 11.4839 8.3871 11.0226 8.3871 10.4516C8.3871 9.88064 8.84839 9.41935 9.41935 9.41935ZM12 17.6774C10.0452 17.6774 7.66129 16.4419 7.36129 14.6677C7.29677 14.2871 7.66129 13.971 8.02903 14.0903C9.00323 14.4032 10.4516 14.5806 12 14.5806C13.5484 14.5806 14.9968 14.4032 15.971 14.0903C16.3355 13.971 16.7 14.2871 16.6387 14.6677C16.3387 16.4419 13.9548 17.6774 12 17.6774Z" />
        }
        viewBox="0 0 24 24"
        {...this.props}
      />
    );
  }
}
