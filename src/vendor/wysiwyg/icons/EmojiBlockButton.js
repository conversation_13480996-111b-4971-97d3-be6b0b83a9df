import React from 'react';

import { colors } from '../helpers/styles/editor';
import BlockButton from './BlockButton';

export default class EmojiBlockButton extends React.PureComponent {
  render() {
    return (
      <BlockButton
        ariaLabel="add emoji"
        pathNode={
          <path
            fillRule="evenodd"
            clipRule="evenodd"
            d="M18.75 12C18.75 15.7279 15.7279 18.75 12 18.75C8.27208 18.75 5.25 15.7279 5.25 12C5.25 8.27208 8.27208 5.25 12 5.25C15.7279 5.25 18.75 8.27208 18.75 12ZM12 20C16.4183 20 20 16.4183 20 12C20 7.58172 16.4183 4 12 4C7.58172 4 4 7.58172 4 12C4 16.4183 7.58172 20 12 20ZM8.95835 12.8332C9.23369 12.6267 9.62395 12.6818 9.83153 12.9558L9.83502 12.9603C9.83922 12.9656 9.84701 12.9753 9.85831 12.9887C9.88094 13.0157 9.91737 13.0575 9.96688 13.1091C10.0664 13.213 10.2158 13.3537 10.4093 13.4944C10.7986 13.7776 11.3389 14.0416 12 14.0416C12.6611 14.0416 13.2014 13.7776 13.5907 13.4944C13.7842 13.3537 13.9336 13.213 14.0331 13.1091C14.0827 13.0575 14.1191 13.0157 14.1417 12.9887C14.153 12.9753 14.1608 12.9656 14.165 12.9603L14.1685 12.9558C14.3761 12.6818 14.7663 12.6267 15.0417 12.8332C15.3178 13.0403 15.3738 13.4321 15.1667 13.7082L14.6667 13.3332C15.1667 13.7082 15.1662 13.7089 15.166 13.7091L15.1653 13.71L15.1636 13.7123L15.1589 13.7184L15.1451 13.7361C15.1338 13.7504 15.1185 13.7693 15.0992 13.7923C15.0606 13.8383 15.0059 13.9007 14.9356 13.974C14.7956 14.1201 14.5908 14.3127 14.326 14.5054C13.7986 14.8889 13.0056 15.2916 12 15.2916C10.9945 15.2916 10.2014 14.8889 9.67408 14.5054C9.40919 14.3127 9.20443 14.1201 9.0644 13.974C8.99412 13.9007 8.9394 13.8383 8.90084 13.7923C8.88154 13.7693 8.86622 13.7504 8.85496 13.7361L8.8411 13.7184L8.83646 13.7123L8.83471 13.71L8.83398 13.7091L8.83335 13.7082L9.33335 13.3332C8.83335 13.7082 8.8332 13.708 8.83335 13.7082C8.62624 13.4321 8.68221 13.0403 8.95835 12.8332ZM14.75 10C14.75 10.4142 14.4142 10.75 14 10.75C13.5858 10.75 13.25 10.4142 13.25 10C13.25 9.58579 13.5858 9.25 14 9.25C14.4142 9.25 14.75 9.58579 14.75 10ZM10 10.75C10.4142 10.75 10.75 10.4142 10.75 10C10.75 9.58579 10.4142 9.25 10 9.25C9.58579 9.25 9.25 9.58579 9.25 10C9.25 10.4142 9.58579 10.75 10 10.75Z"
            fill={colors.apc_purple}
          />
        }
        viewBox="0 0 24 24"
        {...this.props}
      />
    );
  }
}
