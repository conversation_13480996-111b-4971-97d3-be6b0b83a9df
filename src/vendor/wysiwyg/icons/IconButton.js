import React from 'react';
import PropTypes from 'prop-types';
import styled from 'styled-components';
import tinyColor from 'tinycolor2';
import { colors } from '../helpers/styles/editor';

const Wrapper = styled.span`
  align-items: center;
  display: flex;
  padding: 8px 4px;
  z-index: 10;

  svg,
  svg path {
    color: inherit;
  }
`;

const MenuIndicator = () => (
  <span
    style={{
      borderBottom: '4px solid #7e89a9',
      borderLeft: '4px solid transparent',
      bottom: '4px',
      height: 0,
      position: 'absolute',
      right: '4px',
      width: 0,
    }}
  />
);

export default class IconButton extends React.Component {
  constructor(props) {
    super(props);

    this.state = {
      colorOverride: null,
      hoverColor:
        props.hoverColor || tinyColor(props.color).lighten(5).toHexString(),
      isHovering: false,
    };
  }

  handleClick(e) {
    e.preventDefault();
    const { onClick, disabled } = this.props;
    if (disabled) return;
    if (onClick) {
      onClick(e);
    }
  }

  handleMouseOver() {
    const { onMouseOver } = this.props;
    const { hoverColor } = this.state;

    if (hoverColor) {
      this.setState({
        colorOverride: hoverColor,
        isHovering: true,
      });
    }
    if (onMouseOver) {
      onMouseOver();
    }
  }

  handleMouseOut() {
    const { onMouseOut } = this.props;
    const { hoverColor } = this.state;

    if (hoverColor) {
      this.setState({
        colorOverride: null,
        isHovering: false,
      });
    }
    if (onMouseOut) {
      onMouseOut();
    }

    /*
    This was necessary at one point.
    Leaving it commented as a reminder
    if (onMouseUp) {
      onMouseUp();
    }
    */
  }

  handleMouseDown(e) {
    e.preventDefault();
    const { clickColor, onMouseDown, disabled } = this.props;

    if (disabled) return;

    if (clickColor) {
      this.setState({
        colorOverride: clickColor,
      });
    }
    if (onMouseDown) {
      onMouseDown(e);
    }
  }

  handleMouseUp() {
    const { clickColor, onMouseUp, disabled } = this.props;

    if (disabled) return;

    if (clickColor) {
      this.setState({
        colorOverride: null,
      });
    }
    if (onMouseUp) {
      onMouseUp();
    }
  }

  render() {
    const {
      ariaLabel,
      title,
      disabled,
      pathNode,
      iconStyle,
      viewBox,
      color,
      hideBackground,
      onClick,
      onMouseDown,
      onMouseUp,
      isActive,
      activeColor,
      cursor,
      svg, // eslint-disable-line no-unused-vars
      style,
      secondary,
      smallButton,
      textStyleButton,
      buttonTypeButton,
      hoverColor,
      isAddButton,
      isMenuButton,
      text,
    } = this.props;

    const { colorOverride, isHovering } = this.state;

    let finalColor = color;
    if (isActive && activeColor) {
      finalColor = activeColor;
    } else if (colorOverride) {
      finalColor = colorOverride;
    }

    const baseIconStyle = {
      display: 'inline-block',
      width: 24,
      height: 24,
      strokeWidth: 0,
      stroke: 'currentColor',
      fill: 'currentColor',
      transition: 'background-color 0.15s ease-in',
    };

    const iconWrapperStyle = {
      position: 'relative',
      textAlign: 'center',
      backgroundColor: 'transparent',
      color: 'rgba(255, 255, 255, 0.8)',
      borderRadius: isAddButton ? '50%' : 6,
      height: isAddButton ? 36 : 32,
      width: isAddButton ? 36 : 32,
      lineHeight: 0,
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      flexShrink: '0',
      transition: 'background-color 0.3s ease-out',
    };

    if (finalColor) {
      iconWrapperStyle.backgroundColor = finalColor;
    }

    if (isActive || (isHovering && hoverColor)) {
      iconWrapperStyle.backgroundColor = isActive
        ? colors.apc_purple_20_percent
        : colors.apc_purple_10_percent;
    }

    if (secondary) {
      iconWrapperStyle.border = `2px solid ${finalColor}`;
      iconWrapperStyle.color = finalColor;
      delete iconWrapperStyle.backgroundColor;
    }

    if (hideBackground) {
      if (!isHovering && !isActive) {
        delete iconWrapperStyle.backgroundColor;
      }
      delete iconWrapperStyle.boxShadow;
      if (finalColor) {
        iconWrapperStyle.color = finalColor;
      }
    }

    if (smallButton) {
      iconWrapperStyle.height = 24;
      iconWrapperStyle.width = 24;
      baseIconStyle.height = 12;
      baseIconStyle.width = 12;
    }

    if (textStyleButton || buttonTypeButton) {
      iconWrapperStyle.width = iconStyle.width;
    }

    const textWrapperStyle = {
      ...iconWrapperStyle,
      width: 'max-content',
      fontWeight: 'var(--bold)',
      fontSize: 'var(--regular)',
      padding: '5px 9px',
      color: isHovering || isActive ? 'var(--secondary)' : 'var(--background)',
    };

    const finalIconStyle = { ...baseIconStyle, ...iconStyle };

    const iconNodes = (
      <Wrapper className={this.props.className}>
        <span style={text ? textWrapperStyle : iconWrapperStyle}>
          {!text && (
            <svg
              style={finalIconStyle}
              viewBox={viewBox}
              aria-label={ariaLabel}
            >
              {title && <title>{title}</title>}
              {pathNode}
            </svg>
          )}
          {text}
          {isMenuButton && <MenuIndicator />}
        </span>
      </Wrapper>
    );

    const linkStyle = {
      textDecoration: 'none',
      ...style,
    };
    if (cursor) linkStyle.cursor = cursor;

    if (disabled) {
      linkStyle.cursor = 'disabled';
      linkStyle.opacity = 0.5;
    }

    return onClick || onMouseDown || onMouseUp ? (
      <a
        href="#"
        style={linkStyle}
        onClick={e => this.handleClick(e)}
        onMouseDown={e => this.handleMouseDown(e)}
        onMouseUp={() => this.handleMouseUp()}
        onMouseOver={() => this.handleMouseOver()}
        onMouseOut={() => this.handleMouseOut()}
        className={this.props.className}
      >
        {iconNodes}
      </a>
    ) : (
      iconNodes
    );
  }
}

IconButton.defaultProps = {
  style: {},
  textStyle: {},
  secondary: false,
};

IconButton.propTypes = {
  ariaLabel: PropTypes.string,
  title: PropTypes.string,
  pathNode: PropTypes.node,
  viewBox: PropTypes.string,
  disabled: PropTypes.bool,
  iconStyle: PropTypes.object,
  color: PropTypes.string,
  cursor: PropTypes.string,
  clickColor: PropTypes.string,
  activeColor: PropTypes.string,
  hideBackground: PropTypes.bool,
  onClick: PropTypes.func,
  onMouseDown: PropTypes.func,
  onMouseUp: PropTypes.func,
  onMouseOver: PropTypes.func,
  onMouseOut: PropTypes.func,
  isActive: PropTypes.bool,
  svg: PropTypes.element,
  style: PropTypes.object,
  secondary: PropTypes.bool,
  smallButton: PropTypes.bool,
  textStyleButton: PropTypes.bool,
  isAddButton: PropTypes.bool,
  hoverColor: PropTypes.string,
  buttonTypeButton: PropTypes.bool,
  isMenuButton: PropTypes.bool,
  text: PropTypes.string,
  className: PropTypes.string,
};
