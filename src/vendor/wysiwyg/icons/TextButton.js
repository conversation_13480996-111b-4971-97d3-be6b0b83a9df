import React from 'react';

import { colors } from '../helpers/styles/editor';
import BlockButton from './BlockButton';

export default class TextButton extends React.PureComponent {
  render() {
    return (
      <BlockButton
        ariaLabel="text"
        pathNode={
          <path
            fillRule="evenodd"
            clipRule="evenodd"
            d="M6.56604 6C6.25342 6 6 6.25342 6 6.56604C6 6.87865 6.25342 7.13208 6.56604 7.13208H11.434L11.434 17.434C11.434 17.7466 11.6874 18 12 18C12.3126 18 12.566 17.7466 12.566 17.434V7.13208H17.434C17.7466 7.13208 18 6.87865 18 6.56604C18 6.25342 17.7466 6 17.434 6H12H6.56604Z"
            fill={colors.apc_purple}
          />
        }
        viewBox="0 0 24 24"
        {...this.props}
      />
    );
  }
}
