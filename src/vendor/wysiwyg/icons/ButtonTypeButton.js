import React from 'react';
import PropTypes from 'prop-types';
import { BUTTON_TYPES } from '../helpers/constants';
import { colors, getButtonProps } from '../helpers/styles/editor';
import IconButton from './IconButton';

const DefaultButtonTypeButton = ({ buttonProps, isActive, onClick }) => {
  return (
    <IconButton
      isMenuButton
      pathNode={
        <g>
          <path
            d="M17.078 12.032H21.404C22.4773 12.032 23.3127 12.2933 23.91 12.816C24.5073 13.3293 24.806 14.034 24.806 14.93C24.806 15.826 24.5027 16.5353 23.896 17.058C23.2987 17.5807 22.468 17.842 21.404 17.842H18.884V22H17.078V12.032ZM21.194 16.442C21.8193 16.442 22.2907 16.316 22.608 16.064C22.9347 15.8027 23.098 15.4293 23.098 14.944C23.098 14.4493 22.9393 14.076 22.622 13.824C22.3047 13.5627 21.8287 13.432 21.194 13.432H18.884V16.442H21.194ZM29.9475 14.762C30.2182 14.762 30.4515 14.7993 30.6475 14.874L30.6335 16.47C30.3348 16.3487 30.0268 16.288 29.7095 16.288C29.1122 16.288 28.6548 16.4607 28.3375 16.806C28.0295 17.1513 27.8755 17.6087 27.8755 18.178V22H26.1395V16.932C26.1395 16.1853 26.1022 15.518 26.0275 14.93H27.6655L27.8055 16.176C27.9828 15.7187 28.2628 15.3687 28.6455 15.126C29.0282 14.8833 29.4622 14.762 29.9475 14.762ZM31.6219 22V14.93H33.3579V22H31.6219ZM31.5099 11.724H33.4559V13.446H31.5099V11.724ZM43.6163 14.748C44.419 14.748 45.0163 14.9907 45.4083 15.476C45.8003 15.952 45.9963 16.68 45.9963 17.66V22H44.2463V17.716C44.2463 17.156 44.153 16.7547 43.9663 16.512C43.789 16.26 43.4903 16.134 43.0703 16.134C42.585 16.134 42.2023 16.3067 41.9223 16.652C41.6516 16.988 41.5163 17.4547 41.5163 18.052V22H39.7663V17.716C39.7663 17.1653 39.6683 16.764 39.4723 16.512C39.2856 16.26 38.987 16.134 38.5763 16.134C38.091 16.134 37.7083 16.3067 37.4283 16.652C37.1483 16.988 37.0083 17.4547 37.0083 18.052V22H35.2723V16.932C35.2723 16.1853 35.235 15.518 35.1603 14.93H36.7983L36.9243 16.05C37.1296 15.63 37.4236 15.308 37.8063 15.084C38.1983 14.86 38.6463 14.748 39.1503 14.748C40.233 14.748 40.9376 15.196 41.2643 16.092C41.4883 15.672 41.8056 15.3453 42.2163 15.112C42.6363 14.8693 43.103 14.748 43.6163 14.748ZM54.6261 14.93V22H52.9041V20.866C52.6894 21.258 52.3814 21.566 51.9801 21.79C51.5788 22.0047 51.1214 22.112 50.6081 22.112C49.9921 22.112 49.4461 21.9627 48.9701 21.664C48.4941 21.3653 48.1254 20.9407 47.8641 20.39C47.6028 19.8393 47.4721 19.2 47.4721 18.472C47.4721 17.744 47.6028 17.1 47.8641 16.54C48.1348 15.9707 48.5081 15.532 48.9841 15.224C49.4601 14.9067 50.0014 14.748 50.6081 14.748C51.1214 14.748 51.5788 14.86 51.9801 15.084C52.3814 15.2987 52.6894 15.602 52.9041 15.994V14.93H54.6261ZM51.0841 20.74C51.6628 20.74 52.1108 20.5393 52.4281 20.138C52.7454 19.7367 52.9041 19.172 52.9041 18.444C52.9041 17.6973 52.7454 17.1233 52.4281 16.722C52.1108 16.3207 51.6581 16.12 51.0701 16.12C50.4914 16.12 50.0388 16.33 49.7121 16.75C49.3948 17.1607 49.2361 17.7347 49.2361 18.472C49.2361 19.2 49.3948 19.76 49.7121 20.152C50.0388 20.544 50.4961 20.74 51.0841 20.74ZM60.3401 14.762C60.6107 14.762 60.8441 14.7993 61.0401 14.874L61.0261 16.47C60.7274 16.3487 60.4194 16.288 60.1021 16.288C59.5047 16.288 59.0474 16.4607 58.7301 16.806C58.4221 17.1513 58.2681 17.6087 58.2681 18.178V22H56.5321V16.932C56.5321 16.1853 56.4947 15.518 56.4201 14.93H58.0581L58.1981 16.176C58.3754 15.7187 58.6554 15.3687 59.0381 15.126C59.4207 14.8833 59.8547 14.762 60.3401 14.762ZM69.1303 14.93L65.8403 22.476C65.467 23.3533 64.991 23.9927 64.4123 24.394C63.8337 24.7953 63.1197 25.0613 62.2703 25.192L61.8783 23.848C62.541 23.6987 63.0357 23.5073 63.3623 23.274C63.6983 23.05 63.969 22.714 64.1743 22.266L64.3983 21.748L61.4583 14.93H63.2923L65.3223 20.012L67.3943 14.93H69.1303Z"
            fill={colors.apc_gray_5}
          />
        </g>
      }
      viewBox="0 0 80 32"
      iconStyle={{
        width: 80,
        height: 32,
      }}
      buttonTypeButton
      hideBackground={buttonProps.hideBackground}
      color={buttonProps.color}
      clickColor={buttonProps.clickColor}
      activeColor={buttonProps.activeColor}
      hoverColor={buttonProps.hoverColor}
      isActive={isActive}
      onClick={onClick}
    />
  );
};
DefaultButtonTypeButton.propTypes = {
  buttonProps: PropTypes.object,
  isActive: PropTypes.bool,
  onClick: PropTypes.func,
};

const PrimaryButtonTypeButton = ({ buttonProps, isActive, onClick }) => {
  return (
    <IconButton
      isMenuButton
      pathNode={
        <g>
          <path
            d="M17.078 12.032H21.404C22.4773 12.032 23.3127 12.2933 23.91 12.816C24.5073 13.3293 24.806 14.034 24.806 14.93C24.806 15.826 24.5027 16.5353 23.896 17.058C23.2987 17.5807 22.468 17.842 21.404 17.842H18.884V22H17.078V12.032ZM21.194 16.442C21.8193 16.442 22.2907 16.316 22.608 16.064C22.9347 15.8027 23.098 15.4293 23.098 14.944C23.098 14.4493 22.9393 14.076 22.622 13.824C22.3047 13.5627 21.8287 13.432 21.194 13.432H18.884V16.442H21.194ZM29.9475 14.762C30.2182 14.762 30.4515 14.7993 30.6475 14.874L30.6335 16.47C30.3348 16.3487 30.0268 16.288 29.7095 16.288C29.1122 16.288 28.6548 16.4607 28.3375 16.806C28.0295 17.1513 27.8755 17.6087 27.8755 18.178V22H26.1395V16.932C26.1395 16.1853 26.1022 15.518 26.0275 14.93H27.6655L27.8055 16.176C27.9828 15.7187 28.2628 15.3687 28.6455 15.126C29.0282 14.8833 29.4622 14.762 29.9475 14.762ZM31.6219 22V14.93H33.3579V22H31.6219ZM31.5099 11.724H33.4559V13.446H31.5099V11.724ZM43.6163 14.748C44.419 14.748 45.0163 14.9907 45.4083 15.476C45.8003 15.952 45.9963 16.68 45.9963 17.66V22H44.2463V17.716C44.2463 17.156 44.153 16.7547 43.9663 16.512C43.789 16.26 43.4903 16.134 43.0703 16.134C42.585 16.134 42.2023 16.3067 41.9223 16.652C41.6516 16.988 41.5163 17.4547 41.5163 18.052V22H39.7663V17.716C39.7663 17.1653 39.6683 16.764 39.4723 16.512C39.2856 16.26 38.987 16.134 38.5763 16.134C38.091 16.134 37.7083 16.3067 37.4283 16.652C37.1483 16.988 37.0083 17.4547 37.0083 18.052V22H35.2723V16.932C35.2723 16.1853 35.235 15.518 35.1603 14.93H36.7983L36.9243 16.05C37.1296 15.63 37.4236 15.308 37.8063 15.084C38.1983 14.86 38.6463 14.748 39.1503 14.748C40.233 14.748 40.9376 15.196 41.2643 16.092C41.4883 15.672 41.8056 15.3453 42.2163 15.112C42.6363 14.8693 43.103 14.748 43.6163 14.748ZM54.6261 14.93V22H52.9041V20.866C52.6894 21.258 52.3814 21.566 51.9801 21.79C51.5788 22.0047 51.1214 22.112 50.6081 22.112C49.9921 22.112 49.4461 21.9627 48.9701 21.664C48.4941 21.3653 48.1254 20.9407 47.8641 20.39C47.6028 19.8393 47.4721 19.2 47.4721 18.472C47.4721 17.744 47.6028 17.1 47.8641 16.54C48.1348 15.9707 48.5081 15.532 48.9841 15.224C49.4601 14.9067 50.0014 14.748 50.6081 14.748C51.1214 14.748 51.5788 14.86 51.9801 15.084C52.3814 15.2987 52.6894 15.602 52.9041 15.994V14.93H54.6261ZM51.0841 20.74C51.6628 20.74 52.1108 20.5393 52.4281 20.138C52.7454 19.7367 52.9041 19.172 52.9041 18.444C52.9041 17.6973 52.7454 17.1233 52.4281 16.722C52.1108 16.3207 51.6581 16.12 51.0701 16.12C50.4914 16.12 50.0388 16.33 49.7121 16.75C49.3948 17.1607 49.2361 17.7347 49.2361 18.472C49.2361 19.2 49.3948 19.76 49.7121 20.152C50.0388 20.544 50.4961 20.74 51.0841 20.74ZM60.3401 14.762C60.6107 14.762 60.8441 14.7993 61.0401 14.874L61.0261 16.47C60.7274 16.3487 60.4194 16.288 60.1021 16.288C59.5047 16.288 59.0474 16.4607 58.7301 16.806C58.4221 17.1513 58.2681 17.6087 58.2681 18.178V22H56.5321V16.932C56.5321 16.1853 56.4947 15.518 56.4201 14.93H58.0581L58.1981 16.176C58.3754 15.7187 58.6554 15.3687 59.0381 15.126C59.4207 14.8833 59.8547 14.762 60.3401 14.762ZM69.1303 14.93L65.8403 22.476C65.467 23.3533 64.991 23.9927 64.4123 24.394C63.8337 24.7953 63.1197 25.0613 62.2703 25.192L61.8783 23.848C62.541 23.6987 63.0357 23.5073 63.3623 23.274C63.6983 23.05 63.969 22.714 64.1743 22.266L64.3983 21.748L61.4583 14.93H63.2923L65.3223 20.012L67.3943 14.93H69.1303Z"
            fill={colors.apc_gray_5}
          />
        </g>
      }
      viewBox="0 0 80 32"
      iconStyle={{
        width: 80,
        height: 32,
      }}
      buttonTypeButton
      hideBackground={buttonProps.hideBackground}
      color={buttonProps.color}
      clickColor={buttonProps.clickColor}
      activeColor={buttonProps.activeColor}
      hoverColor={buttonProps.hoverColor}
      isActive={isActive}
      onClick={onClick}
    />
  );
};
PrimaryButtonTypeButton.propTypes = {
  buttonProps: PropTypes.object,
  isActive: PropTypes.bool,
  onClick: PropTypes.func,
};

const SecondaryButtonTypeButton = ({ buttonProps, isActive, onClick }) => {
  return (
    <IconButton
      isMenuButton
      pathNode={
        <g>
          <path
            d="M20.452 22.126C19.6867 22.126 18.968 22.0233 18.296 21.818C17.6333 21.6127 17.0687 21.3187 16.602 20.936L17.218 19.62C17.7033 19.984 18.2073 20.2547 18.73 20.432C19.262 20.6 19.8407 20.684 20.466 20.684C21.1567 20.684 21.6887 20.5627 22.062 20.32C22.4447 20.0773 22.636 19.7367 22.636 19.298C22.636 18.9247 22.4587 18.64 22.104 18.444C21.7587 18.2387 21.1847 18.0473 20.382 17.87C19.1313 17.5993 18.2213 17.2493 17.652 16.82C17.0827 16.3907 16.798 15.7653 16.798 14.944C16.798 14.356 16.9567 13.8333 17.274 13.376C17.5913 12.9187 18.0393 12.5593 18.618 12.298C19.206 12.0367 19.8827 11.906 20.648 11.906C21.348 11.906 22.0107 12.0133 22.636 12.228C23.2707 12.4333 23.7887 12.7227 24.19 13.096L23.588 14.412C22.7013 13.7027 21.7213 13.348 20.648 13.348C20.004 13.348 19.4953 13.4833 19.122 13.754C18.7487 14.0153 18.562 14.3747 18.562 14.832C18.562 15.224 18.7253 15.5227 19.052 15.728C19.388 15.9333 19.9573 16.1247 20.76 16.302C21.5907 16.498 22.2673 16.7127 22.79 16.946C23.3127 17.17 23.714 17.464 23.994 17.828C24.274 18.1827 24.414 18.6307 24.414 19.172C24.414 19.7693 24.2553 20.292 23.938 20.74C23.6207 21.1787 23.1633 21.5193 22.566 21.762C21.9687 22.0047 21.264 22.126 20.452 22.126ZM32.1498 18.556H27.2778C27.3151 19.312 27.5018 19.872 27.8378 20.236C28.1831 20.5907 28.6918 20.768 29.3638 20.768C30.1384 20.768 30.8571 20.516 31.5198 20.012L32.0238 21.216C31.6878 21.4867 31.2724 21.706 30.7778 21.874C30.2924 22.0327 29.7978 22.112 29.2938 22.112C28.1364 22.112 27.2264 21.7853 26.5638 21.132C25.9011 20.4787 25.5698 19.5827 25.5698 18.444C25.5698 17.7253 25.7144 17.086 26.0038 16.526C26.2931 15.966 26.6991 15.532 27.2218 15.224C27.7444 14.9067 28.3371 14.748 28.9998 14.748C29.9704 14.748 30.7358 15.0653 31.2958 15.7C31.8651 16.3253 32.1498 17.1887 32.1498 18.29V18.556ZM29.0418 16.008C28.5751 16.008 28.1924 16.148 27.8938 16.428C27.6044 16.6987 27.4178 17.0953 27.3338 17.618H30.6238C30.5678 17.086 30.4044 16.6847 30.1338 16.414C29.8724 16.1433 29.5084 16.008 29.0418 16.008ZM36.8377 22.112C36.1004 22.112 35.461 21.9627 34.9197 21.664C34.3784 21.3653 33.963 20.9453 33.6737 20.404C33.3844 19.8533 33.2397 19.2093 33.2397 18.472C33.2397 17.7347 33.389 17.086 33.6877 16.526C33.9957 15.966 34.425 15.532 34.9757 15.224C35.5357 14.9067 36.1797 14.748 36.9077 14.748C37.4117 14.748 37.897 14.8273 38.3637 14.986C38.8397 15.1447 39.2177 15.364 39.4977 15.644L39.0077 16.876C38.709 16.6333 38.3917 16.4513 38.0557 16.33C37.7197 16.1993 37.3884 16.134 37.0617 16.134C36.427 16.134 35.9324 16.3347 35.5777 16.736C35.223 17.128 35.0457 17.6973 35.0457 18.444C35.0457 19.1907 35.2184 19.76 35.5637 20.152C35.9184 20.544 36.4177 20.74 37.0617 20.74C37.3884 20.74 37.7197 20.6747 38.0557 20.544C38.3917 20.4133 38.709 20.2267 39.0077 19.984L39.4977 21.216C39.199 21.496 38.8117 21.7153 38.3357 21.874C37.8597 22.0327 37.3604 22.112 36.8377 22.112ZM43.8787 22.112C43.16 22.112 42.5254 21.9627 41.9747 21.664C41.4334 21.3653 41.0134 20.9407 40.7147 20.39C40.4254 19.83 40.2807 19.1767 40.2807 18.43C40.2807 17.6833 40.4254 17.0347 40.7147 16.484C41.0134 15.924 41.4334 15.4947 41.9747 15.196C42.5254 14.8973 43.16 14.748 43.8787 14.748C44.588 14.748 45.2134 14.8973 45.7547 15.196C46.296 15.4947 46.7114 15.924 47.0007 16.484C47.2994 17.0347 47.4487 17.6833 47.4487 18.43C47.4487 19.1767 47.2994 19.83 47.0007 20.39C46.7114 20.9407 46.296 21.3653 45.7547 21.664C45.2134 21.9627 44.588 22.112 43.8787 22.112ZM43.8647 20.74C44.462 20.74 44.9147 20.5487 45.2227 20.166C45.54 19.774 45.6987 19.1953 45.6987 18.43C45.6987 17.674 45.54 17.1 45.2227 16.708C44.9054 16.3067 44.4574 16.106 43.8787 16.106C43.2907 16.106 42.838 16.3067 42.5207 16.708C42.2034 17.1 42.0447 17.674 42.0447 18.43C42.0447 19.1953 42.1987 19.774 42.5067 20.166C42.824 20.5487 43.2767 20.74 43.8647 20.74ZM53.0039 14.748C54.6932 14.748 55.5379 15.7187 55.5379 17.66V22H53.7879V17.744C53.7879 17.184 53.6805 16.778 53.4659 16.526C53.2512 16.2647 52.9199 16.134 52.4719 16.134C51.9399 16.134 51.5105 16.3067 51.1839 16.652C50.8572 16.988 50.6939 17.436 50.6939 17.996V22H48.9579V16.932C48.9579 16.1853 48.9205 15.518 48.8459 14.93H50.4839L50.6099 16.092C50.8525 15.6627 51.1792 15.3313 51.5899 15.098C52.0099 14.8647 52.4812 14.748 53.0039 14.748ZM64.1554 11.696V22H62.4334V20.866C62.2187 21.258 61.9107 21.566 61.5094 21.79C61.1081 22.0047 60.6507 22.112 60.1374 22.112C59.5307 22.112 58.9894 21.958 58.5134 21.65C58.0374 21.342 57.6641 20.908 57.3934 20.348C57.1321 19.7787 57.0014 19.13 57.0014 18.402C57.0014 17.674 57.1321 17.0347 57.3934 16.484C57.6547 15.9333 58.0234 15.5087 58.4994 15.21C58.9754 14.902 59.5214 14.748 60.1374 14.748C60.6414 14.748 61.0894 14.8553 61.4814 15.07C61.8827 15.2753 62.1907 15.5693 62.4054 15.952V11.696H64.1554ZM60.5994 20.74C61.1781 20.74 61.6261 20.5393 61.9434 20.138C62.2701 19.7367 62.4334 19.1673 62.4334 18.43C62.4334 17.6927 62.2747 17.1233 61.9574 16.722C61.6401 16.3207 61.1921 16.12 60.6134 16.12C60.0254 16.12 59.5681 16.3207 59.2414 16.722C58.9241 17.114 58.7654 17.674 58.7654 18.402C58.7654 19.1393 58.9241 19.7133 59.2414 20.124C59.5681 20.5347 60.0207 20.74 60.5994 20.74ZM72.8234 14.93V22H71.1014V20.866C70.8867 21.258 70.5787 21.566 70.1774 21.79C69.776 22.0047 69.3187 22.112 68.8054 22.112C68.1894 22.112 67.6434 21.9627 67.1674 21.664C66.6914 21.3653 66.3227 20.9407 66.0614 20.39C65.8 19.8393 65.6694 19.2 65.6694 18.472C65.6694 17.744 65.8 17.1 66.0614 16.54C66.332 15.9707 66.7054 15.532 67.1814 15.224C67.6574 14.9067 68.1987 14.748 68.8054 14.748C69.3187 14.748 69.776 14.86 70.1774 15.084C70.5787 15.2987 70.8867 15.602 71.1014 15.994V14.93H72.8234ZM69.2814 20.74C69.86 20.74 70.308 20.5393 70.6254 20.138C70.9427 19.7367 71.1014 19.172 71.1014 18.444C71.1014 17.6973 70.9427 17.1233 70.6254 16.722C70.308 16.3207 69.8554 16.12 69.2674 16.12C68.6887 16.12 68.236 16.33 67.9094 16.75C67.592 17.1607 67.4334 17.7347 67.4334 18.472C67.4334 19.2 67.592 19.76 67.9094 20.152C68.236 20.544 68.6934 20.74 69.2814 20.74ZM78.5373 14.762C78.808 14.762 79.0413 14.7993 79.2373 14.874L79.2233 16.47C78.9247 16.3487 78.6167 16.288 78.2993 16.288C77.702 16.288 77.2447 16.4607 76.9273 16.806C76.6193 17.1513 76.4653 17.6087 76.4653 18.178V22H74.7293V16.932C74.7293 16.1853 74.692 15.518 74.6173 14.93H76.2553L76.3953 16.176C76.5727 15.7187 76.8527 15.3687 77.2353 15.126C77.618 14.8833 78.052 14.762 78.5373 14.762ZM87.3276 14.93L84.0376 22.476C83.6643 23.3533 83.1883 23.9927 82.6096 24.394C82.0309 24.7953 81.3169 25.0613 80.4676 25.192L80.0756 23.848C80.7383 23.6987 81.2329 23.5073 81.5596 23.274C81.8956 23.05 82.1663 22.714 82.3716 22.266L82.5956 21.748L79.6556 14.93H81.4896L83.5196 20.012L85.5916 14.93H87.3276Z"
            fill={colors.apc_gray_5}
          />
        </g>
      }
      viewBox="0 0 100 32"
      iconStyle={{
        width: 100,
        height: 32,
      }}
      buttonTypeButton
      hideBackground={buttonProps.hideBackground}
      color={buttonProps.color}
      clickColor={buttonProps.clickColor}
      activeColor={buttonProps.activeColor}
      hoverColor={buttonProps.hoverColor}
      isActive={isActive}
      onClick={onClick}
    />
  );
};
SecondaryButtonTypeButton.propTypes = {
  buttonProps: PropTypes.object,
  isActive: PropTypes.bool,
  onClick: PropTypes.func,
};

const ButtonTypeButton = props => {
  const { blockType, isActive, onClick } = props;
  const buttonProps = getButtonProps(isActive);
  switch (blockType) {
    case BUTTON_TYPES.PRIMARY:
      return (
        <PrimaryButtonTypeButton
          buttonProps={buttonProps}
          isActive={isActive}
          onClick={onClick}
        />
      );
    case BUTTON_TYPES.SECONDARY:
      return (
        <SecondaryButtonTypeButton
          buttonProps={buttonProps}
          isActive={isActive}
          onClick={onClick}
        />
      );
    default:
      return (
        <DefaultButtonTypeButton
          buttonProps={buttonProps}
          isActive={isActive}
          onClick={onClick}
        />
      );
  }
};
ButtonTypeButton.propTypes = {
  blockType: PropTypes.string,
  isActive: PropTypes.bool,
  onClick: PropTypes.func,
};

export default ButtonTypeButton;
