import React from 'react';

import { colors } from '../helpers/styles/editor';
import BlockButton from './BlockButton';

export default class FormRadioButton extends React.PureComponent {
  render() {
    return (
      <BlockButton
        ariaLabel="radio selects"
        pathNode={
          <path
            fillRule="evenodd"
            clipRule="evenodd"
            d="M18.75 12C18.75 15.7279 15.7279 18.75 12 18.75C8.27208 18.75 5.25 15.7279 5.25 12C5.25 8.27208 8.27208 5.25 12 5.25C15.7279 5.25 18.75 8.27208 18.75 12ZM20 12C20 16.4183 16.4183 20 12 20C7.58172 20 4 16.4183 4 12C4 7.58172 7.58172 4 12 4C16.4183 4 20 7.58172 20 12ZM12 15C13.6569 15 15 13.6569 15 12C15 10.3431 13.6569 9 12 9C10.3431 9 9 10.3431 9 12C9 13.6569 10.3431 15 12 15Z"
            fill={colors.apc_purple}
          />
        }
        viewBox="0 0 24 24"
        {...this.props}
      />
    );
  }
}
