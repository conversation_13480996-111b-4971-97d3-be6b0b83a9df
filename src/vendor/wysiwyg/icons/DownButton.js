import React from 'react';

import IconButton from './IconButton';

export default class DownButton extends React.PureComponent {
  render() {
    return (
      <IconButton
        ariaLabel="open menu"
        pathNode={
          <path
            d="M7.57737 10.3236L4.17631 6.92256C3.94123 6.68749 3.94123 6.30737 4.17631 6.07479L4.74148 5.50962C4.97656 5.27454 5.35667 5.27454 5.58925 5.50962L8 7.92037L10.4108 5.50962C10.6458 5.27454 11.0259 5.27454 11.2585 5.50962L11.8237 6.07479C12.0588 6.30987 12.0588 6.68999 11.8237 6.92256L8.42263 10.3236C8.19256 10.5587 7.81244 10.5587 7.57737 10.3236Z"
            fill="#7E89A9"
          />
        }
        viewBox="0 0 12 12"
        {...this.props}
      />
    );
  }
}
