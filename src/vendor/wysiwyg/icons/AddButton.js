import React from 'react';
import PropTypes from 'prop-types';

import IconButton from './IconButton';

export default class AddButton extends React.PureComponent {
  render() {
    return (
      <IconButton
        ariaLabel="add component button"
        pathNode={
          <path d="M22 11.5v3c0 0.828-0.672 1.5-1.5 1.5h-6.5v6.5c0 0.828-0.672 1.5-1.5 1.5h-3c-0.828 0-1.5-0.672-1.5-1.5v-6.5h-6.5c-0.828 0-1.5-0.672-1.5-1.5v-3c0-0.828 0.672-1.5 1.5-1.5h6.5v-6.5c0-0.828 0.672-1.5 1.5-1.5h3c0.828 0 1.5 0.672 1.5 1.5v6.5h6.5c0.828 0 1.5 0.672 1.5 1.5z" />
        }
        viewBox="0 0 22 28"
        color="#00b850"
        {...this.props}
        iconStyle={{
          width: 16,
          height: 16,
        }}
        isAddButton
      />
    );
  }
}

AddButton.propTypes = {
  shadow: PropTypes.bool,
  colorOverride: PropTypes.string,
};
