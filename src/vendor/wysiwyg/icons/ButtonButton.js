import React from 'react';

import { colors } from '../helpers/styles/editor';
import BlockButton from './BlockButton';

export default class ButtonButton extends React.PureComponent {
  render() {
    return (
      <BlockButton
        ariaLabel="add button"
        pathNode={
          <path
            fillRule="evenodd"
            clipRule="evenodd"
            d="M5.25 14.75V9.25H18.75V14.75H5.25ZM19 8H5C4.44772 8 4 8.44772 4 9V15C4 15.5523 4.44772 16 5 16H19C19.5523 16 20 15.5523 20 15V9C20 8.44772 19.5523 8 19 8ZM9 11.375C8.65482 11.375 8.375 11.6548 8.375 12C8.375 12.3452 8.65482 12.625 9 12.625H15C15.3452 12.625 15.625 12.3452 15.625 12C15.625 11.6548 15.3452 11.375 15 11.375H9Z"
            fill={colors.apc_purple}
          />
        }
        viewBox="0 0 24 24"
        {...this.props}
      />
    );
  }
}
