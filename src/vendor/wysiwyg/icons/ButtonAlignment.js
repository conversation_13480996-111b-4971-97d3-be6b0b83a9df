import React from 'react';
import PropTypes from 'prop-types';
import { ALIGNMENT_TYPE } from '../helpers/constants';
import ActionsMenu from '../components/ActionsMenu';

const pathNodeByAlignmentType = {
  [ALIGNMENT_TYPE.LEFT]: (
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M4.5 4C4.22386 4 4 4.22386 4 4.5V19.5C4 19.7761 4.22386 20 4.5 20H5.5C5.77614 20 6 19.7761 6 19.5V4.5C6 4.22386 5.77614 4 5.5 4H4.5ZM8 7.5C8 7.22386 8.22386 7 8.5 7H19.5C19.7761 7 20 7.22386 20 7.5V9.5C20 9.77614 19.7761 10 19.5 10H8.5C8.22386 10 8 9.77614 8 9.5V7.5ZM8 14.5C8 14.2239 8.22386 14 8.5 14H13.5C13.7761 14 14 14.2239 14 14.5V16.5C14 16.7761 13.7761 17 13.5 17H8.5C8.22386 17 8 16.7761 8 16.5V14.5Z"
    />
  ),
  [ALIGNMENT_TYPE.CENTER]: (
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M11.5 4C11.2239 4 11 4.22386 11 4.5V7H5.5C5.22386 7 5 7.22386 5 7.5V9.5C5 9.77614 5.22386 10 5.5 10H11V14H8.5C8.22386 14 8 14.2239 8 14.5V16.5C8 16.7761 8.22386 17 8.5 17H11V19.5C11 19.7761 11.2239 20 11.5 20H12.5C12.7761 20 13 19.7761 13 19.5V17H15.5C15.7761 17 16 16.7761 16 16.5V14.5C16 14.2239 15.7761 14 15.5 14H13V10H18.5C18.7761 10 19 9.77614 19 9.5V7.5C19 7.22386 18.7761 7 18.5 7H13V4.5C13 4.22386 12.7761 4 12.5 4H11.5Z"
    />
  ),
  [ALIGNMENT_TYPE.RIGHT]: (
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M18.5 4C18.2239 4 18 4.22386 18 4.5V19.5C18 19.7761 18.2239 20 18.5 20H19.5C19.7761 20 20 19.7761 20 19.5V4.5C20 4.22386 19.7761 4 19.5 4H18.5ZM4 7.5C4 7.22386 4.22386 7 4.5 7H15.5C15.7761 7 16 7.22386 16 7.5V9.5C16 9.77614 15.7761 10 15.5 10H4.5C4.22386 10 4 9.77614 4 9.5V7.5ZM10 14.5C10 14.2239 10.2239 14 10.5 14H15.5C15.7761 14 16 14.2239 16 14.5V16.5C16 16.7761 15.7761 17 15.5 17H10.5C10.2239 17 10 16.7761 10 16.5V14.5Z"
    />
  ),
};

const ButtonAlignment = props => {
  const { textAlign } = props;

  return (
    <ActionsMenu.IconButton
      ariaLabel={`align ${textAlign}`}
      pathNode={pathNodeByAlignmentType[textAlign]}
      viewBox="0 0 24 24"
      {...props}
    />
  );
};

ButtonAlignment.propTypes = {
  textAlign: PropTypes.oneOf([
    ALIGNMENT_TYPE.LEFT,
    ALIGNMENT_TYPE.CENTER,
    ALIGNMENT_TYPE.RIGHT,
  ]),
};

export default ButtonAlignment;
