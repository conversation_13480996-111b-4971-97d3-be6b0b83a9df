import React from 'react';

import { colors } from '../helpers/styles/editor';
import BlockButton from './BlockButton';

export default class ImageBlockButton extends React.PureComponent {
  render() {
    return (
      <BlockButton
        ariaLabel="image / gif"
        pathNode={
          <path
            fillRule="evenodd"
            clipRule="evenodd"
            d="M5.25 7.5V16H9.58334L14.3183 12.7219C14.7357 12.433 15.281 12.4061 15.7248 12.6527L18.75 14.3333V7.5H5.25ZM4.66667 6.25C4.29848 6.25 4 6.54848 4 6.91667V16.5833C4 16.9515 4.29848 17.25 4.66667 17.25H19.3333C19.7015 17.25 20 16.9515 20 16.5833V6.91667C20 6.54848 19.7015 6.25 19.3333 6.25H4.66667ZM9.5 12.25C10.3284 12.25 11 11.5784 11 10.75C11 9.92157 10.3284 9.25 9.5 9.25C8.67157 9.25 8 9.92157 8 10.75C8 11.5784 8.67157 12.25 9.5 12.25Z"
            fill={colors.apc_purple}
          />
        }
        viewBox="0 0 24 24"
        {...this.props}
      />
    );
  }
}
