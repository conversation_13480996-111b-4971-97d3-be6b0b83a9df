import React from 'react';

import IconButton from './IconButton';

export default class UserPropertyButton extends React.PureComponent {
  render() {
    return (
      <IconButton
        title="user property"
        ariaLabel="user property"
        pathNode={
          <path d="M9.6 11.4C9.01667 11.4 8.47917 11.2583 7.9875 10.975C7.49583 10.6917 7.10833 10.3042 6.825 9.8125C6.54167 9.32083 6.4 8.78333 6.4 8.2C6.4 7.61667 6.54167 7.07917 6.825 6.5875C7.10833 6.09583 7.49583 5.70833 7.9875 5.425C8.47917 5.14167 9.01667 5 9.6 5C10.1833 5 10.7208 5.14167 11.2125 5.425C11.7042 5.70833 12.0917 6.09583 12.375 6.5875C12.6583 7.07917 12.8 7.61667 12.8 8.2C12.8 8.78333 12.6583 9.32083 12.375 9.8125C12.0917 10.3042 11.7042 10.6917 11.2125 10.975C10.7208 11.2583 10.1833 11.4 9.6 11.4ZM11.85 12.2C12.3333 12.2 12.7917 12.3 13.225 12.5C13.6583 12.7 14.0333 12.975 14.35 13.325L12.2 15.475L12 17.275C11.9833 17.4417 12.0083 17.6167 12.075 17.8H5.2C4.86667 17.8 4.58333 17.6833 4.35 17.45C4.11667 17.2167 4 16.9333 4 16.6V15.55C4 14.95 4.15 14.3917 4.45 13.875C4.75 13.3583 5.15833 12.95 5.675 12.65C6.19167 12.35 6.75 12.2 7.35 12.2H7.775C8.35833 12.4667 8.96667 12.6 9.6 12.6C10.2333 12.6 10.8417 12.4667 11.425 12.2H11.85ZM12.975 15.825L16.425 12.375L18.225 14.175L14.775 17.625L13.25 17.8C13.1167 17.8167 13.0042 17.7792 12.9125 17.6875C12.8208 17.5958 12.7833 17.4833 12.8 17.35L12.975 15.825ZM19.825 11.725C19.9417 11.8417 20 11.9833 20 12.15C20 12.3167 19.9417 12.4583 19.825 12.575L18.775 13.625L16.975 11.825L18.025 10.775C18.1417 10.6583 18.2833 10.6 18.45 10.6C18.6167 10.6 18.7583 10.6583 18.875 10.775L19.825 11.725Z" />
        }
        viewBox="0 0 24 24"
        {...this.props}
      />
    );
  }
}
