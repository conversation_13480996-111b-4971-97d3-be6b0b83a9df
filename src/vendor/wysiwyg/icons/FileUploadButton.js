import React from 'react';

import IconButton from './IconButton';

export default class FileUploadButton extends React.PureComponent {
  render() {
    return (
      <IconButton
        title="Upload file"
        ariaLabel="Upload file"
        pathNode={
          <path d="M13.25 16H10.75C10.5417 16 10.3646 15.9271 10.2188 15.7812C10.0729 15.6354 10 15.4583 10 15.25V10H7.25C6.97917 10 6.79167 9.86979 6.6875 9.60938C6.58333 9.34896 6.625 9.125 6.8125 8.9375L11.5625 4.1875C11.6875 4.0625 11.8333 4 12 4C12.1667 4 12.3125 4.0625 12.4375 4.1875L17.1875 8.9375C17.375 9.125 17.4167 9.34896 17.3125 9.60938C17.2083 9.86979 17.0208 10 16.75 10H14V15.25C14 15.4583 13.9271 15.6354 13.7812 15.7812C13.6354 15.9271 13.4583 16 13.25 16ZM20 15.75V19.25C20 19.4583 19.9271 19.6354 19.7812 19.7812C19.6354 19.9271 19.4583 20 19.25 20H4.75C4.54167 20 4.36458 19.9271 4.21875 19.7812C4.07292 19.6354 4 19.4583 4 19.25V15.75C4 15.5417 4.07292 15.3646 4.21875 15.2188C4.36458 15.0729 4.54167 15 4.75 15H9V15.25C9 15.7292 9.17188 16.1406 9.51562 16.4844C9.85938 16.8281 10.2708 17 10.75 17H13.25C13.7292 17 14.1406 16.8281 14.4844 16.4844C14.8281 16.1406 15 15.7292 15 15.25V15H19.25C19.4583 15 19.6354 15.0729 19.7812 15.2188C19.9271 15.3646 20 15.5417 20 15.75ZM16.125 18.5C16.125 18.3333 16.0625 18.1875 15.9375 18.0625C15.8125 17.9375 15.6667 17.875 15.5 17.875C15.3333 17.875 15.1875 17.9375 15.0625 18.0625C14.9375 18.1875 14.875 18.3333 14.875 18.5C14.875 18.6667 14.9375 18.8125 15.0625 18.9375C15.1875 19.0625 15.3333 19.125 15.5 19.125C15.6667 19.125 15.8125 19.0625 15.9375 18.9375C16.0625 18.8125 16.125 18.6667 16.125 18.5ZM18.125 18.5C18.125 18.3333 18.0625 18.1875 17.9375 18.0625C17.8125 17.9375 17.6667 17.875 17.5 17.875C17.3333 17.875 17.1875 17.9375 17.0625 18.0625C16.9375 18.1875 16.875 18.3333 16.875 18.5C16.875 18.6667 16.9375 18.8125 17.0625 18.9375C17.1875 19.0625 17.3333 19.125 17.5 19.125C17.6667 19.125 17.8125 19.0625 17.9375 18.9375C18.0625 18.8125 18.125 18.6667 18.125 18.5Z" />
        }
        viewBox="0 0 24 24"
        {...this.props}
      />
    );
  }
}
