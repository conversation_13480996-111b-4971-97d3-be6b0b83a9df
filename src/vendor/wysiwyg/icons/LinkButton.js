import React from 'react';

import IconButton from './IconButton';

export default class LinkButton extends React.PureComponent {
  render() {
    return (
      <IconButton
        ariaLabel="link"
        pathNode={
          <path d="M14.2187 9.78124C14.8229 10.4062 15.2291 11.125 15.4375 11.9375C15.6458 12.75 15.6458 13.5625 15.4375 14.375C15.2291 15.1875 14.8229 15.8958 14.2187 16.5L12.0937 18.625C11.4896 19.2291 10.7812 19.6354 9.96874 19.8437C9.15624 20.052 8.34374 20.052 7.53124 19.8437C6.71874 19.6354 6.0052 19.2239 5.39062 18.6093C4.77604 17.9948 4.36458 17.2812 4.15625 16.4687C3.94792 15.6562 3.94792 14.8437 4.15625 14.0312C4.36458 13.2187 4.77083 12.5104 5.375 11.9062L6.56249 10.7187C6.70833 10.5729 6.8802 10.5417 7.07812 10.625C7.27603 10.7083 7.38541 10.8541 7.40624 11.0625C7.42708 11.625 7.53124 12.1771 7.71874 12.7187C7.78124 12.9062 7.73957 13.0729 7.59374 13.2187L7.18749 13.6562C6.74999 14.0937 6.52604 14.6198 6.51562 15.2343C6.5052 15.8489 6.71874 16.3802 7.15624 16.8281C7.59374 17.276 8.12499 17.5 8.74999 17.5C9.37499 17.5 9.90624 17.2812 10.3437 16.8437L12.4375 14.75C12.875 14.3125 13.0937 13.7812 13.0937 13.1562C13.0937 12.5312 12.875 12 12.4375 11.5625C12.3333 11.4583 12.2187 11.3646 12.0937 11.2812C11.9687 11.1979 11.9062 11.0729 11.9062 10.9062C11.8854 10.5312 12 10.2187 12.25 9.96874L12.9375 9.31249C13.0208 9.22915 13.1198 9.18228 13.2344 9.17186C13.3489 9.16145 13.4583 9.18749 13.5625 9.24999C13.7916 9.41665 14.0104 9.59374 14.2187 9.78124ZM18.625 5.375C19.2291 6 19.6354 6.71874 19.8437 7.53124C20.052 8.34374 20.052 9.15624 19.8437 9.96874C19.6354 10.7812 19.2291 11.4896 18.625 12.0937L17.4375 13.2812C17.2916 13.4271 17.1198 13.4583 16.9218 13.375C16.7239 13.2916 16.6146 13.1458 16.5937 12.9375C16.5729 12.375 16.4687 11.8229 16.2812 11.2812C16.2187 11.0937 16.2604 10.9271 16.4062 10.7812L16.8125 10.3437C17.25 9.90624 17.4739 9.3802 17.4843 8.76561C17.4948 8.15103 17.2812 7.61978 16.8437 7.17187C16.4062 6.72395 15.875 6.49999 15.25 6.49999C14.625 6.49999 14.0937 6.71874 13.6562 7.15624L11.5625 9.24999C11.125 9.68749 10.9062 10.2187 10.9062 10.8437C10.9062 11.4687 11.125 12 11.5625 12.4375C11.6666 12.5416 11.7812 12.6354 11.9062 12.7187C12.0312 12.8021 12.0937 12.9271 12.0937 13.0937C12.1146 13.4687 12 13.7812 11.75 14.0312L11.0625 14.6875C10.9791 14.7708 10.8802 14.8177 10.7656 14.8281C10.651 14.8385 10.5417 14.8125 10.4375 14.75C10.2083 14.5833 9.98957 14.4062 9.78124 14.2187C9.17707 13.5937 8.77082 12.875 8.56249 12.0625C8.35416 11.25 8.35416 10.4375 8.56249 9.62499C8.77082 8.81249 9.17707 8.10416 9.78124 7.49999L11.9062 5.375C12.5104 4.77083 13.2187 4.36458 14.0312 4.15625C14.8437 3.94792 15.6562 3.94792 16.4687 4.15625C17.2812 4.36458 18 4.77083 18.625 5.375Z" />
        }
        viewBox="0 0 24 24"
        {...this.props}
      />
    );
  }
}
