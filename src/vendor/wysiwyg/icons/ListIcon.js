import React from 'react';
import PropTypes from 'prop-types';
import { LIST_STYLE_TYPE } from '../helpers/constants';
import ActionsMenu from '../components/ActionsMenu';

export default class ListIcon extends React.PureComponent {
  getPathNode(listType) {
    switch (listType) {
      case LIST_STYLE_TYPE.UNORDERED_LIST_ITEM:
        return (
          <path d="M7 7.5C7 7.91667 6.85417 8.27083 6.5625 8.5625C6.27083 8.85417 5.91667 9 5.5 9C5.08333 9 4.72917 8.85417 4.4375 8.5625C4.14583 8.27083 4 7.91667 4 7.5C4 7.08333 4.14583 6.72917 4.4375 6.4375C4.72917 6.14583 5.08333 6 5.5 6C5.91667 6 6.27083 6.14583 6.5625 6.4375C6.85417 6.72917 7 7.08333 7 7.5ZM5.5 11C5.91667 11 6.27083 11.1458 6.5625 11.4375C6.85417 11.7292 7 12.0833 7 12.5C7 12.9167 6.85417 13.2708 6.5625 13.5625C6.27083 13.8542 5.91667 14 5.5 14C5.08333 14 4.72917 13.8542 4.4375 13.5625C4.14583 13.2708 4 12.9167 4 12.5C4 12.0833 4.14583 11.7292 4.4375 11.4375C4.72917 11.1458 5.08333 11 5.5 11ZM5.5 16C5.91667 16 6.27083 16.1458 6.5625 16.4375C6.85417 16.7292 7 17.0833 7 17.5C7 17.9167 6.85417 18.2708 6.5625 18.5625C6.27083 18.8542 5.91667 19 5.5 19C5.08333 19 4.72917 18.8542 4.4375 18.5625C4.14583 18.2708 4 17.9167 4 17.5C4 17.0833 4.14583 16.7292 4.4375 16.4375C4.72917 16.1458 5.08333 16 5.5 16ZM8.5 8.625C8.35417 8.625 8.23438 8.57812 8.14062 8.48438C8.04688 8.39062 8 8.27083 8 8.125V6.875C8 6.72917 8.04688 6.60938 8.14062 6.51562C8.23438 6.42188 8.35417 6.375 8.5 6.375H19.5C19.6458 6.375 19.7656 6.42188 19.8594 6.51562C19.9531 6.60938 20 6.72917 20 6.875V8.125C20 8.27083 19.9531 8.39062 19.8594 8.48438C19.7656 8.57812 19.6458 8.625 19.5 8.625H8.5ZM8.5 13.625C8.35417 13.625 8.23438 13.5781 8.14062 13.4844C8.04688 13.3906 8 13.2708 8 13.125V11.875C8 11.7292 8.04688 11.6094 8.14062 11.5156C8.23438 11.4219 8.35417 11.375 8.5 11.375H19.5C19.6458 11.375 19.7656 11.4219 19.8594 11.5156C19.9531 11.6094 20 11.7292 20 11.875V13.125C20 13.2708 19.9531 13.3906 19.8594 13.4844C19.7656 13.5781 19.6458 13.625 19.5 13.625H8.5ZM8.5 18.625C8.35417 18.625 8.23438 18.5781 8.14062 18.4844C8.04688 18.3906 8 18.2708 8 18.125V16.875C8 16.7292 8.04688 16.6094 8.14062 16.5156C8.23438 16.4219 8.35417 16.375 8.5 16.375H19.5C19.6458 16.375 19.7656 16.4219 19.8594 16.5156C19.9531 16.6094 20 16.7292 20 16.875V18.125C20 18.2708 19.9531 18.3906 19.8594 18.4844C19.7656 18.5781 19.6458 18.625 19.5 18.625H8.5Z" />
        );

      case LIST_STYLE_TYPE.ORDERED_LIST_ITEM:
        return (
          <path d="M4.03137 8.38824C4.03137 8.13725 4.15686 8.01176 4.40784 8.01176H4.87843V6.75686L4.9098 6.44314L4.78431 6.53725C4.72157 6.62092 4.64314 6.65752 4.54902 6.64706C4.4549 6.6366 4.37647 6.58954 4.31373 6.50588L4.12549 6.31765C3.95817 6.15033 3.96863 5.98301 4.15686 5.81569L4.81569 5.18824C4.96209 5.06275 5.11895 5 5.28627 5H5.66274C5.91373 5 6.03922 5.12549 6.03922 5.37647V8.01176H6.5098C6.76078 8.01176 6.88627 8.13725 6.88627 8.38824V8.63922C6.88627 8.8902 6.76078 9.01569 6.5098 9.01569H4.40784C4.15686 9.01569 4.03137 8.8902 4.03137 8.63922V8.38824ZM4 13.5647C4 13.1255 4.13595 12.749 4.40784 12.4353C4.57516 12.2471 4.82614 12.0379 5.16078 11.8078C5.3281 11.7033 5.44314 11.6196 5.50588 11.5569C5.56863 11.4941 5.6 11.4314 5.6 11.3686C5.6 11.2013 5.50588 11.1176 5.31765 11.1176C5.21307 11.1176 5.1085 11.1595 5.00392 11.2431C4.8366 11.4105 4.66928 11.4314 4.50196 11.3059L4.25098 11.0863C4.06275 10.9399 4.03137 10.783 4.15686 10.6157C4.44967 10.2183 4.86797 10.0196 5.41176 10.0196C5.78824 10.0196 6.10196 10.1137 6.35294 10.302C6.66667 10.532 6.82353 10.8562 6.82353 11.2745C6.82353 11.6091 6.69804 11.9124 6.44706 12.1843C6.30065 12.3307 6.06013 12.5085 5.72549 12.7176C5.45359 12.8641 5.30719 12.9686 5.28627 13.0314H6.5098C6.76078 13.0314 6.88627 13.1569 6.88627 13.4078V13.6902C6.88627 13.9203 6.76078 14.0353 6.5098 14.0353H4.37647C4.2719 14.0353 4.18301 14.0039 4.1098 13.9412C4.0366 13.8784 4 13.7948 4 13.6902V13.5647ZM4.28235 17.9569C4.40784 17.7686 4.57516 17.7373 4.78431 17.8627C4.93072 17.9255 5.07712 17.9569 5.22353 17.9569C5.53725 17.9569 5.69412 17.868 5.69412 17.6902C5.69412 17.5124 5.5268 17.4235 5.19216 17.4235H5.03529C4.86797 17.4235 4.74248 17.3399 4.65882 17.1725L4.62745 17.1098C4.54379 16.9634 4.56471 16.8065 4.6902 16.6392L4.87843 16.4196C5.02484 16.2523 5.15033 16.1163 5.2549 16.0118V15.9804L4.87843 16.0431H4.47059C4.21961 16.0431 4.09412 15.9176 4.09412 15.6667V15.3843C4.09412 15.1542 4.21961 15.0392 4.47059 15.0392H6.2902C6.54118 15.0392 6.66667 15.1542 6.66667 15.3843V15.5098C6.66667 15.6771 6.60392 15.8235 6.47843 15.949L5.9451 16.5765C6.23791 16.6601 6.46275 16.8013 6.61961 17C6.77647 17.1987 6.8549 17.4235 6.8549 17.6745C6.8549 18.051 6.72941 18.3647 6.47843 18.6157C6.20654 18.9085 5.81961 19.0549 5.31765 19.0549C4.89935 19.0549 4.52288 18.9608 4.18824 18.7725C4.02092 18.6261 4 18.4588 4.12549 18.2706L4.28235 17.9569ZM8.4549 8.13725C8.3085 8.13725 8.18824 8.0902 8.09412 7.99608C8 7.90196 7.95294 7.7817 7.95294 7.63529V6.38039C7.95294 6.23399 8 6.11373 8.09412 6.01961C8.18824 5.92549 8.3085 5.87843 8.4549 5.87843H19.498C19.6444 5.87843 19.7647 5.92549 19.8588 6.01961C19.9529 6.11373 20 6.23399 20 6.38039V7.63529C20 7.7817 19.9529 7.90196 19.8588 7.99608C19.7647 8.0902 19.6444 8.13725 19.498 8.13725H8.4549ZM8.4549 13.1569C8.3085 13.1569 8.18824 13.1098 8.09412 13.0157C8 12.9216 7.95294 12.8013 7.95294 12.6549V11.4C7.95294 11.2536 8 11.1333 8.09412 11.0392C8.18824 10.9451 8.3085 10.898 8.4549 10.898H19.498C19.6444 10.898 19.7647 10.9451 19.8588 11.0392C19.9529 11.1333 20 11.2536 20 11.4V12.6549C20 12.8013 19.9529 12.9216 19.8588 13.0157C19.7647 13.1098 19.6444 13.1569 19.498 13.1569H8.4549ZM8.4549 18.1765C8.3085 18.1765 8.18824 18.1294 8.09412 18.0353C8 17.9412 7.95294 17.8209 7.95294 17.6745V16.4196C7.95294 16.2732 8 16.1529 8.09412 16.0588C8.18824 15.9647 8.3085 15.9176 8.4549 15.9176H19.498C19.6444 15.9176 19.7647 15.9647 19.8588 16.0588C19.9529 16.1529 20 16.2732 20 16.4196V17.6745C20 17.8209 19.9529 17.9412 19.8588 18.0353C19.7647 18.1294 19.6444 18.1765 19.498 18.1765H8.4549Z" />
        );

      default:
    }
  }

  render() {
    const { listType } = this.props;
    const label = listType.replace(/-/g, ' ');
    return (
      <ActionsMenu.IconButton
        ariaLabel={label}
        pathNode={this.getPathNode(listType)}
        viewBox="0 0 24 24"
        {...this.props}
      />
    );
  }
}

ListIcon.propTypes = {
  listType: PropTypes.oneOf([
    LIST_STYLE_TYPE.UNORDERED_LIST_ITEM,
    LIST_STYLE_TYPE.ORDERED_LIST_ITEM,
  ]),
};
