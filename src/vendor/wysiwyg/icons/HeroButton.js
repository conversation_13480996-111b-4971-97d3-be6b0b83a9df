import React from 'react';

import { colors } from '../helpers/styles/editor';
import BlockButton from './BlockButton';

export default class HeroButton extends React.PureComponent {
  render() {
    return (
      <BlockButton
        ariaLabel="hero image"
        pathNode={
          <path
            fillRule="evenodd"
            clipRule="evenodd"
            d="M18.75 12.4545H5.25V15.8636H18.75V12.4545ZM4 12.4545V7.90909V7.60606C4 7.27134 4.29848 7 4.66667 7H19.3333C19.7015 7 20 7.27134 20 7.60606V7.90909V12.4545V16.3939C20 16.7287 19.7015 17 19.3333 17H4.66667C4.29848 17 4 16.7287 4 16.3939V12.4545Z"
            fill={colors.apc_purple}
          />
        }
        viewBox="0 0 24 24"
        {...this.props}
      />
    );
  }
}
