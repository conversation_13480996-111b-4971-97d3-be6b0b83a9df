import React from 'react';
import PropTypes from 'prop-types';

export default class ConfirmButton extends React.Component {
  render() {
    const {
      title,
      pathNode,
      viewBox,
      text,
      isHovering,
      onMouseOver,
      onMouseOut,
      onClick,
      ariaLabel,
    } = this.props;

    const baseIconStyle = {
      display: 'inline-block',
      width: 24,
      height: 24,
      strokeWidth: 0,
      stroke: 'currentColor',
      fill: 'currentColor',
      transition: 'background-color 0.15s ease-in',
    };
    const wrapperStyle = {
      zIndex: 10,
      alignItems: 'center',
      cursor: 'pointer',
    };

    const iconWrapperStyle = {
      display: 'flex',
      justifyContent: 'center',
      transition: 'all 0.15s ease-out',
      marginRight: 6,
      borderRadius: isHovering ? '50%' : '0%',
      boxShadow: isHovering ? 'inset 0px 20px 1px 17px #eff0ff' : 'none',
    };

    const textStyle = {
      fontSize: 'var(--regular)',
      fontWeight: 'normal',
      color: '#394455',
      display: 'flex',
      justifyContent: 'center',
    };

    return (
      <span
        style={wrapperStyle}
        onClick={onClick}
        onMouseOver={onMouseOver}
        onMouseOut={onMouseOut}
        aria-label={ariaLabel}
      >
        <span style={iconWrapperStyle}>
          <svg style={baseIconStyle} viewBox={viewBox}>
            <title>{title}</title>
            {pathNode}
          </svg>
        </span>
        <span style={textStyle}>{text}</span>
      </span>
    );
  }
}

ConfirmButton.propTypes = {
  title: PropTypes.string,
  ariaLabel: PropTypes.string,
  pathNode: PropTypes.node,
  viewBox: PropTypes.string,
  text: PropTypes.string,
  isHovering: PropTypes.bool,
  onClick: PropTypes.func,
  onMouseOver: PropTypes.func,
  onMouseOut: PropTypes.func,
};
