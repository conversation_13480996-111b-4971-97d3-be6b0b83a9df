import React from 'react';

import IconButton from './IconButton';

export default class ImageButton extends React.PureComponent {
  render() {
    return (
      <IconButton
        data-testid="image-button"
        ariaLabel="image"
        pathNode={
          <path d="M18.5 6C18.9167 6 19.2708 6.14583 19.5625 6.4375C19.8542 6.72917 20 7.08333 20 7.5V16.5C20 16.9167 19.8542 17.2708 19.5625 17.5625C19.2708 17.8542 18.9167 18 18.5 18H5.5C5.08333 18 4.72917 17.8542 4.4375 17.5625C4.14583 17.2708 4 16.9167 4 16.5V7.5C4 7.08333 4.14583 6.72917 4.4375 6.4375C4.72917 6.14583 5.08333 6 5.5 6H18.5ZM18.3125 16.5C18.3542 16.5 18.3958 16.4792 18.4375 16.4375C18.4792 16.3958 18.5 16.3542 18.5 16.3125V7.6875C18.5 7.64583 18.4792 7.60417 18.4375 7.5625C18.3958 7.52083 18.3542 7.5 18.3125 7.5H5.6875C5.64583 7.5 5.60417 7.52083 5.5625 7.5625C5.52083 7.60417 5.5 7.64583 5.5 7.6875V16.3125C5.5 16.3542 5.52083 16.3958 5.5625 16.4375C5.60417 16.4792 5.64583 16.5 5.6875 16.5H18.3125ZM8 8.75C8.35417 8.75 8.65104 8.86979 8.89062 9.10938C9.13021 9.34896 9.25 9.64583 9.25 10C9.25 10.3542 9.13021 10.651 8.89062 10.8906C8.65104 11.1302 8.35417 11.25 8 11.25C7.64583 11.25 7.34896 11.1302 7.10938 10.8906C6.86979 10.651 6.75 10.3542 6.75 10C6.75 9.64583 6.86979 9.34896 7.10938 9.10938C7.34896 8.86979 7.64583 8.75 8 8.75ZM7 15V13.5L8.25 12.25C8.3125 12.1875 8.39583 12.1562 8.5 12.1562C8.60417 12.1562 8.6875 12.1875 8.75 12.25L10 13.5L13.75 9.75C13.8125 9.6875 13.8958 9.65625 14 9.65625C14.1042 9.65625 14.1875 9.6875 14.25 9.75L17 12.5V15H7Z" />
        }
        viewBox="0 0 24 24"
        {...this.props}
      />
    );
  }
}
