import React from 'react';

import { colors } from '../helpers/styles/editor';
import BlockButton from './BlockButton';

export default class FormSmallInput extends React.PureComponent {
  render() {
    return (
      <BlockButton
        ariaLabel="small input"
        pathNode={
          <path
            fillRule="evenodd"
            clipRule="evenodd"
            d="M17.8375 6.06932C17.7449 5.97685 17.5949 5.9769 17.5024 6.06943L10.9311 12.6407C10.8867 12.6852 10.8617 12.7455 10.8617 12.8083V14.9C10.8617 15.0309 10.9678 15.137 11.0987 15.137H13.1904C13.2532 15.137 13.3135 15.112 13.358 15.0676L19.9306 8.495C19.975 8.45053 20 8.39021 20 8.32732C20 8.26443 19.975 8.20412 19.9305 8.15968L17.8375 6.06932ZM4.17357 9.06714C4.2847 8.95601 4.43543 8.89357 4.59259 8.89357H9.46623C9.79351 8.89357 10.0588 9.15889 10.0588 9.48617C10.0588 9.81345 9.79351 10.0788 9.46623 10.0788H5.18518V16.7239H16.9964V14.9013C16.9964 14.574 17.2617 14.3087 17.589 14.3087C17.9162 14.3087 18.1815 14.574 18.1815 14.9013V17.3165C18.1815 17.6437 17.9162 17.9091 17.589 17.9091H4.59259C4.26531 17.9091 4 17.6437 4 17.3165V9.48617C4 9.329 4.06243 9.17827 4.17357 9.06714Z"
            fill={colors.apc_purple}
          />
        }
        viewBox="0 0 24 24"
        {...this.props}
      />
    );
  }
}
