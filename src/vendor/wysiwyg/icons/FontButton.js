import React from 'react';
import IconButton from './IconButton';

export const FontButton = props => (
  <IconButton
    ariaLabel="font"
    pathNode={
      <g>
        <text
          x="18"
          y="25"
          fontSize="24px"
          fontFamily="'Times New Roman', Times, serif"
        >
          T
        </text>
      </g>
    }
    viewBox="0 0 52 32"
    iconStyle={{
      width: 52,
      height: 32,
    }}
    textStyleButton
    {...props}
  />
);
