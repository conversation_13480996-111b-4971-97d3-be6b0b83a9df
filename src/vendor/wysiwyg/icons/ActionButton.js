import React from 'react';

import IconButton from './IconButton';

export default class ActionButton extends React.PureComponent {
  render() {
    return (
      <IconButton
        ariaLabel="actions button"
        pathNode={
          <path d="M17.876 4.15354C18.2722 3.96588 18.6632 3.95024 19.0489 4.10662C19.4347 4.26301 19.7109 4.53928 19.8777 4.93545C20.0445 5.33162 20.0341 5.72779 19.8465 6.12396L13.8727 19.135C13.6433 19.5937 13.2992 19.87 12.8405 19.9638C12.3818 20.0576 11.9596 19.969 11.5738 19.698C11.1881 19.4269 10.9952 19.0307 10.9952 18.5095V13.0048H5.49055C4.96927 13.0048 4.5731 12.8119 4.30204 12.4262C4.03098 12.0404 3.94236 11.6182 4.03619 11.1595C4.13002 10.7008 4.4063 10.3567 4.86502 10.1273L17.876 4.15354Z" />
        }
        viewBox="0 0 24 24"
        {...this.props}
      />
    );
  }
}
