import React from 'react';

import { colors } from '../helpers/styles/editor';
import BlockButton from './BlockButton';

const FormMultiSelectButton = props => {
  return (
    <BlockButton
      ariaLabel="radio selects"
      pathNode={
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M17.7143 4C18.975 4 20 5.0223 20 6.28368V17.7021C20 18.9616 18.975 19.9857 17.7143 19.9857H6.28571C5.02321 19.9857 4 18.9616 4 17.7021V6.28368C4 5.0223 5.02321 4 6.28571 4H17.7143ZM16.1357 10.4157C16.525 10.0268 16.525 9.39162 16.1357 9.00268C15.7464 8.61374 15.1107 8.61374 14.7214 9.00268L10.8571 12.8635L9.27857 11.2864C8.88929 10.8974 8.25357 10.8974 7.86429 11.2864C7.47393 11.6753 7.47393 12.3104 7.86429 12.6994L10.15 14.9831C10.5393 15.372 11.175 15.372 11.5643 14.9831L16.1357 10.4157Z"
          fill={colors.apc_purple}
        />
      }
      viewBox="0 0 24 24"
      {...props}
    />
  );
};

export default FormMultiSelectButton;
