import React from 'react';

import IconButton from './IconButton';

export default class AdvancedStylingButton extends React.PureComponent {
  render() {
    return (
      <IconButton
        ariaLabel="advanced styling button"
        pathNode={
          <path d="M10.3773 4.16663C12.0436 3.83337 13.6369 4 15.1574 4.66651C16.5946 5.27053 17.7609 6.23385 18.6566 7.55645C19.5522 8.87905 20 10.3527 20 11.9773C20 12.5396 19.8021 13.0187 19.4064 13.4144C19.0107 13.8102 18.5316 14.008 17.9692 14.008H15.4698C14.9699 14.008 14.5273 14.1643 14.142 14.4767C13.7567 14.7891 13.5067 15.1849 13.3922 15.6639C13.2776 16.143 13.3453 16.6116 13.5953 17.0698C13.7827 17.4656 13.8452 17.8717 13.7827 18.2883C13.7202 18.7049 13.5536 19.0693 13.2828 19.3818C13.0121 19.6942 12.6684 19.8817 12.2518 19.9441C11.2729 20.0899 10.2419 19.9494 9.15882 19.5224C8.07574 19.0954 7.11763 18.4445 6.2845 17.5697C5.38888 16.6533 4.75361 15.6014 4.3787 14.4142C3.96213 13.102 3.88923 11.7482 4.16 10.3527C4.47243 8.81136 5.20142 7.47314 6.34698 6.33799C7.49254 5.20284 8.83598 4.47905 10.3773 4.16663ZM7.00308 14.008C7.27385 14.008 7.50817 13.9091 7.70604 13.7112C7.90391 13.5134 8.00284 13.2791 8.00284 13.0083C8.00284 12.7375 7.90391 12.5032 7.70604 12.3053C7.50817 12.1075 7.27385 12.0085 7.00308 12.0085C6.73231 12.0085 6.49799 12.1075 6.30012 12.3053C6.10225 12.5032 6.00331 12.7375 6.00331 13.0083C6.00331 13.2791 6.10225 13.5134 6.30012 13.7112C6.49799 13.9091 6.73231 14.008 7.00308 14.008ZM8.00284 10.009C8.27361 10.009 8.50793 9.91006 8.7058 9.71219C8.90367 9.51432 9.0026 9.28 9.0026 9.00923C9.0026 8.73846 8.90367 8.50414 8.7058 8.30627C8.50793 8.1084 8.27361 8.00947 8.00284 8.00947C7.73207 8.00947 7.49775 8.1084 7.29988 8.30627C7.10201 8.50414 7.00308 8.73846 7.00308 9.00923C7.00308 9.28 7.10201 9.51432 7.29988 9.71219C7.49775 9.91006 7.73207 10.009 8.00284 10.009ZM12.0019 8.00947C12.2727 8.00947 12.507 7.91053 12.7049 7.71266C12.9027 7.51479 13.0017 7.28047 13.0017 7.0097C13.0017 6.73893 12.9027 6.50462 12.7049 6.30675C12.507 6.10888 12.2727 6.00994 12.0019 6.00994C11.7311 6.00994 11.4968 6.10888 11.2989 6.30675C11.1011 6.50462 11.0021 6.73893 11.0021 7.0097C11.0021 7.28047 11.1011 7.51479 11.2989 7.71266C11.4968 7.91053 11.7311 8.00947 12.0019 8.00947ZM16.0009 10.009C16.2717 10.009 16.506 9.91006 16.7039 9.71219C16.9018 9.51432 17.0007 9.28 17.0007 9.00923C17.0007 8.73846 16.9018 8.50414 16.7039 8.30627C16.506 8.1084 16.2717 8.00947 16.0009 8.00947C15.7302 8.00947 15.4959 8.1084 15.298 8.30627C15.1001 8.50414 15.0012 8.73846 15.0012 9.00923C15.0012 9.28 15.1001 9.51432 15.298 9.71219C15.4959 9.91006 15.7302 10.009 16.0009 10.009Z" />
        }
        viewBox="0 0 24 24"
        {...this.props}
      />
    );
  }
}
