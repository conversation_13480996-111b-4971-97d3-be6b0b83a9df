import React from 'react';
import PropTypes from 'prop-types';

export default class Block<PERSON>utton extends React.Component {
  render() {
    const { title, pathNode, viewBox, text, isHovering } = this.props;

    const baseIconStyle = {
      display: 'inline-block',
      width: 32,
      height: 32,
      strokeWidth: 0,
      stroke: 'currentColor',
      fill: 'currentColor',
      transition: 'background-color 0.15s ease-in',
    };

    const wrapperStyle = {
      zIndex: 10,
      alignItems: 'center',
    };

    const iconWrapperStyle = {
      display: 'flex',
      justifyContent: 'center',
      transition: 'all 0.3s ease-out',
      marginTop: '20px',
      height: 36,
      width: 36,
      marginLeft: 'auto',
      marginRight: 'auto',
      alignItems: 'center',
      borderRadius: '50%',
      boxShadow: isHovering
        ? 'inset 0px 20px 1px 17px #eff0ff'
        : 'inset 0px 20px 1px 17px transparent',
    };

    const textStyle = {
      fontSize: 'var(--regular)',
      fontWeight: 'normal',
      color: '#394455',
      display: 'flex',
      justifyContent: 'center',
      marginTop: '5px',
    };

    return (
      <span style={wrapperStyle}>
        <span style={iconWrapperStyle}>
          <svg style={baseIconStyle} viewBox={viewBox}>
            <title>{title}</title>
            {pathNode}
          </svg>
        </span>
        <span style={textStyle}>{text}</span>
      </span>
    );
  }
}

BlockButton.propTypes = {
  title: PropTypes.string,
  pathNode: PropTypes.node,
  viewBox: PropTypes.string,
  text: PropTypes.string,
  isHovering: PropTypes.bool,
};
