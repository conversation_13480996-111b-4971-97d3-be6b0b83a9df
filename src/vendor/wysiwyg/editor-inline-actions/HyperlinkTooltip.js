import React, { createRef } from 'react';
import PropTypes from 'prop-types';
import { Map } from 'immutable';

import { getEditorDocument } from 'ext/lib/document';
import HyperlinkInline from '../editor-actions/HyperlinkInline';
import { removeLinkEntityFromEditorState } from '../helpers/draft/entities';

export default class HyperlinkTooltip extends React.Component {
  tooltip = createRef();

  componentDidMount() {
    const $document = getEditorDocument();
    $document.addEventListener('click', this.onClickOut, true);
  }

  componentWillUnmount() {
    const $document = getEditorDocument();
    $document.removeEventListener('click', this.onClickOut, true);
  }

  onClickOut = e => {
    const { onToggleActive, node } = this.props;

    const { current: $tooltip } = this.tooltip;

    // Ignore clicks that occur on the link or tooltip.
    if (
      ($tooltip && $tooltip.contains(e.target)) ||
      (node && node.contains(e.target))
    ) {
      return;
    }

    onToggleActive(false);
  };

  onClickUnlink = () => {
    const {
      onToggleEditorAction,
      onToggleActive,
      entityKey,
      localState,
      persistedState,
      onChange,
    } = this.props;

    // Update the editor state with content state with the link entity removed.
    const newEditorState = removeLinkEntityFromEditorState(
      entityKey,
      localState.get('editorState')
    );
    onChange({
      localState: localState.set('editorState', newEditorState),
      persistedState,
    });

    // Remove tooltip and toolbar link box if it's there.
    onToggleEditorAction(HyperlinkInline.actionName, false);
    onToggleActive(false);
  };

  onClickEdit = () => {
    const { onToggleEditorAction, onToggleActive } = this.props;

    // Show the toolbar's edit link box.
    onToggleEditorAction(HyperlinkInline.actionName, true);
    onToggleActive(false);
  };

  render() {
    const { top, left, href } = this.props;

    const containerStyle = {
      position: 'fixed',
      top: `calc(${top}px + 1.8em)`,
      left,
      backgroundColor: '#333',
      color: '#ddd',
      whiteSpace: 'nowrap',
      padding: '3px 10px',
      borderRadius: '3px',
      userSelect: 'none',
      cursor: 'default',
      fontSize: '0.9em',
      display: 'flex',
      zIndex: 101,
    };
    const arrowStyle = {
      ...containerStyle,
      position: 'absolute',
      top: '-3px',
      left: '5px',
      width: '10px',
      height: '10px',
      transform: 'rotate(45deg)',
      borderRadius: '2px',
      padding: 0,
    };
    const separatorStyle = {
      opacity: 0.5,
      padding: '0 6px',
    };

    return (
      <div ref={this.tooltip} style={containerStyle}>
        <div style={arrowStyle} />
        <span
          style={{
            maxWidth: '150px',
            textOverflow: 'ellipsis',
            display: 'inline-block',
            overflow: 'hidden',
          }}
        >
          {href}
        </span>
        <span style={separatorStyle}>|</span>
        <span style={{ cursor: 'pointer' }} onClick={this.onClickEdit}>
          Edit
        </span>
        <span style={separatorStyle}>|</span>
        <span style={{ cursor: 'pointer' }} onClick={this.onClickUnlink}>
          Unlink
        </span>
      </div>
    );
  }
}

HyperlinkTooltip.propTypes = {
  localState: PropTypes.instanceOf(Map),
  persistedState: PropTypes.instanceOf(Map),
  onChange: PropTypes.func,
  onToggleActive: PropTypes.func,
  onToggleEditorAction: PropTypes.func,
  // NOTE: Similar to other instances where the type should be e.g. HTMLElement,
  //       the reference to HTMLElement will differ at runtime due to the
  //       component being rendered in a Shadow root via an injected script.
  node: PropTypes.object,
  top: PropTypes.number,
  left: PropTypes.number,
  entityKey: PropTypes.string,
  href: PropTypes.string,
};

HyperlinkTooltip.actionName = 'inline-hyperlink-tooltip';
