import { combineReducers } from 'redux';
import { allstate, composeReducers } from 'ext/lib/collections';

/**
 * NOTE: these imports do NOT go through the index.js for each entity
 *
 * THIS IS INTENTIONAL. The reducers are not a public API, unlike
 * actions and selectors which ARE appropriate to import in components.
 */

import account from 'ext/entities/account/reducer';
import accounts from 'ext/entities/accounts/reducer';
import status from 'ext/entities/status/reducer';
import flowsSummary from 'ext/entities/flows-summary/reducer';
import userProperties from 'ext/entities/user-properties/reducer';
import permissions from 'ext/entities/permissions/reducer';
import entitlements from 'ext/entities/entitlements/reducer';
import themes from 'ext/entities/themes/reducer';

import application from 'entities/application/reducer';
import flow from 'entities/flows/flow-reducer';
import stepGroups from 'entities/step-groups/reducer';
import stepChildren from 'entities/step-children/reducer';
import defaultContent from 'entities/step-children/default-content';
import mode from 'entities/mode/reducer';
import selected from 'entities/selected/reducer';
import tags from 'entities/tags/reducer';
import templates from 'entities/templates/reducer';
import stylesheets from 'entities/stylesheets/reducer';
import usageProperties from 'entities/usage-properties/reducer';
import layouts from 'entities/layouts/reducer';

import preview from 'lib/preview-mode/reducer';

const identity = (state = null) => state;

const combined = combineReducers({
  application,
  account,
  flow,
  stepGroups,
  stepChildren,
  defaultContent,
  flowsSummary,
  mode,
  tags,
  templates,
  themes,
  stylesheets,
  usageProperties,
  userProperties,
  status,
  preview,
  accounts,
  permissions,
  layouts,
  entitlements,
  // NOTE: Placeholder reducers so that `combineReducers` doesn't complain about
  //       the missing keys when the `allstate` enhancer is applied
  selected: identity,
});

export default composeReducers(combined, allstate(selected, 'selected'));
