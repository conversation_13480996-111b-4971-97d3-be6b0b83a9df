/* globals STANDALONE */

import { applyMiddleware, createStore } from 'redux';
import createSagaMiddleware from 'redux-saga';
import { create as createLoggerClient } from 'ext/lib/loggers';
import getReduxConfig from 'ext/lib/redux-config';
import apiClientLib from 'ext/lib/api/client';
import { create as createApiClient } from 'lib/api-client';
import { create as createCrxClient } from 'lib/crx-client';

export const createApiContext = async ({ jwt, accountId }) => {
  return STANDALONE || !accountId
    ? apiClientLib(jwt).getEndpoints()
    : createApiClient({ accountId, jwt });
};

export default async function configureStore({
  port,
  data,
  initialState,
  rootReducer,
  rootSaga,
}) {
  const { accountId, jwt, gates } = data;
  const { composeEnhancers, sagaMonitor, logStateChange } = getReduxConfig();

  const apiClient = await createApiContext({ accountId, jwt });

  const sagaMiddleware = createSagaMiddleware({
    sagaMonitor,
    context: {
      api: apiClient,
      crx: createCrxClient(port),
      logger: createLoggerClient(initialState),
      gates,
    },
  });

  const middlewares = [sagaMiddleware];

  const store = createStore(
    rootReducer,
    initialState,
    composeEnhancers(applyMiddleware(...middlewares))
  );

  const rootTask = sagaMiddleware.run(rootSaga);
  logStateChange(store, 'Flow builder');

  return { store, rootTask, apiClient };
}
