/* globals NEW_EXTENSION DEV, CUSTOMER_API_URL */

import React from 'react';
import { render } from 'react-dom';
import PropTypes from 'prop-types';
import { Provider } from 'react-redux';
import { DndProvider } from 'react-dnd';
import { HTML5Backend } from 'react-dnd-html5-backend';
import { ImageryProvider } from '@appcues/imagery-kit';

/** DEVTOOLS */
import {
  OverridesContext,
  enhance,
  shape as overridesShape,
} from 'ext/lib/devtools';

/** STYLES */
import StyleProvider from 'ext/components/StyleProvider';

/** CONTEXTS */
import GatesContext, { shape as gatesShape } from 'ext/lib/gates';
import AnalyticsContext, { createAnalyticsClient } from 'ext/lib/analytics';
import { RootProvider } from 'ext/components/RootContext';

import { initialize, loadSDK } from 'ext/root/root-actions';
import { getStandAloneState } from 'ext/lib/get-initial-state';
import {
  attemptInteraction,
  FETCH_ACCOUNTS,
  FETCH_FLOWS_SUMMARY,
  FETCH_USER_PROPERTIES,
  FETCH_BOOTSTRAP,
} from 'ext/lib/track';
import { setBuilder } from 'ext/lib/open-builder';
import { FETCH_LAYOUTS } from 'lib/metrics/interaction-types';

/** COMPONENTS */
import App from 'components/App';

/** STORE */
import configureStore, { createApiContext } from './configure-store';
import rootReducer from './root-reducer';
import rootSaga from './root-saga';

function Root({ store, target, analyticsClient, data, apiClient }) {
  const { jwt, accountId, gates = {}, overrides = {} } = data;

  return (
    <Provider store={store}>
      <AnalyticsContext.Provider value={analyticsClient}>
        <OverridesContext.Provider value={overrides}>
          <GatesContext.Provider value={gates}>
            <StyleProvider target={target}>
              <DndProvider
                backend={HTML5Backend}
                options={{ rootElement: target }}
              >
                <ImageryProvider
                  jwt={jwt}
                  accountId={accountId}
                  customerApiURL={CUSTOMER_API_URL}
                  fetchImagesApiOverride={apiClient.fetchImages}
                  uploadImageApiOverride={apiClient.uploadImage}
                >
                  <RootProvider value={target}>
                    <App />
                  </RootProvider>
                </ImageryProvider>
              </DndProvider>
            </StyleProvider>
          </GatesContext.Provider>
        </OverridesContext.Provider>
      </AnalyticsContext.Provider>
    </Provider>
  );
}

Root.propTypes = {
  store: PropTypes.shape({
    dispatch: PropTypes.func,
    getState: PropTypes.func,
  }),
  target: PropTypes.instanceOf(ShadowRoot).isRequired,
  analyticsClient: PropTypes.shape({
    track: PropTypes.func,
  }),
  data: PropTypes.shape({
    jwt: PropTypes.string,
    accountId: PropTypes.string,
    gates: gatesShape,
    overrides: overridesShape,
  }),
  // eslint-disable-next-line react/forbid-prop-types
  apiClient: PropTypes.object,
};

function createInitialState(data) {
  const {
    accountId,
    email,
    isSpoofing,
    persisted,
    userId,
    stylesheets = {},
    version,
    hidePermissionModal = true,
    usageProperties,
  } = data;

  const isNavigating = persisted?.flow?.previewUrl !== window.location.href;

  return {
    // NOTE: We're allowing persisted.mode to potentially overwrite mode here since
    // we may have mode persisted in state and dont want to enter welcome mode
    // in that case
    mode: 'edit',
    ...persisted,
    ...(persisted?.preview?.content
      ? {
          preview: {
            ...persisted.preview,
            continued: isNavigating,
          },
        }
      : {}),
    account: {
      accountId,
      email,
      isSpoofing,
      userId,
      version,
    },
    stylesheets,
    permissions: {
      hidePermissionModal,
    },
    usageProperties,
  };
}

export const inject = async ($host, $target, $container) => {
  /**
   * Start the application
   *
   * @param {Port} port - Connected port
   * @param {object} payload - Initial CRX message data
   * @returns {void}
   */
  const start = async port => {
    let token = $container.getAttribute('auth-token');
    let flowId = $container.getAttribute('flowId');

    if (DEV) {
      if (!token) {
        /** AUTH */
        // eslint-disable-next-line global-require
        const { login } = require('lib/auth');

        const user = await login();
        token = user.jwt;
        $container.setAttribute('auth-token', token);
      }

      if (!flowId) {
        // eslint-disable-next-line global-require, import/no-unresolved
        const bundlerSettings = require('standalone-settings');
        flowId = bundlerSettings.flowId;
      }
    }

    setBuilder('flow');

    const initialData = await getStandAloneState({
      token,
    });

    // Enhance the CRX payload with any devtools related overrides
    const data = enhance({
      ...initialData,
      persisted: {
        selected: {
          flow: flowId ?? null,
        },
      },
    });

    const { store, rootTask, apiClient } = await configureStore({
      port,
      data,
      initialState: createInitialState(data),
      rootReducer,
      rootSaga,
    });

    store.dispatch(initialize());

    if (!NEW_EXTENSION && data.builderType) store.dispatch(loadSDK());

    store.dispatch(attemptInteraction(FETCH_ACCOUNTS));
    store.dispatch(attemptInteraction(FETCH_BOOTSTRAP));
    store.dispatch(attemptInteraction(FETCH_FLOWS_SUMMARY));
    store.dispatch(attemptInteraction(FETCH_LAYOUTS));
    store.dispatch(attemptInteraction(FETCH_USER_PROPERTIES));

    const analyticsClient = createAnalyticsClient(store.getState(), {
      app: 'Flow Builder',
    });

    // Adds a listener to the container to observer the attributes change
    // and update the api context when the token changes
    $container.addEventListener(
      'builder:change',
      async ({ detail: { name, newValue } }) => {
        switch (name) {
          case 'auth-token': {
            const api = await createApiContext({ jwt: newValue });
            rootTask.setContext({
              api,
            });
            break;
          }

          default:
          //
        }
      }
    );

    render(
      <Root
        store={store}
        target={$target}
        data={data}
        analyticsClient={analyticsClient}
        apiClient={apiClient}
      />,
      $host
    );
  };

  start();
};
