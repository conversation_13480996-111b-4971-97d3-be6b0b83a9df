/* globals NEW_EXTENSION STANDALONE */
import { fork, delay, put, setContext } from 'redux-saga/effects';
import isEmpty from 'lodash.isempty';

/**
 * NOTE: these imports do NOT go through the index.js for each entity
 *
 * THIS IS INTENTIONAL. The sagas are not a public API, unlike
 * actions and selectors which ARE appropriate to import in components.
 */
import accountsSaga from 'ext/entities/accounts/saga';
import { injectSDK } from 'ext/root/root-actions';
import trackSaga from 'ext/lib/track/saga';
import flowsSummarySaga from 'ext/entities/flows-summary/saga';
import userPropertiesSaga from 'ext/entities/user-properties/saga';
import bootstrapSaga from 'ext/entities/bootstrap/saga';

import entitlementsSaga from 'ext/entities/entitlements/saga';
import flowSaga from 'entities/flows/flow-saga';
import stepGroupsSaga from 'entities/step-groups/saga';
import stepChildrenSaga from 'entities/step-children/saga';
import usagePropertiesSaga from 'entities/usage-properties/saga';
import templatesSaga from 'entities/templates/saga';
import layoutSaga from 'entities/layouts/saga';
import crxSaga from 'lib/crx/saga';
import convertButtonsSaga from 'lib/convert-buttons/saga';

import previewModeSaga from 'lib/preview-mode/saga';
import navigateModeSaga from 'lib/navigation-mode/saga';

export default function* root() {
  yield fork(bootstrapSaga);
  yield fork(flowSaga);
  yield fork(flowsSummarySaga);
  yield fork(stepGroupsSaga);
  yield fork(stepChildrenSaga);
  yield fork(usagePropertiesSaga);
  yield fork(userPropertiesSaga);
  yield fork(templatesSaga);
  yield fork(convertButtonsSaga);
  yield fork(entitlementsSaga);
  yield fork(previewModeSaga);
  yield fork(navigateModeSaga);

  yield fork(accountsSaga);
  yield fork(trackSaga);
  yield fork(layoutSaga);

  if (!STANDALONE) {
    yield fork(crxSaga);
  }

  /**
   * This block MUST occur in the root due to the way saga-context works
   * If this moves to a child process, other processes won't be able to access it!
   */
  if (NEW_EXTENSION || STANDALONE) {
    // eslint-disable-next-line global-require
    const appcues = require('@appcues/javascript-sdk/dist/appcues.injectable');
    yield setContext({ sdk: appcues });
  } else {
    while (typeof window.Appcues === 'undefined' || isEmpty(window.Appcues)) {
      yield delay(200);
    }
    yield setContext({ sdk: window.Appcues });
  }
  yield put(injectSDK());
}
