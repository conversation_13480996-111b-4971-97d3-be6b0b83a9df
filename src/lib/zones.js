import { v4 as uuid } from 'uuid';

export function updateSurveyZoneIds(rows) {
  const surveyZoneTypes = new Set([
    'SelectionField',
    'TextAreaInput',
    'TextInput',
    'Rating',
  ]);

  return rows.map(row => {
    if (!row.zones) return row;

    const zones = row.zones.map(zone => {
      if (surveyZoneTypes.has(zone.type)) {
        return {
          ...zone,
          id: uuid(),
        };
      }

      return zone;
    });

    return {
      ...row,
      zones,
    };
  });
}
