import { call, getContext, select, takeEvery } from 'redux-saga/effects';
import { INITIALIZE } from 'ext/root/root-actions';
import { selectMode } from 'entities/mode';

/**
 * If the builder has been initialized in `navigate` mode, assume the user
 * is currently in the middle of navigating and continue to persist the
 * state to the CRX since the CRX clears out the persisted state once it has
 * been used to re-hydrate the builder.
 */
function* resume() {
  try {
    const mode = yield select(selectMode);
    if (mode !== 'navigate') {
      return;
    }

    const crx = yield getContext('crx');
    const state = yield select();
    yield call(crx.persist, state);
  } catch {
    // TODO: invariant?
  }
}

export default function* navigate() {
  yield takeEvery(INITIALIZE, resume);
}
