import { put, select, take } from 'redux-saga/effects';
import { compareLocations } from 'ext/lib/location';
import { changeLocation } from 'ext/root/root-actions';
import { selectPreviewUrl } from './selectors';

export function* ensureLocation(id = '') {
  const previewUrl = yield select(selectPreviewUrl, id);
  const { origin } = new URL(window.document.baseURI);
  const { href: destination } = new URL(previewUrl, origin);

  const similar = compareLocations(window.location.href, destination);

  if (!similar) {
    // allow sagas to hook into this change
    yield put(changeLocation());

    // change the location to the actual preview URL
    window.location.assign(destination);

    // BLOCK so that no other code in the caller runs
    yield take('ALL BLOCKED UP');
  }
}
