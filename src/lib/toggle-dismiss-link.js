import { v4 as uuid } from 'uuid';
import { createDismissLinkZone } from 'entities/step-children';
import { EDITOR_TYPES } from 'vendor/wysiwyg/helpers/constants';

/**
 *
 * @param  {object} child  The step child object
 * @return {Array}         An array of 2 elements where the first value is a
 *                         boolean indicating if anything changed and the second
 *                         value is the updated step child object.
 */
export const removeDismissLink = child => {
  const { rows: childRows } = child;
  let shouldUpdate = false;

  const rows = childRows.reduce((prevRows, row) => {
    const zones = row.zones.filter(it => {
      if (it.type === EDITOR_TYPES.DISMISS_LINK) {
        shouldUpdate = true;
        return false;
      }

      return true;
    });

    if (zones.length > 0) {
      return [...prevRows, { ...row, zones }];
    }

    return prevRows;
  }, []);

  return [shouldUpdate, { ...child, rows }];
};

/**
 *
 * @param  {object} child  The step child object
 * @return {Array}         An array of 2 elements where the first value is a
 *                         boolean indicating if anything changed and the second
 *                         value is the updated step child object.
 */
export const addDismissLink = child => {
  const { rows: childRows } = child;
  let rows = [...childRows];

  const dismissLinkZone = createDismissLinkZone({
    skipText: 'Hide these tips',
  });

  const newRow = {
    id: uuid(),
    zones: [dismissLinkZone],
  };

  if (rows.length === 0)
    return [
      true,
      {
        ...child,
        rows: [newRow],
      },
    ];

  const hasDismissLink = rows
    .flatMap(it => it.zones)
    .some(it => it.type === EDITOR_TYPES.DISMISS_LINK);

  // Each step child should have just one dismiss link
  if (hasDismissLink) return [false, child];

  const lastRowIndex = rows.length - 1;
  const { zones } = rows[lastRowIndex];
  const { type, persistedState } = zones[0];

  // if last row zone contains just one button
  // right aligned we should add the dismiss link
  // as the left zone in the same row
  // otherwise add a new row with the dismiss link
  const shouldInsertOnLastRow =
    zones.length === 1 &&
    type === 'Button' &&
    persistedState.textAlign === 'right';

  if (shouldInsertOnLastRow) {
    const customDismissLinkZone = createDismissLinkZone({
      skipText: 'Hide these tips',
    });

    const lastRow = {
      ...rows[lastRowIndex],
      zones: [customDismissLinkZone, ...zones],
    };

    rows = [...rows.slice(0, lastRowIndex), lastRow];
  } else {
    rows.push(newRow);
  }

  return [true, { ...child, rows }];
};
