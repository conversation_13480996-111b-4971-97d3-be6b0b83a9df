import {
  PREVIEW_REQUESTED,
  PREVIEW_CONTENT_LOADED,
  PREVIEW_STARTED,
  PREVIEW_EXITED,
  PREVIEW_COMPLETED,
  PREVIEW_RELOADED,
} from './actions';

export default function preview(state = {}, action) {
  switch (action.type) {
    case PREVIEW_REQUESTED:
      return { id: action.payload.id, step: action.payload.step };
    case PREVIEW_CONTENT_LOADED:
      return { ...state, content: action.payload };
    case PREVIEW_STARTED:
      return { ...state, done: false, continued: true };
    case PREVIEW_RELOADED:
      return { ...state, done: false, continued: false };
    case PREVIEW_COMPLETED:
      return { ...state, done: true, continued: false };
    case PREVIEW_EXITED:
      return {};
    default:
      return state;
  }
}

export const selectPreview = state => state.preview;
