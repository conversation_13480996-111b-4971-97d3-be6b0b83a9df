import { eventChannel } from 'redux-saga';
import {
  call,
  cancel,
  delay,
  fork,
  getContext,
  put,
  race,
  select,
  take,
  takeEvery,
} from 'redux-saga/effects';
import { getCurrentBuilder } from 'ext/lib/open-builder';
import { SDK_INJECTED } from 'ext/root/root-actions';
import { selectThemes, selectDefaultTheme } from 'ext/entities/themes';
import { selectSelected } from 'entities/selected';
import { ensureLocation } from 'lib/location';
import {
  PREVIEW_REQUESTED,
  PREVIEW_RELOADED,
  PREVIEW_EXITED,
  start,
  complete,
  load,
} from './actions';

const FLOW_STARTED = 'flow_started';
const FLOW_COMPLETED = 'flow_completed';
const FLOW_SKIPPED = 'flow_skipped';

/*
 * Translate SDK events into a channel that redux-saga can take
 *
 * @param {SDK} sdk - our SDK instance
 * @return {Channel} - a redux-saga channel
 */
function listenTo(sdk) {
  return eventChannel(emit => {
    sdk.on(FLOW_STARTED, emit);
    sdk.on(FLOW_COMPLETED, emit);
    sdk.on(FLOW_SKIPPED, emit);
    return () => {
      sdk.off(FLOW_STARTED, emit);
      sdk.off(FLOW_COMPLETED, emit);
      sdk.off(FLOW_SKIPPED, emit);
    };
  });
}

/*
 * listen for events from the SDK to update/control preview-mode
 */
function* initSDK() {
  const sdk = yield getContext('sdk');

  if (sdk && getCurrentBuilder() === 'flow') {
    const chan = yield call(listenTo, sdk);

    while (true) {
      const { id: sdkEvent } = yield take(chan);
      if (sdkEvent === FLOW_STARTED) {
        yield put(start());
      } else if (sdkEvent === FLOW_COMPLETED || sdkEvent === FLOW_SKIPPED) {
        yield put(complete());
      }
    }
  }
}

/**
 * periodically check if the window location has changed
 * and notify the SDK
 *
 * NOTE: this task must be cancelled by the caller!
 *
 * @param {SDK} sdk - instance of the injectable SDK
 */
function* watchForPageChanges(sdk) {
  let previous = '';

  while (true) {
    if (previous !== window.location.href) {
      previous = window.location.href;
      yield call([sdk, sdk.page]);
    }
    yield delay(300);
  }
}

/*
 * THE MEAT
 *
 * start the preview control handlers
 *
 * @param {Flow} flow - the SDK-flavored flow to preview
 * @param {string} stepId - optionally restrict the preview to a single step
 *                          needed for the ensureLocation call
 * @param {boolean} continued - TRUE iff we're resuming an in-progress preview
 *     after a page load, meaning the SDK already has the content and we should NOT re-inject;
 *     FALSE otherwise.
 *
 */
function* previewFlow(content, stepId = '', continued = false) {
  try {
    const sdk = yield getContext('sdk');

    const watcher = yield fork(watchForPageChanges, sdk);

    yield race([
      take(PREVIEW_EXITED),
      call(function* preview() {
        // we might be resuming after navigation, meaning we want to wait
        // for a RELOAD event before injecting content, since the SDK already
        // has the content loaded
        if (continued) {
          yield take(PREVIEW_RELOADED);
          yield call(ensureLocation, stepId);
        }
        while (true) {
          yield call([sdk, sdk.injectContent], content);
          yield take(PREVIEW_RELOADED);
          yield call(ensureLocation, stepId);
        }
      }),
    ]);
    // cleanup by removing the flow from the SDK
    yield cancel(watcher);
    yield call([sdk, sdk.reset]);
  } catch {
    // TODO uhhh???
  }
}

/*
 * fetch a preview of the flow from the end-user API
 *
 * @param {string} id - a FLOW id
 * @param {string} stepId - an _optional_ stepId, when previewing a single step-group
 *
 * @return {object} content - the flow to preview, formatted for SDK injestion
 */
function* prepareContent(id, stepId) {
  const api = yield getContext('api');
  const sdk = yield getContext('sdk');

  const flow = yield call(api.getFlowPreview, id);
  /**
   * TODO - eventually the API will provide an endpoint to get a flow
   *      filtered down to one step group; until then, we can manually filter
   */
  const content = stepId
    ? {
        ...flow,
        attributes: {
          ...flow.attributes,
          steps: {
            [stepId]: {
              ...flow.attributes.steps[stepId],
              index: 0,
            },
          },
        },
      }
    : flow;

  const defaultTheme = yield select(selectDefaultTheme);
  const themes = yield select(selectThemes);

  yield call([sdk, sdk.injectStyles], defaultTheme, themes);

  yield put(load(content));

  return content;
}

/*
 * setup preview-mode by navigating to the right URL.
 * if we're already there, start the preview immediately.
 *
 * @param {Action} PREVIEW_REQUESTED
 */
function* initPreviewMode({ payload: { id, step } }) {
  try {
    /*
     * if ensurelocation does navigate, the call is BLOCKING
     * so that the next call doesn't run
     */
    yield call(ensureLocation, step);
    const content = yield call(prepareContent, id, step);
    yield call(previewFlow, content, step);
  } catch {
    // TODO what happens if we cannot get the flow?
  }
}

/*
 * check for preview-mode data and invoke/resume preview-mode as needed
 * If we have content, navigation happened during preview and we can resume.
 *
 * If we have an id instead, we're just starting the preview after a corrective navigation
 * to get to the right starting URL
 *
 * If we have neither content nor an id, we are not in preview mode and should do nothing :)
 */
function* resumePreview() {
  const { id, step, content, continued } = yield select(state => state.preview);

  if (content) {
    yield call(previewFlow, content, step, continued);
  } else if (id) {
    const flow = yield call(prepareContent, id, step);
    yield call(previewFlow, flow, step);
  }
}

/*
 * go back to where we started when exiting preview-mode
 */
function* exitPreviewMode() {
  const { stepGroup } = yield select(selectSelected);
  yield call(ensureLocation, stepGroup);
}

export default function* previewMode() {
  yield take(SDK_INJECTED);
  yield fork(initSDK);
  yield takeEvery(PREVIEW_REQUESTED, initPreviewMode);
  yield takeEvery(PREVIEW_EXITED, exitPreviewMode);

  /* we may have already spun up in preview-mode, so check and resume */
  yield call(resumePreview);
}
