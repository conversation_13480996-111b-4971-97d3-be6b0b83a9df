export const PREVIEW_REQUESTED = 'PREVIEW_REQUESTED';
export const PREVIEW_RELOADED = 'PREVIEW_RELOADED';
export const PREVIEW_EXITED = 'PREVIEW_EXITED';
export const PREVIEW_STARTED = 'PREVIEW_STARTED';
export const PREVIEW_COMPLETED = 'PREVIEW_COMPLETED';
export const PREVIEW_CONTENT_LOADED = 'PREVIEW_CONTENT_LOADED';

export const requestPreview = (id, step) => ({
  type: PREVIEW_REQUESTED,
  payload: { id, step },
});

export const reload = () => ({
  type: PREVIEW_RELOADED,
});

export const exit = () => ({
  type: PREVIEW_EXITED,
});

export const start = () => ({
  type: PREVIEW_STARTED,
});

export const load = content => ({
  type: PREVIEW_CONTENT_LOADED,
  payload: content,
});

export const complete = () => ({
  type: PREVIEW_COMPLETED,
});
