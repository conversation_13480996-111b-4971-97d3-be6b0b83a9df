import { evaluateSelectorAll } from 'ext/lib/evaluate-selector';

export const VALID = 'VALID';
export const INVALID = 'INVALID';
export const NOT_FOUND = 'NOT_FOUND';
export const NOT_UNIQUE = 'NOT_UNIQUE';
export const SVG = 'SVG';

export const STATUS = {
  VALID,
  INVALID,
  NOT_FOUND,
  NOT_UNIQUE,
  SVG,
};

export const INVALID_STATUS = [NOT_FOUND, INVALID, SVG];

/**
 * Determine validity of selector
 *
 * @param {string} selector - Selector
 * @param {Object<(string|number)>} options - Additional filter options
 * @param {string} options.text - Text filter
 * @param {number} options.number - Order filter
 * @return {Object} result
 * @return {(VALID|INVALID|NOT_FOUND|NOT_UNIQUE|SVG)} result.status - Selector validity
 * @return {number} result.count - Count of matched elements
 */
export function validateSelector(selector, { text, order } = {}) {
  let $elements;
  try {
    $elements = [...evaluateSelectorAll(selector)];
  } catch {
    return { status: INVALID, count: 0 };
  }

  if ($elements.length === 0) {
    return { status: NOT_FOUND, count: 0 };
  }

  let $filtered = $elements;

  if (text != null) {
    $filtered = $filtered.filter(({ innerText }) => {
      if (!innerText) {
        return false;
      }

      const sanitized = innerText
        .toLowerCase()
        .replace(/\r\n|\r|\n/g, ' ')
        .trim();

      return sanitized === text.toLowerCase().trim();
    });
  }

  const { length: count } = $filtered;

  if (order != null) {
    $filtered = $filtered[order] ? [$filtered[order]] : [];
  }

  if ($filtered.length === 0) {
    return { status: NOT_FOUND, count };
  }

  if ($filtered.length === 1) {
    const [$element] = $filtered;
    return {
      status: $element instanceof SVGElement ? SVG : VALID,
      count,
    };
  }

  return { status: NOT_UNIQUE, count };
}
