import { useState } from 'react';
import { useLayoutInterval } from 'ext/lib/hooks/use-interval';
import { validateSelector } from 'lib/validate-selector';

export default function useSelectorValidity(stepChild) {
  const [{ status, count }, setValidity] = useState({});

  const {
    includeOrderedMatchSelector,
    includeTextMatchSelector,
    selector,
    selectorOrderValue,
    selectorTextValue,
  } = stepChild;

  useLayoutInterval(() => {
    const { count: newCount, status: newStatus } = validateSelector(selector, {
      ...(includeOrderedMatchSelector && { order: selectorOrderValue }),
      ...(includeTextMatchSelector && { text: selectorTextValue }),
    });
    if (newCount !== count || status !== newStatus) {
      setValidity({
        status: newStatus,
        count: newCount,
      });
    }
  }, 200);

  return { status, count };
}
