import { useEffect, useState } from 'react';

/**
 * React Hook to mark a single instance of a component as the "allocated"
 * singleton. For example, this hook can be used to ensure that only one
 * instance of a component is rendered. However, the hook itself is agnostic to
 * how the allocated flag will be used and can use be used in other scenarios.
 */

/**
 * Create a new instance of the use-singleton hook. Because a shared global
 * state is required to know whether this particular rendered instance is the
 * allocated singleton, a factory is used to create that global state in
 * closure. An instance is create and exported as the default, but the factory
 * itself is also exported for tests and if a separate instance is required for
 * some reason.
 *
 * @returns {React.Hook} New use-singleton hook
 */
export const createUseSingleton = () => {
  // Map to hold allocated instances
  const singleton = new Map();

  // Map to hold queued instances
  const queued = new Map();

  // Ticketing ID to mark individual instances
  let ticket = 0;

  /**
   * Created singleton React Hook
   *
   * @param {string} id - Singleton ID
   * @return {boolean} Whether this instance is the allocated singleton
   */
  return id => {
    const [allocated, allocate] = useState(false);

    useEffect(() => {
      // Assign instance the current ticket
      const instance = ticket;

      // Increment ticket to prevent collision
      ticket += 1;

      // If a singleton for this ID already exists, add this instance to the
      // queue to allow it be allocated once the instance is freed. We store
      // both the instance ID and the allocate callback to manually trigger an
      // updated render from the previously allocated. If a singleton does
      // not exist for this ID, allocate this instance to the singleton.
      if (singleton.has(id)) {
        const queue = queued.get(id) || new Map();
        const message = { instance, allocate };
        queued.set(id, queue.set(instance, message));
      } else {
        singleton.set(id, instance);
        allocate(true);
      }

      return () => {
        // If this instance is the currently allocated singleton, then
        // deallocate the instance.
        if (singleton.get(id) === instance) {
          singleton.delete(id);
          allocate(false);
        }

        // Check whether or not there is a queue for this singleton ID. If so,
        // remove the instance from the queue so reallocation is not attempted
        // and also attempt to reallocate the singleton to the next possible
        // instance in the queue.
        if (queued.has(id)) {
          const queue = queued.get(id);
          queue.delete(instance);

          const [next] = queue.values();

          // If another instance is ready to be allocated, remove it from the
          // queue, set the instance to this singleton ID and trigger an update
          // by calling it's allocate callback.
          if (next) {
            queue.delete(next.instance);
            singleton.set(id, next.instance);
            next.allocate(true);
          }
        }
      };
    }, [id]);

    return allocated;
  };
};

export default createUseSingleton();
