/* global NEW_EXTENSION */
/* globals chrome */

import { initialize } from 'ext/lib/crx';

const { attach, create } = initialize('flow', post => ({
  // NOTE: This one method doesn't use the port, but it's here anyway to keep
  //       the CRX dependency injection clean
  openFlow: (id, url) => {
    if (NEW_EXTENSION) {
      chrome.runtime.sendMessage({
        action: 'appcues:open-flow',
        value: JSON.stringify({ flow: id, url }),
      });
    } else {
      // @TODO - remove this after the manivest V3 rewrite is complete
      chrome.runtime.sendMessage({
        action: 'open-flow-from-studio',
        value: JSON.stringify({ contentKey: id, url }),
      });
    }
  },

  navigate: url => post('set-navigating-to-url', { url }),
}));

export { attach, create };
