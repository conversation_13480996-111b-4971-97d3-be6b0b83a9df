import { v4 as uuid } from 'uuid';
import HTMLParser from 'html-parse-stringify2';
import { fromJS } from 'immutable';

import { generateNewZoneAST, generateNewRowAST } from 'ext/lib/wysiwyg-ast';
import { MODAL, SLIDEOUT, TOOLTIP, HOTSPOT } from 'entities/step-groups';
import {
  createModalButtonZone,
  createTooltipButtonZone,
  createDismissLinkZone,
} from 'entities/step-children';
import { generateButtonAST } from 'vendor/wysiwyg/helpers/buttons';
import { generateDismissLinkAST } from 'vendor/wysiwyg/helpers/dismissLink';
import { EDITOR_TYPES } from 'vendor/wysiwyg/helpers/constants';

const generateContentHTML = ({ content, zones, theme, group }) => {
  const contentAST = HTMLParser.parse(content);
  const zonesAST = zones.map(zone => {
    const generateFunction =
      zone.type === EDITOR_TYPES.DISMISS_LINK
        ? generateDismissLinkAST
        : generateButtonAST;

    return generateNewZoneAST(
      generateFunction({
        persistedState: fromJS(zone.persistedState),
        zoneId: zone.id,
        theme,
        patternType: group.stepType,
      })
    );
  });

  contentAST[0].children.push(generateNewRowAST(zonesAST));

  return HTMLParser.stringify(contentAST);
};

/**
 * Transforms a step child's sidebar button settings into custom buttons. The
 * return value indicates if anything changed and the new step child.
 *
 * @param  {object} child  The step child object
 * @param  {object} group  The step group object
 * @return {Array}         An array of 2 elements where the first value is a
 *                         boolean indicating if anything changed and the second
 *                         value is the updated step child object.
 */
export function transformStepChild(child, group, theme) {
  const rows = child.rows || [];

  switch (group.stepType) {
    case MODAL:
    case SLIDEOUT: {
      const {
        actionsHidden,
        prevButtonHidden,
        nextButtonHidden,
        isButtonCentered,
        prevText,
        nextText,
      } = child;

      if (actionsHidden || (prevButtonHidden && nextButtonHidden)) {
        return [false, child];
      }

      const zones = [];
      if (!prevButtonHidden && !isButtonCentered) {
        zones.push(
          createModalButtonZone({
            buttonText: prevText || 'Back',
            buttonActionType: 'prev',
            textAlign: 'left',
            buttonType: 'secondary',
          })
        );
      }

      if (!nextButtonHidden) {
        zones.push(
          createModalButtonZone({
            buttonText: nextText || 'Next',
            buttonActionType: 'next',
            textAlign: isButtonCentered ? 'center' : 'right',
            buttonType: 'primary',
          })
        );
      }

      return [
        true,
        {
          ...child,
          rows: [...rows, { id: uuid(), zones }],
          content: generateContentHTML({
            content: child.content,
            zones,
            group,
            theme,
          }),
        },
      ];
    }

    case TOOLTIP: {
      const { nextText, skipText, uiConditions = {}, hideNextButton } = child;
      const { skippable, skippableDisplayType } = group;
      if (
        (!skippable || (skippable && skippableDisplayType === 'exit-symbol')) &&
        !!uiConditions.next
      ) {
        return [false, child];
      }

      const zones = [];
      if (skippable && skippableDisplayType !== 'exit-symbol') {
        zones.push(createDismissLinkZone({ skipText }));
      }

      if (
        !uiConditions.next ||
        (!hideNextButton && uiConditions.next !== null)
      ) {
        zones.push(
          createTooltipButtonZone({
            buttonText: nextText || 'Next',
            buttonActionType: 'next',
            textAlign: 'right',
            buttonType: 'primary',
          })
        );
      }

      return [
        true,
        {
          ...child,
          rows: [...rows, { id: uuid(), zones }],
          content: generateContentHTML({
            content: child.content,
            zones,
            group,
            theme,
          }),
        },
      ];
    }

    case HOTSPOT: {
      const { skipText } = child;
      const { skippable, skippableDisplayType } = group;
      if (skippable && skippableDisplayType !== 'exit-symbol') {
        const zones = [createDismissLinkZone({ skipText })];

        return [
          true,
          {
            ...child,
            rows: [...rows, { id: uuid(), zones }],
            content: generateContentHTML({
              content: child.content,
              zones,
              group,
              theme,
            }),
          },
        ];
      }
      return [false, child];
    }

    default:
      return [false, child];
  }
}
