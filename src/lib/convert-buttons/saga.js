import {
  put,
  getContext,
  takeEvery,
  select,
  call,
  take,
} from 'redux-saga/effects';
import { ONLY_CUSTOM_BUTTONS } from 'ext/lib/gates';
import { set, remove } from 'ext/lib/session-storage';
import { compareLocations } from 'ext/lib/location';
import { selectThemes, selectThemeOrDefault } from 'ext/entities/themes';
import { patterns as bootstrapPatterns } from 'ext/entities/bootstrap';
import {
  resolve,
  reject,
  patterns,
  FLOW_CONVERSION_UNDONE,
  FLOW_CONVERSION_REDONE,
  transform,
  revert,
  selectFlow,
} from 'entities/flows';
import { selectStepGroups } from 'entities/step-groups';
import { selectStepChildren } from 'entities/step-children';

import { transformStepChild } from './transform';

function* transformFlow({ flow = {}, stepGroups = {}, stepChildren = {} }) {
  const logger = yield getContext('logger');
  const themes = yield select(selectThemes);

  // for each transformStepChild call we need to also access the right theme
  // but a racing condition may happen if we try to call it before the bootstrap
  // data is loaded.
  // example, when after opening a v1 flow and enabling the onlyCustomButton flag
  // it will reload the builder and open the flow that was already opened,
  // and since the flow is a v1 it will try to convert before the bootstrap action
  // resolves, and in this case the race condition happens.
  if (Object.keys(themes).length === 0) {
    yield take(bootstrapPatterns.resolve);
  }

  const state = yield select();

  try {
    const updatedStepChildren = Object.values(stepGroups)
      .flatMap(
        group =>
          group.steps &&
          group.steps.map(id => {
            if (stepChildren[id]) {
              const theme = selectThemeOrDefault(state, group.style);
              const [, child] = transformStepChild(
                stepChildren[id],
                group,
                theme
              );

              return child;
            }
            return null;
          })
      )
      .filter(Boolean)
      .reduce((memo, child) => ({ ...memo, [child.id]: child }), {});

    const fullFlow = revert(
      { ...flow, formatVersion: 2, formatVersionUpdatedAt: Date.now() },
      { stepGroups, stepChildren: updatedStepChildren }
    );

    const api = yield getContext('api');
    const response = yield call(api.replaceFlow, fullFlow.id, fullFlow);
    yield put(resolve(transform(response)));
  } catch (error) {
    yield put(reject(error));
    logger.error(error);
  }
}

function* convertFlow({
  payload: { flow = {}, stepGroups = {}, stepChildren = {} },
}) {
  const { [ONLY_CUSTOM_BUTTONS]: customButtonsEnabled } =
    (yield getContext('gates')) || {};

  const {
    formatVersion,
    formatVersionUpdatedAt,
    lockedForLocalization,
    id,
    previewUrl,
  } = flow;

  const { href: destination } = new URL(previewUrl, window.document.baseURI);
  // To avoid a flow to be double converted
  // we check if the current opened url is the previewURL
  // if not, we do nothing since the builder will force a redirect
  // and only then, after the redirect we will convert the flow.
  // As a result, the users will have a change to see the conversion modal
  // and the flow doesn't get converted twice.
  const isPreviewURL = compareLocations(window.location.href, destination);

  if (
    formatVersion === 2 ||
    formatVersionUpdatedAt ||
    lockedForLocalization ||
    !customButtonsEnabled ||
    !isPreviewURL
  ) {
    return;
  }

  set(`apc_btn:${id}`, { flow, stepGroups, stepChildren });

  yield call(transformFlow, { flow, stepGroups, stepChildren });
}

function* undoFlowConversion({ payload: { flow, stepGroups, stepChildren } }) {
  const logger = yield getContext('logger');
  try {
    const fullFlow = revert(
      { ...flow, formatVersionUpdatedAt: Date.now() },
      { stepGroups, stepChildren }
    );

    const api = yield getContext('api');
    const response = yield call(api.replaceFlow, flow.id, fullFlow);
    yield put(resolve(transform(response)));

    remove(`apc_btn:${flow.id}`);
  } catch (error) {
    yield put(reject(error));
    logger.error(error);
  }
}

function* redoFlowConversion() {
  const flow = yield select(selectFlow);
  const stepGroups = yield select(selectStepGroups);
  const stepChildren = yield select(selectStepChildren);
  yield call(transformFlow, { flow, stepGroups, stepChildren });
}

export default function* convertButtonsSaga() {
  yield takeEvery(patterns.resolve, convertFlow);
  yield takeEvery(FLOW_CONVERSION_UNDONE, undoFlowConversion);
  yield takeEvery(FLOW_CONVERSION_REDONE, redoFlowConversion);
}
