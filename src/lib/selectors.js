import { doesLocationMatch } from 'ext/vendor/url-match';
import { selectFlow } from 'entities/flows';
import { selectSelected } from 'entities/selected';
import {
  selectStepGroup,
  selectStepGroups,
  ACTION,
} from 'entities/step-groups';

/**
 * Select parent flow and step group IDs
 *
 * @param {State} state - Redux state
 * @return {object} Parent flow and step group IDs
 */
export function selectParents(state) {
  return {
    flowId: selectFlow(state).id,
    stepGroupId: selectSelected(state).stepGroup,
  };
}

export function selectRelevantAction(state, stepId) {
  const groups = selectStepGroups(state);
  const { steps } = selectFlow(state);

  if (groups[stepId] && groups[stepId].stepType === ACTION) {
    return groups[stepId];
  }

  const index = steps && steps.indexOf(stepId);
  const actionId =
    steps &&
    steps
      .slice(0, index)
      .reverse()
      .find(i => groups[i].stepType === ACTION);

  return groups[actionId];
}

/**
 * Derive preview URL for provided step group ID
 *
 * @param {State} state - Redux state
 * @param {string} stepId - Step group ID
 * @return {string} Either the flow's preview URL or page group's build URL
 */
export function selectPreviewUrl(state, stepId) {
  const { previewUrl: baseUrl } = selectFlow(state);

  if (!stepId) {
    return baseUrl;
  }

  const action = selectRelevantAction(state, stepId);
  return action ? action.previewUrl : baseUrl;
}

/**
 * Derive target URL for provided step group ID
 *
 * @param {State} state - Redux state
 * @param {string} stepId - Step group ID
 * @return {string} Either the flow's preview URL or page group's target URL
 */
export function selectTargetUrl(state, stepId) {
  const { previewUrl: baseUrl } = selectFlow(state);

  if (!stepId) {
    return baseUrl;
  }

  const action = selectRelevantAction(state, stepId);
  return action ? action.params.url : baseUrl;
}

/**
 * Derive index where group should be inserted
 *
 * @param {State} state - Redux state
 * @return {number} Index to insert group
 */
export function selectNextGroupIndex(state) {
  const { stepGroup } = selectSelected(state) || {};
  const { steps } = selectFlow(state);
  return steps.indexOf(stepGroup) + 1;
}

/**
 * Derive whether there is a location mismatch
 *
 * @param {State} state - Redux state
 * @return {number} Index to insert group
 */
export function selectHasLocationMismatch(state) {
  const flow = selectFlow(state);
  const selected = selectSelected(state);
  const stepGroup = selected && selectStepGroup(state, selected.stepGroup);

  return (
    flow &&
    stepGroup &&
    stepGroup.stepType !== ACTION &&
    !doesLocationMatch(
      selectTargetUrl(state, selected.stepGroup),
      window.location.href
    )
  );
}

/**
 * Derive whether is a first step in the selected step group
 * @param {State} state
 * @returns {boolean} if is first step in a group
 */
export function selectIsFirstStep(state) {
  const selected = selectSelected(state);
  const stepGroup = selected && selectStepGroup(state, selected.stepGroup);

  return !!(stepGroup && !stepGroup.steps.indexOf(selected.stepChild));
}
