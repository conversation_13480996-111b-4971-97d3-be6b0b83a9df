import { call, getContext, put, select, takeEvery } from 'redux-saga/effects';
import { selectUserId } from 'ext/entities/account';
import { completeInteraction } from 'ext/lib/track';
import { UPDATE_USAGE_PROPERTY } from 'lib/metrics/interaction-types';
import { flush, reject, patterns } from './actions';
import { selectAllUsageProperty } from './reducer';

function* updateUsageProperty({ payload: delta }) {
  try {
    const userId = yield select(selectUserId);
    const api = yield getContext('api');
    const allProperties = yield select(selectAllUsageProperty);
    yield call(api.updateUsageProperties, userId, {
      ...allProperties,
      ...delta,
    });
    yield put(flush());
    yield put(completeInteraction(UPDATE_USAGE_PROPERTY));
  } catch (error) {
    yield put(reject(error));
  }
}

export default function* usagePropertiesSaga() {
  yield takeEvery(patterns.update, updateUsageProperty);
}
