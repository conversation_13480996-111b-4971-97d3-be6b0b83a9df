import { schema, normalize, denormalize } from 'normalizr';
import { HOTSPOT, TOOLTIP } from 'entities/step-groups';

const without = (obj, key) => {
  const { [key]: omit, ...result } = obj;
  return result;
};

const stepChild = new schema.Entity(
  'stepChildren',
  {},
  {
    processStrategy: entity => without(entity, 'index'),
  }
);

const stepGroup = new schema.Entity(
  'stepGroups',
  {
    steps: [stepChild],
    hotspots: [stepChild],
  },
  {
    processStrategy: entity => without(entity, 'index'),
  }
);

const flowSchema = { steps: [stepGroup] };

//
// COMPATABILITY BLOCK
//

/**
 * we need to check the group type to know if we should call the stepChildren
 * "steps" or "hotspots"
 */
const hotTypes = new Set([HOTSPOT, TOOLTIP]);

/**
 * The step/stepChild collection is returned as an object with an index property instead
 * of as an array. Recursively map through the tree and turn the objects into arrays.
 */
function sortSteps({ steps, hotspots, ...entity }) {
  const ownSteps = steps || hotspots;
  if (ownSteps) {
    return {
      ...entity,
      steps: Object.keys(ownSteps)
        .sort((a, b) => (ownSteps[a].index < ownSteps[b].index ? -1 : 1))
        .map(key => sortSteps(ownSteps[key])),
    };
  }
  return entity;
}

/**
 * Turn the nice, ordered lists back into objects.
 */
function unsortSteps({ steps, ...entity }) {
  const childKey = hotTypes.has(entity.stepType) ? 'hotspots' : 'steps';

  if (steps) {
    return {
      ...entity,
      [childKey]: steps.reduce((acc, item, index) => {
        if (item) {
          acc[item.id] = { ...unsortSteps(item), index };
        }
        return acc;
      }, {}),
    };
  }
  return entity;
}

/**
 * @param {Flow} flow - the received flow object
 *
 * @return {object} - the normalized flow, stepGroups, and stepChildren
 */
export function transform(data) {
  const { entities, result } = normalize(sortSteps(data), flowSchema);

  return {
    flow: result,
    stepGroups: entities.stepGroups || {},
    stepChildren: entities.stepChildren || {},
  };
}

/**
 * @param {Flow} flow - the normalized flow object
 * @param {object} entities - the collections of stepGroups and stepChildren
 *
 * @return {Flow} - the fully-hydrated flow, just like the server used to make
 */
export const revert = (flow, entities) =>
  unsortSteps(denormalize(flow, flowSchema, entities));

/**
 * @param {Response} response - formatted flow returned from the Preview API
 *
 * @return {object} defaultContent - a map of stepChild id to default-locale HTML
 */
export const getDefaultContent = response => {
  const { steps: groups } = response.attributes;

  return Object.values(groups).reduce((acc, group) => {
    if (group.step_type === 'action') {
      return acc;
    }

    const { steps, hotspots } = group.step.attributes;

    (steps || hotspots || []).forEach(child => {
      acc[child.id] = {
        html: child.html,
        nextText: child.next_text,
        prevText: child.prev_text,
        skipText: child.skip_text,
      };
    });
    return acc;
  }, {});
};
