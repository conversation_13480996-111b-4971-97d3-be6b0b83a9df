import dedent from 'dedent';
import {
  call,
  getContext,
  put,
  select,
  take,
  takeEvery,
  fork,
} from 'redux-saga/effects';
import { ONLY_CUSTOM_BUTTONS } from 'ext/lib/gates';
import { compareLocations } from 'ext/lib/location';
import { completeInteraction } from 'ext/lib/track';
import { INITIALIZE } from 'ext/root/root-actions';
import { selectFlowSummary } from 'ext/entities/flows-summary';
import offcues from 'ext/lib/offcues';
import { selectSelected } from 'entities/selected';
import { selectStepGroup } from 'entities/step-groups';
import { resolveContent } from 'entities/step-children';
import { ensureLocation } from 'lib/location';
import { CREATE_FLOW, MOVE_STEP_GROUP } from 'lib/metrics/interaction-types';
import {
  focus as focusFlow,
  flush,
  patterns,
  reject,
  resolve,
  insert,
  movePatterns,
} from './flow-actions';
import { selectFlow } from './flow-reducer';
import { transform, getDefaultContent } from './flow-schema';

function* fetchFlow(id) {
  const logger = yield getContext('logger');

  try {
    const api = yield getContext('api');
    const { flow } = yield call(api.getFlow, id);
    if (flow.lockedForLocalization) {
      const preview = yield call(api.getFlowPreview, id);
      yield put(resolveContent(getDefaultContent(preview)));
    }
    yield put(resolve(transform(flow)));
  } catch (error) {
    yield put(reject(error));
    logger.error(error);
  }
}

function* createFlow(action) {
  const logger = yield getContext('logger');

  try {
    const {
      payload: { name, tagIds, previewUrl },
    } = action;
    const { [ONLY_CUSTOM_BUTTONS]: customButtonsEnabled } =
      (yield getContext('gates')) || {};
    const api = yield getContext('api');
    const { flow } = yield call(api.createFlow, {
      name,
      tagIds,
      previewUrl,
      formatVersion: customButtonsEnabled ? 2 : 1,
    });

    yield put(insert(transform(flow)));
    yield put(completeInteraction(CREATE_FLOW));
  } catch (error) {
    yield put(reject(error));
    logger.error(error);
  }
}

function* flushFlow(action) {
  const logger = yield getContext('logger');

  try {
    const {
      payload: { id, ...delta },
    } = action;
    const api = yield getContext('api');
    yield call(api.updateFlow, id, delta);
    yield put(flush(id));
  } catch (error) {
    yield put(reject(error));
    logger.error(error);
  }
}

function* moveStepGroup(action) {
  const logger = yield getContext('logger');

  try {
    const {
      payload: { child, to },
    } = action;
    const api = yield getContext('api');
    const { id } = yield select(selectFlow);
    const { stepType } = yield select(selectStepGroup, child);
    const data = { flowId: id, stepType, stepGroupId: child };
    yield call(api.moveStepGroup, data, to);
    yield put(flush(id));
    const { stepGroup } = yield select(selectSelected);

    if (stepGroup === child) {
      yield call(ensureLocation, child);
    }

    yield put(completeInteraction(MOVE_STEP_GROUP));
  } catch (error) {
    yield put(reject(error));
    logger.error(error);
  }
}

/**
 * we may need to persist state and navigate when selecting a flow;
 * this function acts as a gate befor fetching
 *
 * @param {Action} action - a Flow focus() action
 */
function* validate({ payload: id }) {
  try {
    const previous = yield select(selectFlow);
    const flowSummary = yield select(selectFlowSummary, id);
    const { previewUrl } = flowSummary;

    const { origin } = new URL(previewUrl);

    const similar = compareLocations(window.location.origin, origin);

    if (!similar) {
      const crx = yield getContext('crx');
      // eslint-disable-next-line no-alert
      const shouldNavigate = window.confirm(
        dedent(`
          The flow you’ve chosen is on a different website.

          You will need to re-open the Appcues Flow Builder on the new page.

          Click OK to continue. (Don’t worry, your work will be saved!)
        `)
      );
      if (shouldNavigate) {
        crx.openFlow(id, previewUrl);
      } else if (previous) {
        yield put(focusFlow(previous.id));
      }
    } else {
      // We fork fetchFlow here rather than call since fetchFlow has a put(resolve) and using call rather than
      // fork on fetchFlow was blocking the take on resolve below until fetchFlow finished doing its thing.
      // Thus, we would never call ensureLocation (and in turn never update the flow's location on the soame domain)
      // since resolve was put before it could be taken. Using fork allows the take on resolve to be made before it is put in fetchFlow
      yield fork(fetchFlow, id);
      yield take(patterns.resolve);
      yield call(ensureLocation);
    }
  } catch (error) {
    const logger = yield getContext('logger');
    logger.error(error);
  }
}

/**
 * Saga to fetch flow when the builder initializes. Primarily used to open the
 * builder at a specific flow/step such as `Open in builder` from studio. Note
 * that `selected.flow` is only used here as a one-time read value from the
 * initial CRX message and will be removed once the `fetchFlow` saga resolves.
 */
function* initialize() {
  const selected = yield select(selectSelected);
  const flow = yield select(selectFlow);
  const selectedId = selected ? selected.flow : null;
  const persistedFlowId = flow ? flow.id : null;

  if (selectedId || persistedFlowId) {
    yield call(fetchFlow, selectedId ?? persistedFlowId);

    // Calling offcues here to guard against an intermittent issue where the live
    // flow is appearing in the builder edit mode when refreshing the page.
    // Suspect this edge case is caused by a race condition where offcues in the
    // onReady (index.js) is called before the SDK has finished loading.
    offcues(document);
  }
}

export default function* flowSaga() {
  yield takeEvery(patterns.focus, validate);
  yield takeEvery(patterns.create, createFlow);
  yield takeEvery(patterns.update, flushFlow);
  yield takeEvery(movePatterns.move, moveStepGroup);
  yield takeEvery(INITIALIZE, initialize);
}
