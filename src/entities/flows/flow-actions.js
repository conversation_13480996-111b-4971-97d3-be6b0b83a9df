import { createLifecycleFor, createMoveActionsFor } from 'ext/lib/collections';

export const TYPE = 'flow';

export const {
  focus,
  resolve,
  create,
  insert,
  update,
  reject,
  replace,
  flush,
  patterns,
} = createLifecycleFor(TYPE);

export const { move: moveStepGroup, patterns: movePatterns } =
  createMoveActionsFor(TYPE);

export const FLOW_CONVERSION_UNDONE = 'FLOW_CONVERSION_UNDONE';
export const undoFlowConversion = preConversionData => ({
  type: FLOW_CONVERSION_UNDONE,
  payload: preConversionData,
});

export const FLOW_CONVERSION_REDONE = 'FLOW_CONVERSION_REDONE';
export const redoFlowConversion = () => ({
  type: FLOW_CONVERSION_REDONE,
});
