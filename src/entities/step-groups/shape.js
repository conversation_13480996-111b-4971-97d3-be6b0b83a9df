import PropTypes from 'prop-types';
import { STEP_TYPES } from './constants';

const modalSpecificProps = {
  isProgressBarHidden: PropTypes.bool,
  patternType: PropTypes.oneOf(['fullscreen', 'left', 'modal', 'shorty']),
  position: PropTypes.oneOf([
    'top',
    'topRight',
    'right',
    'bottomRight',
    'bottom',
    'bottomLeft',
    'left',
    'topLeft',
    'center',
  ]),
};

const tooltipSpecificProps = {
  backdropSolidEdge: PropTypes.bool,
  backdropSolidEdgeOpacity: PropTypes.number,
  beaconStyle: PropTypes.oneOf(['hidden', 'hotspot', 'question']),
  skippableDisplayType: PropTypes.oneOf(['exit-symbol', 'text']),
};

const navigateSpecificProps = {
  actionType: PropTypes.oneOf(['redirect', 'wait-for-page']),
  previewUrl: PropTypes.string,
  params: PropTypes.shape({
    new_tab: PropTypes.bool,
    url: PropTypes.string,
  }),
};

export default PropTypes.shape({
  backdrop: PropTypes.bool,
  id: PropTypes.string,
  index: PropTypes.number,
  skippable: PropTypes.bool,
  stepType: PropTypes.oneOf(STEP_TYPES),
  steps: PropTypes.arrayOf(PropTypes.string),
  ...modalSpecificProps,
  ...tooltipSpecificProps,
  ...navigateSpecificProps,
});
