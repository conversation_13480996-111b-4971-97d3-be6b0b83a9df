export const SLIDEOUT = 'shorty';
export const HOTSPOT = 'hotspot-group';
export const TOOLTIP = 'tooltip-group';
export const MODAL = 'modal';
export const ACTION = 'action';

export const STEP_TYPES_LABELS = {
  [SLIDEOUT]: 'slideout',
  [HOTSPOT]: 'hotspot',
  [TOOLTIP]: 'tooltip',
  [MODAL]: 'modal',
  [ACTION]: 'action',
};

export const STEP_TYPES = Object.keys(STEP_TYPES_LABELS);
export const STEP_LABELS = Object.values(STEP_TYPES_LABELS);

// Reference from our API
// https://github.com/appcues/api/blob/fb43b6d97080dbda9dad827a64ede21e008ebb3d/web/data/customer_api_renderer.ex#L153-L176
export const STEP_TYPES_TO_LEGACY_TYPES = {
  [SLIDEOUT]: 'modal',
  [HOTSPOT]: 'hotspot-group',
  [TOOLTIP]: 'hotspot-group',
  [MODAL]: 'modal',
  [ACTION]: 'action',
};
