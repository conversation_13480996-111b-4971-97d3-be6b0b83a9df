import { schema, normalize } from 'normalizr';

const stepChild = new schema.Entity('stepChildren');

const stepGroup = new schema.Entity(
  'stepGroups',
  { steps: [stepChild] },
  {
    // For hotspot/tooltip groups, rather than keeping the `hotspots` key for
    // step children, rename the field to `steps`, similar to the transform we
    // perform for the flow schema
    processStrategy: ({ steps, hotspots, ...entity }) => ({
      ...entity,
      steps: steps || hotspots,
    }),
  }
);

const response = {
  stepGroups: [
    {
      stepGroup,
      stepChild,
    },
  ],
};

const defaults = {
  stepGroups: {},
  stepChildren: {},
};

export const transform = data => {
  const { entities } = normalize(data, response);
  return {
    ...defaults,
    ...entities,
  };
};
