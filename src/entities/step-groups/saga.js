import {
  call,
  getContext,
  put,
  race,
  select,
  take,
  takeEvery,
  takeLatest,
  all,
} from 'redux-saga/effects';
import { v4 as uuid } from 'uuid';
import {
  attemptInteraction,
  completeInteraction,
  INTERACTION_COMPLETED,
  INTERACTION_ATTEMPTED,
} from 'ext/lib/track';
import { SELECTOR_REQUEST_COMPLETED } from 'ext/lib/targeting-interface';
import { IFRAME_ROOT_TOKEN } from 'ext/lib/evaluate-selector';
import {
  MOVE_STEP_CHILD,
  ADD_STEP_GROUP,
  REMOVE_STEP_GROUP,
  ADD_ACTION_STEP_GROUP,
  UPDATE_STEP_GROUP,
  UPDATE_STEP_CHILD,
} from 'lib/metrics/interaction-types';
import { addDismissLink, removeDismissLink } from 'lib/toggle-dismiss-link';
import {
  CONTROLS_VISIBILITY_TOGGLED,
  SIDEBAR_MOVED,
  SIDEBAR_VISIBILITY_TOGGLED,
} from 'entities/application';
import { selectFlow } from 'entities/flows';
import {
  patterns as childPatterns,
  getPosition,
  createModalButtonZone,
  createTooltipButtonZone,
  update as updateStepChild,
  selectStepChildren,
} from 'entities/step-children';
import { selectSelected } from 'entities/selected';
import { selectPreviewUrl, selectNextGroupIndex } from 'lib/selectors';
import { NAVIGATE_EXITED } from 'lib/navigation-mode';
import { ensureLocation } from 'lib/location';
import { updateSurveyZoneIds } from 'lib/zones';
import {
  flush,
  reject,
  insert,
  patterns,
  movePatterns,
  UPDATE_SKIPPABLE,
  update,
} from './actions';
import { selectStepGroup, getStepLabel } from './reducer';
import getDefaults from './get-defaults';
import { transform } from './schema';
import { ACTION, HOTSPOT, TOOLTIP } from './constants';

/**
 * NOTE: For some reason, the step child move endpoint requires the step type
 *       path param to be in a different format than the step group move
 *       endpoint. It seems this was added to the flow builder backend and
 *       Customer API maintained the mapping for parity reasons. Once this
 *       unneeded mapping is removed from Customer API< we can remove this step
 *       and hopefully pass the `stepType` directly.
 */

const applicationActions = new Set([
  CONTROLS_VISIBILITY_TOGGLED,
  SIDEBAR_MOVED,
  SIDEBAR_VISIBILITY_TOGGLED,
  INTERACTION_COMPLETED,
  INTERACTION_ATTEMPTED,
]);

export function* moveStepChild(action) {
  try {
    const {
      payload: { parent, child, to },
    } = action;
    const api = yield getContext('api');
    const { id: flowId } = yield select(selectFlow);
    const { stepType: type } = yield select(selectStepGroup, parent);

    /** SEE NOTE ABOVE */
    const stepType = `${getStepLabel(type)}s`;
    const data = { flowId, stepType, stepGroupId: parent, stepChildId: child };
    yield call(api.moveStepChild, data, to);
    yield put(flush(parent));
    yield put(completeInteraction(MOVE_STEP_CHILD));
  } catch (error) {
    yield put(reject(error));
  }
}

function* addStepGroup({ payload: stepType }) {
  try {
    const [create, abort] = yield race([
      take(childPatterns.create),
      // Take any non-application action
      take(action => !applicationActions.has(action.type)),
    ]);

    if (abort) {
      yield put(completeInteraction(ADD_STEP_GROUP));
      return;
    }

    const {
      payload: { fromTemplate },
    } = create;

    let {
      payload: { step: stepChild },
    } = create;

    if (stepType === HOTSPOT || stepType === TOOLTIP) {
      const positionFields = yield call(getPosition, stepChild);

      if (positionFields === null) {
        yield put(completeInteraction(ADD_STEP_GROUP));
        return;
      }

      stepChild = { ...stepChild, ...positionFields };
    }

    const isScratchStep = !stepChild.rows || stepChild.rows.length === 0;
    const { isTypeSidebar } = stepChild;
    const { id: flowId, formatVersion } = yield select(selectFlow);

    const hasNextBtn = stepChild.rows?.some(row =>
      row.zones?.some(zone => zone.persistedState.buttonActionType === 'next')
    );

    if (fromTemplate) {
      stepChild.rows = updateSurveyZoneIds(stepChild.rows);
    }

    if (
      !fromTemplate &&
      formatVersion === 2 &&
      stepType !== HOTSPOT &&
      !isScratchStep &&
      !hasNextBtn &&
      !stepChild.nextButtonHidden
    ) {
      const buttonZone =
        stepType === TOOLTIP
          ? createTooltipButtonZone({
              buttonText: 'Next',
              buttonActionType: 'next',
              buttonType: 'primary',
              textAlign: 'right',
            })
          : createModalButtonZone({
              buttonText: 'Next',
              buttonActionType: 'next',
              buttonType: 'primary',
              textAlign: 'right',
            });
      stepChild = {
        ...stepChild,
        rows: [
          ...(stepChild.rows || []),
          {
            id: uuid(),
            zones: [buttonZone],
          },
        ],
      };
    }

    const index = yield select(selectNextGroupIndex);
    const stepGroup = yield call(getDefaults, { stepType, index });

    if (isScratchStep) {
      stepGroup.skippable = false;
      stepGroup.skippableDisplayType = null;
    }

    if (isTypeSidebar) {
      stepGroup.patternType = 'left';
    }

    const shouldAddDismissLink =
      stepGroup.skippable &&
      (stepType === HOTSPOT ||
        (stepType === TOOLTIP && stepGroup.skippableDisplayType === 'text'));

    if (formatVersion === 2 && shouldAddDismissLink) {
      const [, stepChildWithDismissLink] = addDismissLink(stepChild);
      stepChild = stepChildWithDismissLink;
    }

    const { createStepGroup } = yield getContext('api');
    const stepGroupTypePathParam = `${getStepLabel(stepType)}s`;
    const response = yield call(createStepGroup, {
      flowId,
      group: {
        index,
        stepGroups: [
          {
            stepGroup,
            stepChild: {
              ...stepChild,
              index: 0,
            },
            stepGroupTypePathParam,
          },
        ],
      },
    });
    yield put(insert(transform(response), flowId));
    yield put(completeInteraction(ADD_STEP_GROUP));

    const { stepGroup: selectedStep } = yield select(selectSelected);
    yield call(ensureLocation, selectedStep);
  } catch {
    // TODO: what does this error MEAN
  }
}
function* flushStepGroup(action) {
  try {
    const {
      payload: { id, delta },
    } = action;

    const { id: flowId } = yield select(selectFlow);
    const { stepType } = yield select(selectStepGroup, id);
    const stepTypePathParam = `${getStepLabel(stepType)}s`;
    const meta = {
      flowId,
      stepType: stepTypePathParam,
      stepGroupId: id,
    };
    const api = yield getContext('api');
    yield call(api.updateStepGroup, meta, delta);
    yield put(flush(id));
    yield put(completeInteraction(UPDATE_STEP_GROUP));
  } catch (error) {
    yield put(reject(error));
  }
}

function* removeStepGroup(action) {
  try {
    const {
      payload: { id },
    } = action;

    const { id: flowId } = yield select(selectFlow);
    const { stepType } = yield select(selectStepGroup, id);
    const stepTypePathParam = `${getStepLabel(stepType)}s`;
    const meta = {
      flowId,
      stepType: stepTypePathParam,
      stepGroupId: id,
    };
    const api = yield getContext('api');
    yield call(api.deleteStepGroup, meta);
    yield put(completeInteraction(REMOVE_STEP_GROUP));
    const { stepGroup } = yield select(selectSelected);
    yield call(ensureLocation, stepGroup);
  } catch (error) {
    // TODO: Consider reverting the soft delete on error
    yield put(reject(error));
  }
}

function* addActionGroup(action) {
  try {
    const index = yield select(selectNextGroupIndex);
    const { stepGroup: stepGroupId } = yield select(selectSelected);
    const { id: flowId, previewUrl: baseUrl } = yield select(selectFlow);
    const previewUrl = yield select(selectPreviewUrl, stepGroupId);

    // Since `previewUrl` can be either a path (if pulled from the step group
    // action) or a full URL (if pulled from the flow object), normalize the URL
    // where navigation started by using the flow's preview URL as the base
    const { origin, href: source } = new URL(previewUrl, baseUrl);

    // Normalize the destination URL as well to handle any minor discrepancies
    // such as trailing slashes
    const { href: destination } = new URL(action.payload, baseUrl);

    // If the current location (destination) is the same as the starting
    // location of when navigate started (source), then return early since the
    // user is back to where they started and no action step is required.
    // Additionally, ensure that current location (destination) is still in the
    // same origin as the flow's preview URL. Otherwise return early as the user
    // likely navigated across domains.
    if (source === destination || !destination.startsWith(origin)) {
      yield put(completeInteraction(ADD_ACTION_STEP_GROUP));
      return;
    }

    // The preview URL for the action step group is only the path, so remove the
    // origin from the current location (destination)
    const buildUrl = destination.replace(origin, '');
    const stepGroup = yield call(getDefaults, {
      index,
      stepType: ACTION,
      previewUrl: buildUrl,
      params: { url: buildUrl },
    });
    const stepGroupTypePathParam = `${getStepLabel(ACTION)}s`;

    const api = yield getContext('api');
    const response = yield call(api.createStepGroup, {
      flowId,
      group: {
        index,
        stepGroups: [
          {
            stepGroup,
            stepGroupTypePathParam,
          },
        ],
      },
    });
    yield put(insert(transform(response), flowId));
    yield put(completeInteraction(ADD_ACTION_STEP_GROUP));
  } catch (error) {
    // TODO: what does this error MEAN
    yield put(reject(error));
  }
}

function* updateSkippable(action) {
  try {
    const {
      payload: { id, delta },
    } = action;

    yield put(attemptInteraction(UPDATE_STEP_GROUP));
    yield put(update(id, delta));

    const { formatVersion } = yield select(selectFlow);

    if (formatVersion === 2) {
      const { steps } = yield select(selectStepGroup, id);
      const stepChildren = yield select(selectStepChildren);
      const parseStepChild =
        delta.skippable && delta.skippableDisplayType !== 'exit-symbol'
          ? addDismissLink
          : removeDismissLink;

      yield all(
        steps
          .flatMap(stepId => {
            const [needsUpdate, child] = parseStepChild(stepChildren[stepId]);

            if (needsUpdate) {
              return [
                put(attemptInteraction(UPDATE_STEP_CHILD)),
                put(updateStepChild(child.id, child)),
              ];
            }

            return null;
          })
          .filter(Boolean)
      );
    }
  } catch (error) {
    yield put(reject(error));
  }
}

// Remove backdrop and backdropSolidEdge settings when the target is in an iframe
function* targetSelector(action) {
  const { payload } = action;
  const selected = yield select(selectSelected);
  const isTargetInIframe =
    payload?.selector?.includes(IFRAME_ROOT_TOKEN) || false;

  if (isTargetInIframe && selected) {
    const { stepGroup } = selected;
    yield put(update(stepGroup, { backdrop: false, backdropSolidEdge: false }));
  }
}

export default function* stepGroupsSaga() {
  yield takeEvery(movePatterns.move, moveStepChild);
  yield takeLatest(patterns.add, addStepGroup);
  yield takeEvery(patterns.update, flushStepGroup);
  yield takeEvery(patterns.remove, removeStepGroup);
  yield takeEvery(NAVIGATE_EXITED, addActionGroup);
  yield takeLatest(UPDATE_SKIPPABLE, updateSkippable);
  yield takeEvery(SELECTOR_REQUEST_COMPLETED, targetSelector);
}
