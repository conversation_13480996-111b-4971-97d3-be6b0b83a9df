import {
  composeReducers,
  createCollectionReducer,
  resolveSiblings,
  moveChildren,
  hasMany,
} from 'ext/lib/collections';
import { STEP_TYPES_LABELS } from './constants';

const TYPE = 'stepGroups';

const reducer = composeReducers(
  hasMany('steps', 'stepChildren'),
  moveChildren('steps', TYPE),
  createCollectionReducer(TYPE)
);

export default resolveSiblings(reducer, TYPE);

export const selectStepGroups = state => state[TYPE];
export const selectStepGroup = (state, id) => state[TYPE][id];

export const getStepLabel = type => STEP_TYPES_LABELS[type] || type;
