import {
  MODAL,
  SLIDEOUT,
  HOTSPOT,
  TOOLTIP,
  ACTION,
  STEP_TYPES_TO_LEGACY_TYPES,
} from './constants';

export default function getDefaults({ stepType, ...props }) {
  const legacyStepType = STEP_TYPES_TO_LEGACY_TYPES[stepType];

  switch (stepType) {
    case MODAL:
      return {
        stepType,
        legacyStepType,
        patternType: stepType,
        backdrop: true,
        sandbox: true,
        skippable: true,
        steps: [],
        ...props,
      };
    case SLIDEOUT:
      return {
        stepType,
        patternType: stepType,
        legacyStepType,
        backdrop: false,
        position: 'bottomLeft',
        sandbox: true,
        skippable: true,
        ...props,
      };
    case HOTSPOT:
      return {
        stepType,
        patternType: stepType,
        legacyStepType,
        sequential: false,
        skippable: true,
        backdrop: false,
        ...props,
      };
    case TOOLTIP:
      return {
        stepType,
        patternType: stepType,
        legacyStepType,
        sequential: true,
        skippable: true,
        skippableDisplayType: 'text',
        beaconStyle: 'hidden',
        backdrop: false,
        backdropSolidEdge: false,
        backdropSolidEdgeOpacity: 0.5,
        ...props,
      };
    case ACTION:
      return {
        stepType,
        legacyStepType,
        actionType: 'redirect',
        ...props,
      };
    default:
      return {};
  }
}
