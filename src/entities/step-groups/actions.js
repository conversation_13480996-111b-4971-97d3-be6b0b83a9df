import { createLifecycleFor, createMoveActionsFor } from 'ext/lib/collections';

export const TYPE = 'stepGroups';

export const {
  add,
  create,
  insert,
  update,
  flush,
  reject,
  remove,
  focus,
  patterns,
} = createLifecycleFor(TYPE);

export const { move: moveStepChild, patterns: movePatterns } =
  createMoveActionsFor(TYPE);

export const UPDATE_SKIPPABLE = 'UPDATE_SKIPPABLE';

export const updateSkippable = (id, delta) => {
  return {
    type: UPDATE_SKIPPABLE,
    payload: { id, delta },
  };
};
