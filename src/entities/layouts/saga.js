import { call, getContext, put, takeEvery } from 'redux-saga/effects';
import { INITIALIZE } from 'ext/root/root-actions';
import { resolve, reject } from './actions';

export function* fetchLayouts() {
  try {
    const { getLayouts } = yield getContext('api');
    const { template } = yield call(getLayouts);

    yield put(resolve(template));
  } catch (error) {
    yield put(reject(error));
  }
}

export default function* saga() {
  yield takeEvery(INITIALIZE, fetchLayouts);
}
