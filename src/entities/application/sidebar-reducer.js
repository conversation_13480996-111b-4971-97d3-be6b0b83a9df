import {
  CANCELED,
  NEW_ITEM,
  ITEM_FOCUSED,
  ITEM_INSERTED,
  ITEM_CREATED,
} from 'ext/lib/collections';
import { SELECTOR_REQUEST_CANCELED } from 'ext/lib/targeting-interface';
import { TYPE as STEP_GROUPS } from 'entities/step-groups';
import { TYPE as FLOW } from 'entities/flows';
import { SIDEBAR_MOVED, SIDEBAR_VISIBILITY_TOGGLED } from './sidebar-actions';

const LEFT = 'left';
const RIGHT = 'right';

const initialState = {
  mode: 'edit',
  position: 'right',
  collapsed: true,
  stepType: null,
};

export default function sidebar(state = initialState, action) {
  switch (action.type) {
    case SIDEBAR_MOVED:
      return {
        ...state,
        position: state.position === RIGHT ? LEFT : RIGHT,
      };
    case SIDEBAR_VISIBILITY_TOGGLED:
      return {
        ...state,
        collapsed: !state.collapsed,
      };
    case NEW_ITEM:
      return {
        ...state,
        mode: 'create',
        stepType: action.payload,
        collapsed: false,
      };
    case ITEM_CREATED:
      if (action.meta.type === FLOW) {
        return state;
      }
      return {
        ...state,
        mode: 'wait',
      };
    case ITEM_INSERTED:
      return {
        ...state,
        mode: 'edit',
        stepType: null,
        collapsed: false,
      };
    case CANCELED:
    case SELECTOR_REQUEST_CANCELED:
      return {
        ...state,
        mode: 'edit',
        stepType: null,
      };
    case ITEM_FOCUSED:
      return {
        ...state,
        mode: 'edit',
        stepType: null,
        collapsed:
          action.meta && action.meta.type === STEP_GROUPS
            ? false
            : state.collapsed,
      };
    default:
      return state;
  }
}

export const selectSidebar = state => state.application.sidebar;
