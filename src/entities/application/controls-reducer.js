import { CONTROLS_VISIBILITY_TOGGLED } from './controls-actions';

const initialState = {
  collapsed: false,
};

export default function controls(state = initialState, action) {
  switch (action.type) {
    case CONTROLS_VISIBILITY_TOGGLED:
      return {
        ...state,
        collapsed: !state.collapsed,
      };
    default:
      return state;
  }
}

export const selectControls = state => state.application.controls;
