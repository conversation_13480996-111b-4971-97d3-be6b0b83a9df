import {
  SELECTOR_REQUESTED,
  SELECTOR_REQUEST_CANCELED,
  SELECTOR_REQUEST_COMPLETED,
} from 'ext/lib/targeting-interface';
import { PREVIEW_REQUESTED, PREVIEW_EXITED } from 'lib/preview-mode';
import { NAVIGATE_STARTED, NAVIGATE_EXITED } from 'lib/navigation-mode';
import { WELCOME_EXITED } from './actions';

export default function mode(state = 'edit', action) {
  switch (action.type) {
    case PREVIEW_REQUESTED:
      return 'preview';
    case WELCOME_EXITED:
    case PREVIEW_EXITED:
    case SELECTOR_REQUEST_CANCELED:
    case SELECTOR_REQUEST_COMPLETED:
    case NAVIGATE_EXITED:
      return 'edit';
    case SELECTOR_REQUESTED:
      return 'target';
    case NAVIGATE_STARTED:
      return 'navigate';
    default:
      return state;
  }
}

export const selectMode = state => state.mode;
