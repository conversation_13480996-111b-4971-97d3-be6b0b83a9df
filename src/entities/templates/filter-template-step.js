// This function shares a lot with the filtering steps that occur prior to
// cloning a step child. We should consider sharing this behavior in some kind
// of filter function in `entities/step-children`.
export default function filterTemplateStep(step) {
  const {
    createdAt,
    createdBy,
    index,
    id,
    stepNumber,
    updatedAt,
    updatedBy,
    ...templateStep
  } = step;
  return templateStep;
}
