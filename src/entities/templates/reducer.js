import {
  createCollectionReducer,
  resolveSiblings,
  DELETED,
} from 'ext/lib/collections';

const TYPE = 'templates';

const reducer = createCollectionReducer(TYPE);

export default resolveSiblings(reducer, TYPE);

export const selectTemplates = (state, type) =>
  Object.values(state.templates).filter(
    // NOTE: It would be nice to automatically filter out soft-deleted entities
    //       like this rather than having to do it for every selector...
    ({ stepType, [DELETED]: deleted }) => stepType === type && !deleted
  );
