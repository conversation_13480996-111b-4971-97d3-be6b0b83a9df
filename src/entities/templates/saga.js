import { call, getContext, put, takeEvery } from 'redux-saga/effects';
import { completeInteraction } from 'ext/lib/track';
import {
  CREATE_TEMPLATE,
  UPDATE_TEMPLATE,
  DELETE_TEMPLATE,
} from 'lib/metrics/interaction-types';
import { insert, reject, flush, patterns } from './actions';

function* createTemplate({ payload: { name, step, stepGroupId, stepType } }) {
  try {
    const api = yield getContext('api');

    const response = yield call(api.createTemplate, {
      createdAt: Date.now(),
      createdFromStepChildId: step.id,
      createdFromStepId: stepGroupId,
      name,
      step,
      stepType,
    });
    yield put(insert(response));
    yield put(completeInteraction(CREATE_TEMPLATE));
  } catch (error) {
    yield put(reject(error));
  }
}

function* updateTemplate({ payload: { id, delta } }) {
  try {
    const api = yield getContext('api');
    yield call(api.updateTemplate, {
      templateId: id,
      template: delta,
    });
    yield put(flush(id));
    yield put(completeInteraction(UPDATE_TEMPLATE));
  } catch (error) {
    yield put(reject(error));
  }
}

function* deleteTemplate({ payload: { id } }) {
  try {
    const api = yield getContext('api');
    yield call(api.deleteTemplate, id);
    yield put(completeInteraction(DELETE_TEMPLATE));
  } catch (error) {
    yield put(reject(error));
  }
}

export default function* templatesSaga() {
  yield takeEvery(patterns.create, createTemplate);
  yield takeEvery(patterns.update, updateTemplate);
  yield takeEvery(patterns.remove, deleteTemplate);
}
