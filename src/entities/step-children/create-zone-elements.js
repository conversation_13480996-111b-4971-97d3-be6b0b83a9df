import { v4 as uuid } from 'uuid';
import { EDITOR_TYPES } from 'vendor/wysiwyg/helpers/constants';

const createButtonZone = persistedState => {
  return {
    id: uuid(),
    type: 'Button',
    persistedState,
  };
};

/**
 * Creates a Button Zone with default settings appropriate for modals and
 * slideouts.
 *
 * @param  {object} settings Button settings that will be set under the
 *                           `persistedState` of the Zone object. These include:
 *                             - buttonActionType
 *                             - buttonText
 *                             - buttonType
 *                             - textAlign
 * @return {object}          A Zone object for the Button type
 */
export const createModalButtonZone = ({
  buttonActionType,
  buttonText,
  buttonType,
  textAlign,
}) => {
  return createButtonZone({
    buttonActionType,
    buttonText,
    buttonType,
    textAlign,
    marginTop: 25,
    marginBottom: 0,
  });
};

/**
 * Creates a Button Zone with default settings appropriate for hotspots and
 * tooltips.
 *
 * @param  {object} settings Button settings that will be set under the
 *                           `persistedState` of the Zone object. These include:
 *                             - buttonActionType
 *                             - buttonText
 *                             - buttonType
 *                             - textAlign
 * @return {object}          A Zone object for the Button type
 */
export const createTooltipButtonZone = ({
  buttonActionType,
  buttonText,
  buttonType,
  textAlign,
}) => {
  return createButtonZone({
    buttonActionType,
    buttonText,
    buttonType,
    textAlign,
    fontSize: 12,
    marginTop: 5,
    marginBottom: 5,
  });
};

/**
 * Create a Zone representing the Dismiss Link in hotspots and tooltips.
 *
 * @param  {object} settings Settings that will be set under the
 *                           `persistedState` of the Zone object. These include:
 *                             - skipText
 * @return {object}          A Zone object for the Dismiss Link
 */
export const createDismissLinkZone = ({ skipText, persistedState }) => {
  return {
    id: uuid(),
    type: EDITOR_TYPES.DISMISS_LINK,
    persistedState: {
      marginTop: 10,
      ...persistedState,
      content: `⊘ ${skipText}`,
    },
  };
};
