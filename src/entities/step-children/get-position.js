import { put, race, take } from 'redux-saga/effects';
import {
  requestSelector,
  SELECTOR_REQUEST_COMPLETED,
  SELECTOR_REQUEST_CANCELED,
  getParentPadding,
} from 'ext/lib/targeting-interface';

// Half the width of a hotspot/tooltip beacon, used to offset the calculated
// position of the hotspot/tooltip.
const BEACON_RADIUS = 12;

export default function* getPosition(stepChild) {
  yield put(requestSelector());

  const [result, cancel] = yield race([
    take(SELECTOR_REQUEST_COMPLETED),
    take(SELECTOR_REQUEST_CANCELED),
  ]);

  if (
    cancel ||
    !result.payload.selector ||
    !result.payload.element ||
    !stepChild
  ) {
    return null;
  }

  const { selector, elementAttributes, element, clientX, clientY } =
    result.payload;

  // The padding of the parent element is used to calculate the relative
  const padding = getParentPadding(element);
  const {
    borderTopLeftRadius,
    borderTopRightRadius,
    borderBottomRightRadius,
    borderBottomLeftRadius,
  } = window.getComputedStyle(element);

  // Defines the border radius for the backdrop surrounding a tooltip that
  // belongs to a tooltip group with the `backdrop` and `backdropSolidEdge`
  // fields set to true. We calculate this when the element is selected so that
  // the value exists if the user enables the `backdrop` and `backdropSolidEdge`
  // settings.
  const borderRadius =
    Math.min(
      Number.parseInt(borderTopLeftRadius, 10),
      Number.parseInt(borderTopRightRadius, 10),
      Number.parseInt(borderBottomRightRadius, 10),
      Number.parseInt(borderBottomLeftRadius, 10)
    ) || 0;

  const { left, top, width, height } = element.getBoundingClientRect();

  // Calculates the relative position within the target element taking into
  // account the point that was clicked relative to the viewport (`clientX` and
  // `clientY`) and the top-left corner of the target element relative to the
  // viewport (from `getBoundingClientRect()`). We need to offset the value by
  // half the width of the hotspot/tooltip beacon, since the SDK renders the
  // hotspot/tooltip with the top-left corner set to the position calculated
  // using `offsetXPercentage` and `offsetYPercentage`.
  const offsetXPercentage =
    (clientX - left - padding.left - BEACON_RADIUS) / width;
  const offsetYPercentage =
    (clientY - top - padding.top - BEACON_RADIUS) / height;

  // `autoSelector` - Used by the builder to remember the auto-generated
  //   selector for an element. This allows us to retain the selector after the
  //   user toggles to manually building a selector. This way, if the user
  //   toggles back to "auto", we still have the selector to use.
  // `includeOrderedMatchSelector` - Used by the builder to determine if the
  //   "By Order" filter option when building a manual selector is checked. If
  //   this is true, then there should be a valid value in the
  //   `selectorOrderValue` field.
  // `includeTextMatchSelector` - Used by the builder to determine if the "By
  //   Text" filter option when building a manual selector is checked. If this
  //   is true, then there should be a valid value in the `selectorTextValue`
  //   field.
  // `selectorOptions` - The set of element attributes used to construct the
  //   list of options presented when manually building a selector. These are
  //   maintained as a separate field so the builder can display this list as
  //   the user toggles different options on or off.
  // `selectorOrderValue` - Added to the `selectorSettings` object if the user
  //   chooses the "Filter By Order" option (see `includeOrderedMatchSelector`).
  //   Defaults to null since the targeting interface generates a selector that
  //   captures a single element and there's no order value we need to store.
  // `selectorSettings` - An object containing a CSS selector under the
  //   `selector` key and potentially `text_filter` and `order_filter` fields
  //   that are set from the `selectorTextValue` and `selectorOrderValue`
  //   values. This is used to store a more complex selector than can be
  //   captured by just the `selector` string value.
  // `selectorTextValue` - Added to the `selectorSettings` object if the user
  //   chooses the "Filter By Text" option (see `includeTextMatchSelector`). We
  //   capture this value when the target element is selected, so the value
  //   is available to use and isn't lost while the user is using the manual
  //   positioning settings to potentially change the element.
  // `wasPositionedByTargetingInterface` - Used to remember if the user got this
  //   selector by using the targeting interface or not (i.e. manually built the
  //   selector). Determines if the Auto or Manual placement toggle is selected
  //   in the Settings panel.
  const positionDetails = {
    autoSelector: selector,
    backdropSolidEdgeBorderRadius: borderRadius,
    includeOrderedMatchSelector: false,
    includeTextMatchSelector: false,
    offsetXPercentage,
    offsetYPercentage,
    selector,
    selectorOptions: elementAttributes,
    selectorOrderValue: null,
    selectorSettings: { selector },
    selectorTextValue: element.innerText
      ? element.innerText.replace(/\r\n|\r|\n/g, ' ').trim()
      : '',
    wasPositionedByTargetingInterface: true,
  };

  const { uiConditions } = stepChild;
  if (uiConditions && uiConditions.next) {
    // If a tooltip is configured with a `uiConditions.next` value, we call it
    // "action driven", because advancement of the tooltip occurs when the
    // target element is clicked. We store the CSS selector for the element to
    // click separately from the `selector` field of the tooltip itself, since
    // they technically do not need to be exactly the same element. So we set
    // selector generated here in the "conditions object" under
    // `uiConditions.next.params.selector` so the SDK can attach the event
    // listener to the appropriate element when live.
    return {
      ...positionDetails,
      uiConditions: {
        ...uiConditions,
        next: {
          ...uiConditions.next,
          params: {
            ...uiConditions.next.params,
            selector: [{ selector }],
          },
        },
      },
    };
  }

  return positionDetails;
}
