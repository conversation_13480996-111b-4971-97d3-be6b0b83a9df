import {
  call,
  getContext,
  put,
  race,
  select,
  take,
  takeEvery,
  takeLatest,
  debounce,
} from 'redux-saga/effects';
import { v4 as uuid } from 'uuid';
import {
  completeInteraction,
  INTERACTION_COMPLETED,
  INTERACTION_ATTEMPTED,
} from 'ext/lib/track';
import {
  ADD_STEP_CHILD,
  UPDATE_STEP_CHILD,
  REMOVE_STEP_CHILD,
  CLONE_STEP_CHILD,
  TARGET_SELECTOR as TARGET_SELECTOR_INTERACTION,
} from 'lib/metrics/interaction-types';
import { addDismissLink } from 'lib/toggle-dismiss-link';
import {
  CONTROLS_VISIBILITY_TOGGLED,
  SIDEBAR_MOVED,
  SIDEBAR_VISIBILITY_TOGGLED,
} from 'entities/application';
import {
  selectStepGroup,
  getStepLabel,
  HOTSPOT,
  TOOLTIP,
} from 'entities/step-groups';
import { selectFlow } from 'entities/flows';
import { selectParents } from 'lib/selectors';
import { ensureLocation } from 'lib/location';
import { updateSurveyZoneIds } from 'lib/zones';
import {
  patterns,
  flush,
  insert,
  reject,
  update,
  STEP_CHILD_CLONED,
  TARGET_SELECTOR,
} from './actions';
import {
  createModalButtonZone,
  createTooltipButtonZone,
} from './create-zone-elements';
import { selectStepChild } from './reducer';
import getPosition from './get-position';

const applicationActions = new Set([
  CONTROLS_VISIBILITY_TOGGLED,
  SIDEBAR_MOVED,
  SIDEBAR_VISIBILITY_TOGGLED,
  INTERACTION_COMPLETED,
  INTERACTION_ATTEMPTED,
]);

const cloneableFields = new Set([
  // common
  'content',
  'rows',
  'nextText',

  // modal/slideout
  'actionsHidden',
  'isButtonCentered',
  'nextButtonHidden',
  'prevButtonHidden',
  'prevText',

  // tooltip/hotspot
  'backdropSolidEdgeXPadding',
  'backdropSolidEdgeYPadding',
  'content',
  'hideNextButton',
  'skipText',
  'tooltipAlignment',
]);

function* createStepChild(stepType, base) {
  let step = { ...base };
  if (stepType === HOTSPOT || stepType === TOOLTIP) {
    const positionFields = yield call(getPosition, step);

    if (positionFields === null) {
      yield put(completeInteraction(ADD_STEP_CHILD));
      return;
    }

    step = { ...step, ...positionFields };
  }

  const api = yield getContext('api');
  const { flowId, stepGroupId } = yield select(selectParents);
  const stepTypePathParam = `${getStepLabel(stepType)}s`;
  const response = yield call(api.createStepChild, {
    flowId,
    stepGroupId,
    stepType: stepTypePathParam,
    step,
  });

  yield put(insert(response, stepGroupId));
}

function* addStepChild({ payload: stepType }) {
  try {
    const [create, abort] = yield race([
      take(patterns.create),
      // Take any non-application action
      take(action => !applicationActions.has(action.type)),
    ]);

    if (abort) {
      yield put(completeInteraction(ADD_STEP_CHILD));
      return;
    }

    const { payload } = create;
    const { fromTemplate } = payload;
    let { step } = payload;

    const isScratchStep = !step.rows || step.rows.length === 0;

    const { stepGroupId } = yield select(selectParents);
    const { steps, skippable, skippableDisplayType } = yield select(
      selectStepGroup,
      stepGroupId
    );
    const { formatVersion } = yield select(selectFlow);
    const newRow = {
      id: uuid(),
      zones: [],
    };

    if (fromTemplate) {
      step.rows = updateSurveyZoneIds(step.rows);
    }

    const hasNextBtn = step.rows?.some(row =>
      row.zones?.some(zone => zone.persistedState.buttonActionType === 'next')
    );

    const shouldAddNewRow =
      formatVersion === 2 &&
      steps.length > 0 &&
      !isScratchStep &&
      !hasNextBtn &&
      !fromTemplate;

    if (shouldAddNewRow && stepType !== HOTSPOT) {
      if (stepType === TOOLTIP) {
        newRow.zones.push(
          createTooltipButtonZone({
            buttonText: 'Next',
            buttonActionType: 'next',
            buttonType: 'primary',
            textAlign: 'right',
          })
        );
      } else {
        newRow.zones.push(
          createModalButtonZone({
            buttonText: 'Back',
            buttonActionType: 'prev',
            buttonType: 'secondary',
            textAlign: 'left',
          }),
          createModalButtonZone({
            buttonText: 'Next',
            buttonActionType: 'next',
            buttonType: 'primary',
            textAlign: 'right',
          })
        );
      }

      step = {
        ...step,
        rows: [...(step.rows || []), newRow],
      };
    }

    if (
      shouldAddNewRow &&
      (stepType === HOTSPOT ||
        (stepType === TOOLTIP && skippableDisplayType === 'text')) &&
      skippable
    ) {
      const [, newStep] = addDismissLink(step);

      step = newStep;
    }

    yield call(createStepChild, stepType, step);
    yield put(completeInteraction(ADD_STEP_CHILD));
  } catch {
    // TODO: what does this error MEAN
  }
}

function* cloneStepChild(action) {
  try {
    const {
      payload: { id },
    } = action;

    const { stepGroupId } = yield select(selectParents);
    const { stepType } = yield select(selectStepGroup, stepGroupId);
    const stepChild = yield select(selectStepChild, id);

    const base = Object.entries(stepChild).reduce((acc, [field, value]) => {
      if (cloneableFields.has(field)) {
        acc[field] = value;
      }

      if (field === 'rows') {
        acc[field] = updateSurveyZoneIds(value);
      }

      return acc;
    }, {});

    yield call(createStepChild, stepType, base);
    yield put(completeInteraction(CLONE_STEP_CHILD));
  } catch {
    // TODO: what does this error MEAN
  }
}

function* flushStepChild(action) {
  try {
    const {
      payload: { id, delta },
    } = action;

    const { flowId, stepGroupId } = yield select(selectParents);
    const { stepType } = yield select(selectStepGroup, stepGroupId);
    const stepTypePathParam = `${getStepLabel(stepType)}s`;
    const meta = {
      flowId,
      stepType: stepTypePathParam,
      stepGroupId,
      stepChildId: id,
    };
    const api = yield getContext('api');
    const step = yield call(api.updateStepChild, meta, delta);
    yield put(flush(id, step));
    yield put(completeInteraction(UPDATE_STEP_CHILD));
  } catch (error) {
    yield put(reject(error));
  }
}

function* removeStepChild(action) {
  try {
    const {
      payload: { id },
    } = action;

    const { flowId, stepGroupId } = yield select(selectParents);
    const { stepType } = yield select(selectStepGroup, stepGroupId);
    const stepTypePathParam = `${getStepLabel(stepType)}s`;
    const meta = {
      flowId,
      stepType: stepTypePathParam,
      stepGroupId,
      stepChildId: id,
    };
    const api = yield getContext('api');
    yield call(api.deleteStepChild, meta);
    yield put(completeInteraction(REMOVE_STEP_CHILD));
  } catch (error) {
    yield put(reject(error));
  }
}

function* navigateOnSelect({ payload: { parent } }) {
  try {
    yield call(ensureLocation, parent);
  } catch {
    // invariant?
  }
}

function* targetSelector(action) {
  try {
    const {
      payload: { id },
    } = action;

    const stepChild = yield select(selectStepChild, id);
    const positionFields = yield call(getPosition, stepChild);

    if (positionFields === null) {
      yield put(completeInteraction(TARGET_SELECTOR_INTERACTION));
      return;
    }

    yield put(update(id, positionFields));
    yield put(completeInteraction(TARGET_SELECTOR_INTERACTION));
  } catch (error) {
    // TODO: Consider reverting the soft delete on error
    yield put(reject(error));
  }
}

export default function* stepChildrenSaga() {
  yield takeLatest(patterns.add, addStepChild);
  yield debounce(100, patterns.update, flushStepChild);
  yield takeEvery(patterns.remove, removeStepChild);
  yield takeEvery(STEP_CHILD_CLONED, cloneStepChild);
  yield takeEvery(TARGET_SELECTOR, targetSelector);
  yield takeEvery(patterns.focus, navigateOnSelect);
}
