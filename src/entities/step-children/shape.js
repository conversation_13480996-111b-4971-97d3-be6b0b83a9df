import PropTypes from 'prop-types';

/**
 * NOTE: These types may not fully represent every possible value on step
 *       children, rather they are being updated as they are encounted by
 *       components. For example, additional tooltip/hotspot specific props can
 *       be found in the <TargetInterface />.
 */

const scalarOrArrayOf = type =>
  PropTypes.oneOfType([type, PropTypes.arrayOf(type)]);

const zoneShape = PropTypes.shape({
  id: PropTypes.string,
  persistedSstate: PropTypes.shape({
    content: PropTypes.string,
  }),
  // TODO: change to PropTypes.oneOf
  type: PropTypes.string,
});

const modalSpecificProps = {
  actionsHidden: PropTypes.bool,
  isButtonCentered: PropTypes.bool,
  nextButtonHidden: PropTypes.bool,
  prevButtonHidden: PropTypes.bool,
  prevText: PropTypes.string,
};

const tooltipSpecificProps = {
  autoSelector: PropTypes.string,
  backdropSolidEdgeBorderRadius: PropTypes.number,
  backdropSolidEdgeXPadding: PropTypes.number,
  backdropSolidEdgeYPadding: PropTypes.number,
  hideNextButton: PropTypes.bool,
  includeOrderedMatchSelector: PropTypes.bool,
  includeTextMatchSelector: PropTypes.bool,
  offsetXPercentage: PropTypes.number,
  offsetYPercentage: PropTypes.number,
  selector: PropTypes.string,
  selectorOptions: PropTypes.shape({
    alt: scalarOrArrayOf(PropTypes.string),
    class: scalarOrArrayOf(PropTypes.string),
    href: scalarOrArrayOf(PropTypes.string),
    id: scalarOrArrayOf(PropTypes.string),
    name: scalarOrArrayOf(PropTypes.string),
    placeholder: scalarOrArrayOf(PropTypes.string),
    src: scalarOrArrayOf(PropTypes.string),
    tagName: scalarOrArrayOf(PropTypes.string),
    title: scalarOrArrayOf(PropTypes.string),
    type: scalarOrArrayOf(PropTypes.string),
  }),
  selectorOrderValue: PropTypes.number,
  selectorSettings: PropTypes.shape({
    order_filter: PropTypes.number,
    selector: PropTypes.string,
    text_filter: PropTypes.string,
  }),
  selectorTextValue: PropTypes.string,
  skipText: PropTypes.string,
  tooltipAlignment: PropTypes.oneOf([
    'bottom-right',
    'bottom',
    'bottom-left',
    'left-bottom',
    'left',
    'left-top',
    'top-left',
    'top',
    'top-right',
    'right-top',
    'right',
    'right-bottom',
  ]),
  uiConditions: PropTypes.shape({
    next: PropTypes.shape({
      type: PropTypes.oneOf(['WAIT_FOR_MOUSE_EVENT']),
      params: PropTypes.shape({
        event: PropTypes.oneOf(['click']),
        selector: PropTypes.oneOfType([
          PropTypes.string,
          PropTypes.arrayOf(
            PropTypes.shape({
              order_filter: PropTypes.number,
              selector: PropTypes.string,
              text_filter: PropTypes.string,
            })
          ),
        ]),
      }),
    }),
  }),
  wasPositionedByTargetingInterface: PropTypes.bool,
  zIndexOverride: PropTypes.number,
};

export default PropTypes.shape({
  content: PropTypes.string,
  id: PropTypes.string,
  nextText: PropTypes.string,
  rows: PropTypes.arrayOf(
    PropTypes.shape({
      id: PropTypes.string,
      zones: PropTypes.arrayOf(zoneShape),
    })
  ),
  ...modalSpecificProps,
  ...tooltipSpecificProps,
});

export const defaultContent = PropTypes.shape({
  html: PropTypes.string,
  nextText: PropTypes.string,
  prevText: PropTypes.string,
  skipText: PropTypes.string,
});
