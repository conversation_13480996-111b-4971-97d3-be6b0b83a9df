import React, { useRef } from 'react';
import PropTypes from 'prop-types';
import { IFrame } from 'ext/components/ui';
import variables from 'ext/styles/variables.css';
import useAnalytics from 'ext/lib/hooks/use-analytics';
import { themeShape } from 'ext/entities/themes';
import { userPropertiesShape } from 'ext/entities/user-properties';
import { stepGroupShape } from 'entities/step-groups';
import { stepChildShape, defaultContentShape } from 'entities/step-children';
import { flowShape } from 'entities/flows';
import Wysiwyg from 'vendor/wysiwyg';
import {
  AppcuesLayer,
  Beacon,
  EditorFrame,
  ExitTooltipButton,
  GlobalStyling,
  HardBackdrop,
  InjectableStyles,
  SoftBackdrop,
  TooltipActions,
  TooltipContainer,
  TooltipWrapper,
} from './views';
import DefaultContent from './DefaultContent';
import useLayout from './use-tooltip-layout';
import useWysiwygHovering from './use-wysiwyg-hovering';
import { handler } from './stubs';

/*
 * NOTE:
 *   - current editor polls for CSS loaded to prevent FOUC
 *   - Rather than using the beacon alert in the editor frame, consider
 *     providing positioning alerts e.g. invalid selector, missing element, and
 *     out-of-bounds in the sidebar, bottombar, or even as a toast
 */

const EDITOR_TYPES = ['Image', 'RichText', 'HTML', 'Emoji'];

const TooltipEditor = ({
  accountId,
  defaultContent,
  disabled,
  fullscreen,
  onSave,
  onTarget,
  stepGroup,
  stepChild,
  theme = {},
  userId,
  formatVersion,
  flows,
  flow,
  userProperties,
}) => {
  const $tooltip = useRef();
  const [hovering, setHovering] = useWysiwygHovering();
  const { track } = useAnalytics();

  const {
    backdrop,
    backdropSolidEdge,
    backdropSolidEdgeOpacity,
    beaconStyle,
    skippable,
    skippableDisplayType,
    stepType,
    steps,
  } = stepGroup;
  const {
    backdropSolidEdgeBorderRadius,
    backdropSolidEdgeXPadding,
    backdropSolidEdgeYPadding,
    id,
    offsetXPercentage,
    offsetYPercentage,
    rows,
    selectorSettings,
    tooltipAlignment,
    uiConditions,
  } = stepChild;
  const { globalHotspotAnimation, globalBeaconColor, globalBeaconStyle } =
    theme;

  const handleSave = delta => {
    if (formatVersion === 2) {
      // If "next step" button is added to step and uiConditions.next exists (should be null for progression via button),
      // update uiConditions on the step child
      if (delta.content.includes(`data-step="next"`) && uiConditions.next) {
        onSave({
          ...delta,
          uiConditions: {
            next: null,
          },
        });
        return;
      }

      // If all "next step" buttons are removed from step and uiConditions.next does not exist (should exist for progression via element click),
      // update uiConditions on the step child
      if (!delta.content.includes(`data-step="next"`) && !uiConditions.next) {
        onSave({
          ...delta,
          uiConditions: {
            next: {
              type: 'WAIT_FOR_MOUSE_EVENT',
              params: {
                event: 'click',
                selector: [selectorSettings],
              },
            },
          },
        });
        return;
      }
    }

    onSave(delta);
  };

  const { nextText, skipText, html } = defaultContent || stepChild;

  const type = beaconStyle || globalBeaconStyle || 'hotspot';

  const [layout, onLoad] = useLayout($tooltip, {
    id,
    offsetXPercentage,
    offsetYPercentage,
    selectorSettings,
    tooltipAlignment,
  });

  const { alignment, coordinates, dimensions, position, visibility } = layout;
  const { fixed } = position;

  return (
    <IFrame $fullscreen={fullscreen} onLoad={onLoad}>
      {context => (
        <EditorFrame>
          <InjectableStyles type="container" />

          <AppcuesLayer style={{ visibility }}>
            <Beacon
              alignment={alignment}
              animation={globalHotspotAnimation}
              color={globalBeaconColor}
              coordinates={coordinates}
              fixed={fixed}
              onClick={onTarget}
              type={type}
            />

            {backdrop && backdropSolidEdge && (
              <HardBackdrop
                borderRadius={backdropSolidEdgeBorderRadius}
                opacity={backdropSolidEdgeOpacity}
                position={position}
                xPadding={backdropSolidEdgeXPadding}
                yPadding={backdropSolidEdgeYPadding}
              />
            )}

            {backdrop && !backdropSolidEdge && (
              <SoftBackdrop position={position} />
            )}

            <TooltipContainer
              alignment={alignment}
              coordinates={coordinates}
              dimensions={dimensions}
              fixed={fixed}
            >
              <InjectableStyles type="tooltip" />

              <GlobalStyling styles={variables.toString()} />
              <GlobalStyling styles={theme && theme.globalStyling} />

              <TooltipWrapper
                alignment={alignment}
                onMouseEnter={() => setHovering(true)}
                onMouseLeave={() => setHovering(false)}
                ref={$tooltip}
                key={id}
              >
                {skippable && skippableDisplayType === 'exit-symbol' && (
                  <ExitTooltipButton />
                )}

                {disabled ? (
                  <DefaultContent html={html} />
                ) : (
                  <Wysiwyg
                    allowedEditorTypes={
                      formatVersion === 2
                        ? [...EDITOR_TYPES, 'Button']
                        : EDITOR_TYPES
                    }
                    basePadding={11}
                    cloudinary={{ accountId, userId }}
                    disabled={disabled}
                    isHoveringOverContainer={hovering}
                    onEditEnd={handler('onEditEnd')}
                    onEditStart={handler('onEditStart')}
                    onEditorMenuClose={handler('onEditorMenuClose')}
                    onEditorMenuOpen={handler('onEditorMenuOpen')}
                    onSave={handleSave}
                    patternType={stepType}
                    resetShouldCloseMenu={handler('resetShouldCloseMenu')}
                    rows={rows}
                    shouldCloseMenu={false}
                    stepChild={id}
                    theme={theme}
                    track={(title, properties = {}) =>
                      track(title, { ...properties, source: 'WYSIWYG' })
                    }
                    userProperties={userProperties}
                    window={context.window}
                    flows={flows}
                    flow={flow}
                    numPages={steps.length}
                  />
                )}
                {formatVersion !== 2 && (
                  <TooltipActions
                    disabled={disabled}
                    nextText={nextText}
                    onSave={onSave}
                    skipText={skipText}
                    skippable={skippable}
                    skippableDisplayType={skippableDisplayType}
                    stepType={stepType}
                    uiConditions={uiConditions}
                  />
                )}
              </TooltipWrapper>
            </TooltipContainer>
          </AppcuesLayer>
        </EditorFrame>
      )}
    </IFrame>
  );
};

TooltipEditor.propTypes = {
  accountId: PropTypes.string,
  defaultContent: defaultContentShape,
  disabled: PropTypes.bool,
  fullscreen: PropTypes.bool,
  onSave: PropTypes.func,
  onTarget: PropTypes.func,
  stepGroup: stepGroupShape,
  stepChild: stepChildShape,
  theme: themeShape,
  userId: PropTypes.string,
  userProperties: userPropertiesShape,
  formatVersion: PropTypes.number,
  flows: PropTypes.objectOf(
    PropTypes.shape({
      id: PropTypes.string,
      name: PropTypes.string,
    })
  ),
  flow: flowShape,
};

export default TooltipEditor;
