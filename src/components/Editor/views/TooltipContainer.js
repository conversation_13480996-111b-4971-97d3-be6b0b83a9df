import React, { useMemo } from 'react';
import PropTypes from 'prop-types';
import {
  BEACON_WIDTH_PX,
  ARROW_WIDTH_PX,
  ARROW_OFFSET,
  TOOLTIP_BORDER_WIDTH_PX,
  TOOLTIP_SHADOW_WIDTH_PX,
} from './constants';
import shapes from './shapes';

/**
 * Get the container positioning styles holding the tooltip
 *
 * @param {object} options
 * @param {string} options.alignment - Alignment of target element
 * @param {Coordinates} options.coordinates - (x,y) coordinates of target
 * @param {Dimensions} options.dimensions - Tooltip dimensions
 * @return {object} Positioning styles for tooltip container
 */
const getContainerPosition = ({ alignment, coordinates, dimensions }) => {
  const { x, y } = coordinates;
  const { height = 0, width = 0 } = dimensions;

  const styles = {};

  // For non-center-aligned tooltips we need to calculate the distance from the
  // edge of the beacon container to the edge of the container so we can align
  // the arrow correctly. This is composed of the border and box-shadow widths,
  // plus half the width of the arrow, plus whatever offset we want for the
  // arrow from the edge of the tooltip. Lastly we subtract half the width of
  // the beacon to get the distance from the edge of the beacon. Most of these
  // values are constants defined in our SASS files: - arrowWidth:      2 *
  // $arrowSize - borderWidth:     $panelBorderWidth - boxShadowWidth:
  // $panelXShadowWidth - arrowOffset:     $arrowOffset
  const offset =
    TOOLTIP_BORDER_WIDTH_PX +
    TOOLTIP_SHADOW_WIDTH_PX +
    ARROW_WIDTH_PX / 2 +
    ARROW_OFFSET -
    BEACON_WIDTH_PX / 2;

  // If we're center-aligned, set the left position of the tooltip container
  // such that it accounts for the half the beacon width and half the tooltip
  // width.
  if (alignment === 'top' || alignment === 'bottom') {
    styles.left = `${x + BEACON_WIDTH_PX / 2 - width / 2}px`;
  }

  // if we're vertical-aligned, set the top position of the tooltip container
  // such that it accounts for half the tooltip height
  if (alignment === 'left' || alignment === 'right') {
    styles.top = `${
      y - (height - BEACON_WIDTH_PX - TOOLTIP_SHADOW_WIDTH_PX) / 2
    }px`;
  }

  // If we're left-aligned, set the left position such that we move the tooltip
  // to the left for the entire width of the tooltip from the origin point of
  // our beacon, then back right the full width of the beacon plus the
  // container offset distance, so that the arrow lines up.
  if (alignment.indexOf('left') > 0) {
    styles.left = `${x - width + offset + BEACON_WIDTH_PX}px`;
  }

  // If we're right-aligned, set the left position such that it's the origin
  // point of the beacon moved to the left by the container offset distance.
  else if (alignment.indexOf('right') > 0) {
    styles.left = `${x - offset}px`;
  }

  // If we're left positioned, we need to want to set the top position such that
  // we move the tooltip up from the beacon origin, accounting for half the
  // height of the tooltip, excluding half the height of the beacon. We also
  // wnat to move the tooltip right the length of the tooltip.
  else if (alignment.startsWith('left')) {
    styles.left = `${x - width}px`;
  }

  // If we're right positioned, we need to want to set the top position such
  // that we move the tooltip up from the beacon origin, accounting for half
  // the height of the tooltip, excluding half the height of the beacon. We
  // also wnat to move the tooltip right the length of the beacon.
  else if (alignment.startsWith('right')) {
    styles.left = `${x + BEACON_WIDTH_PX}px`;
  }

  // If we're top-aligned, set the top position such that we move the tooltip up
  // the height of the tooltip, excluding the height of the tooltip and the
  // container offset eg. right-top, left-top
  if (alignment.indexOf('top') > 0) {
    styles.top = `${y - height + offset + BEACON_WIDTH_PX}px`;
  }

  // If we're bottom-aligned, set the top position such that we move the tooltip
  // up half the height of the beacon, taking into account the offset created
  // by the tooltip shadow. eg. right-bottom, left-bottom
  else if (alignment.indexOf('bottom') > 0) {
    styles.top = `${y - offset + TOOLTIP_SHADOW_WIDTH_PX}px`;
  }

  // If we're top-positioned, we need to want to set the top position such that
  // we move the tooltip up from the beacon origin, accounting for the full
  // height of the tooltip's container.
  else if (alignment.startsWith('top')) {
    styles.top = `${y - height}px`;
  }

  // If we're bottom-positioned, we want to set the top position such that we
  // move the tooltip down from the beacon origin point to account for the
  // size of the beacon.
  else if (alignment.startsWith('bottom')) {
    styles.top = `${y + BEACON_WIDTH_PX}px`;
  }

  return styles;
};

const TooltipContainer = ({
  alignment,
  children,
  coordinates,
  dimensions,
  fixed,
}) => {
  const styles = useMemo(() => {
    return {
      position: fixed ? 'fixed' : 'absolute',
      ...getContainerPosition({ alignment, coordinates, dimensions }),
      ...dimensions,
    };
  }, [alignment, coordinates, dimensions, fixed]);

  return (
    <div
      className={`appcues-tooltip-container align-${alignment}`}
      role="alertdialog"
      style={styles}
    >
      {children}
    </div>
  );
};

TooltipContainer.propTypes = {
  alignment: shapes.alignment,
  children: PropTypes.node,
  coordinates: shapes.coordinates,
  dimensions: shapes.dimensions,
  fixed: PropTypes.bool,
};

export default TooltipContainer;
