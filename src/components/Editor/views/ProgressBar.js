import React from 'react';
import PropTypes from 'prop-types';

const ProgressBar = ({ currentStep, numberOfSteps, progress }) => (
  // NOTE: Global styling caused progress bar to be hidden despite
  //       `isProgressBarHidden` so force visibility
  <div className="appcues-progress" style={{ display: 'block' }}>
    <div
      className="appcues-progress-bar appcues-progress-bar-success"
      role="progressbar"
      aria-label="current step"
      aria-valuemin={1}
      aria-valuemax={numberOfSteps}
      aria-valuenow={currentStep}
      aria-valuetext={`You are on step ${currentStep} of ${numberOfSteps}`}
      style={{ width: `${progress}%` }}
    />
  </div>
);

ProgressBar.propTypes = {
  currentStep: PropTypes.number,
  numberOfSteps: PropTypes.number,
  progress: PropTypes.number,
};

export default ProgressBar;
