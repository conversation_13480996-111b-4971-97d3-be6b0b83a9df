import React, { useMemo } from 'react';
import PropTypes from 'prop-types';
import { Tooltip } from 'ext/components/ui';
import { BEACON_WIDTH_PX } from './constants';
import shapes from './shapes';

const Hotspot = ({ animation, color }) => (
  <g stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
    <circle className="beacon-inner" fill={color} cx="12" cy="12" r="6" />
    <circle
      className={`beacon-outer ${animation}`}
      stroke={color}
      strokeWidth="2"
      cx="12"
      cy="12"
      r="11"
    />
  </g>
);

Hotspot.propTypes = {
  animation: PropTypes.string,
  color: PropTypes.string,
};

const QuestionMark = ({ color }) => (
  <g
    stroke="none"
    strokeWidth="1"
    fill="none"
    fillRule="evenodd"
    transform="translate(3, 3)"
  >
    <circle fill={color} cx="9" cy="9" r="9" />
    <path
      d="M9.8 9.7L9.8 10.7C9.8 11.1 9.5 11.5 9 11.5L9 11.5C8.6 11.5 8.2 11.1 8.2 10.7L8.2 9.1C8.2 8.7 8.5 8.3 8.9 8.3L9 8.2C10.7 7.9 11.4 7.4 11.4 6.6 11.4 5.8 10.3 5 9 5 7.7 5 6.7 5.7 6.6 6.5 6.6 7 6.2 7.3 5.7 7.3 5.3 7.2 4.9 6.8 5 6.4 5.1 4.7 6.9 3.4 9 3.4 11.2 3.4 13.1 4.8 13.1 6.6 13.1 8.2 12 9.2 9.8 9.7L9.8 9.7Z"
      fill="#ffffff"
    />
    <path
      d="M9.6 14.5C9.4 14.6 9.2 14.7 9 14.7 8.8 14.7 8.6 14.6 8.5 14.5 8.3 14.3 8.2 14.1 8.2 13.9 8.2 13.7 8.3 13.5 8.5 13.3 8.8 13 9.3 13 9.6 13.3 9.7 13.5 9.8 13.7 9.8 13.9 9.8 14.1 9.7 14.3 9.6 14.5L9.6 14.5Z"
      fill="#ffffff"
    />
  </g>
);

QuestionMark.propTypes = {
  color: PropTypes.string,
};

const Retarget = () => (
  <g>
    <line
      x1="12"
      y1="6"
      x2="12"
      y2="18"
      stroke="#15313F"
      strokeWidth="2px"
      strokeOpacity="0.5"
    />
    <line
      x1="6"
      y1="12"
      x2="18"
      y2="12"
      stroke="#15313F"
      strokeWidth="2px"
      strokeOpacity="0.5"
    />
  </g>
);

const Beacon = ({
  alignment,
  animation = 'hotspot-animation-none',
  // this color should not be updated, since it is the default color used when no theme is selected
  // SDK ref:
  // https://github.com/appcues/javascript-sdk/blob/6b1130245932f96cb9e320066b2d5ff73c552913/src/scripts/views/components/hotspot.jsx#L63
  color = '#ff765c',
  coordinates,
  fixed,
  onClick,
  type,
}) => {
  const styles = useMemo(
    () => ({
      position: fixed ? 'fixed' : 'absolute',
      top: `${coordinates.y}px`,
      left: `${coordinates.x}px`,
    }),
    [fixed, coordinates]
  );

  /**
   * NOTE: To match the current builder's behavior, if the content
   * tooltip/hotspot is aligned to the top, position the retargeting tooltip to
   * the bottom. Otherwise show the retargeting tooltip at the top. If needed in
   * the future, we can make it mirror the actual content tooltip/hotspot for
   * all positions. Additionally, while in-builder tooltips _should_ be attached
   * from the parent, the absolute positioning of this component makes that a
   * little tough with the current tooltip implementation so we are attaching it
   * here.
   */
  const placement = /top/.test(alignment) ? 'bottom' : 'top';

  return (
    <Tooltip label="Click to reposition" placement={placement}>
      <div className="beacon-container" style={styles}>
        <svg
          className="beacon"
          viewBox="0 0 24 24"
          height={`${BEACON_WIDTH_PX}px`}
          width={`${BEACON_WIDTH_PX}px`}
          onClick={onClick}
        >
          {type === 'question' && <QuestionMark color={color} />}

          {type === 'hotspot' && (
            <Hotspot animation={animation} color={color} />
          )}

          {type === 'hidden' && <Retarget />}
        </svg>
      </div>
    </Tooltip>
  );
};

Beacon.propTypes = {
  alignment: shapes.alignment,
  animation: PropTypes.string,
  color: PropTypes.string,
  coordinates: shapes.coordinates,
  fixed: PropTypes.bool,
  onClick: PropTypes.func,
  type: PropTypes.oneOf(['hidden', 'hotspot', 'question']),
};

export default Beacon;
