import styled from 'styled-components';

/**
 * NOTE: Consolidated styles overrides for both modal and tooltip editors
 */

export default styled.div`
  && {
    /** UNKNOWN OVERRIDES */

    .wysiwyg-container {
      color: inherit;
      font-family: unset;
      height: 100%;
      line-height: normal;

      .canvas {
        height: 100%;

        .align-left {
          text-align: left;
        }
        .align-center {
          text-align: center;
        }
        .align-right {
          text-align: right;
        }
      }

      blockquote,
      dl,
      ol,
      pre,
      span,
      table,
      ul,
      *:not(.DraftEditor-root) div {
        line-height: inherit;
        font-size: inherit;
      }
    }

    appcues {
      overflow: visible;
      position: relative;

      &[data-pattern-type='shorty'],
      &[data-pattern-type='slideout'] {
        overflow: hidden;
      }

      input.appcues-button {
        font-weight: var(--bold);
        font-size: var(--regular);
        text-align: center;
        border: none;
        cursor: text;
      }
    }

    appcues,
    appcues-container {
      transition: none;

      :focus {
        outline: none;
      }
    }

    .tooltip {
      * {
        box-sizing: border-box;
      }

      .content .panel {
        box-sizing: content-box;
      }
    }
  }

  /** KNOWN OVERRIDES */

  /**
   * Live flows use a 'p' tag to render normal style text which include a bottom
   * margin. However when editing, a 'div' is used instead so that style is not
   * applied resulting in a shift in the editing block upon focus. Ideally when
   * editing, a 'p' tag should be used to better align with live flow styles and
   * prevent the need for this particular override. Note that this also only
   * targets modals/slideouts as tooltips do not seem to have this bottom margin
   * style...
   */
  appcues div[data-block='true'] {
    margin-bottom: 15px;
  }
`;
