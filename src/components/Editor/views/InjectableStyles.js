import React from 'react';
import PropTypes from 'prop-types';

import modalCss from '@appcues/javascript-sdk/dist/modal.css';
import tooltipCss from '@appcues/javascript-sdk/dist/tooltip.css';
import containerCSS from '@appcues/javascript-sdk/dist/container.css';
import modalLegacyCss from '@appcues/javascript-sdk/dist/modal-step-legacy-render.css';

const stylesheets = {
  'modal-step-legacy-render': modalLegacyCss,
  modal: modalCss,
  tooltip: tooltipCss,
  container: containerCSS,
};

const InjectableStyles = ({ type }) => {
  return <style>{stylesheets[type]}</style>;
};

InjectableStyles.propTypes = {
  type: PropTypes.oneOf([
    'container',
    'modal',
    'tooltip',
    'modal-step-legacy-render',
  ]),
};

export default InjectableStyles;
