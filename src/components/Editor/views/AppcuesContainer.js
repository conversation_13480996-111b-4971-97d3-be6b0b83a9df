import React from 'react';
import PropTypes from 'prop-types';

const AppcuesContainer = ({
  backdrop,
  children,
  patternType,
  position,
  style,
}) => (
  <appcues-container
    class={backdrop ? 'appcues-fullscreen' : ''}
    data-pattern-type={patternType}
    data-position={position}
    tabindex="0"
    style={style}
  >
    {children}
  </appcues-container>
);

AppcuesContainer.propTypes = {
  backdrop: PropTypes.bool,
  children: PropTypes.node,
  patternType: PropTypes.oneOf(['fullscreen', 'left', 'modal', 'shorty']),
  position: PropTypes.oneOf([
    'top',
    'topRight',
    'right',
    'bottomRight',
    'bottom',
    'bottomLeft',
    'left',
    'topLeft',
    'center',
  ]),
  style: PropTypes.objectOf(PropTypes.string),
};

export default AppcuesContainer;
