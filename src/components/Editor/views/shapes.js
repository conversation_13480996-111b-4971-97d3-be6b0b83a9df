import PropTypes from 'prop-types';

export default {
  /**
   * Tooltip alignment relative to target
   */
  alignment: PropTypes.oneOf([
    'top-left',
    'top',
    'top-right',
    'right-top',
    'right',
    'right-bottom',
    'bottom-right',
    'bottom',
    'bottom-left',
    'left-bottom',
    'left',
    'left-top',
  ]),

  /**
   * Tooltip dimensions
   */
  dimensions: PropTypes.shape({
    height: PropTypes.number,
    width: PropTypes.number,
  }),

  /**
   * Target (x,y) coordinates
   */
  coordinates: PropTypes.shape({
    x: PropTypes.number,
    y: PropTypes.number,
  }),

  /**
   * Tooltip/beacon positioning details
   */
  position: PropTypes.shape({
    boundingRect: PropTypes.shape({
      bottom: PropTypes.number,
      left: PropTypes.number,
      right: PropTypes.number,
      top: PropTypes.number,
    }),
    fixed: PropTypes.bool,
    viewport: PropTypes.shape({
      height: PropTypes.number,
      width: PropTypes.number,
    }),
  }),
};
