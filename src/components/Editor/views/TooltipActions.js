import React from 'react';
import PropTypes from 'prop-types';
import { HOTSPOT, TOOLTIP } from 'entities/step-groups';
import ContentEditable from './ContentEditable';

export default function TooltipActions({
  disabled,
  nextText,
  onSave,
  skipText,
  skippable,
  skippableDisplayType,
  stepType,
  uiConditions,
}) {
  const isActionDriven =
    uiConditions &&
    uiConditions.next &&
    uiConditions.next.type === 'WAIT_FOR_MOUSE_EVENT';

  const handleSkipChange = ({ target: { textContent } }) => {
    if (textContent !== skipText) {
      onSave({ skipText: textContent });
    }
  };

  const handleNextChange = ({ target: { textContent } }) => {
    if (textContent !== nextText) {
      onSave({ nextText: textContent });
    }
  };

  return (
    <div className="panel-content panel-content-actions">
      {skippable && (!skippableDisplayType || skippableDisplayType === 'text') && (
        <div className="appcues-actions-left">
          <small role="button" tabIndex="0" className="text-muted appcues-skip">
            <span aria-hidden>⊘&nbsp;</span>

            <ContentEditable
              disabled={disabled}
              defaultValue={skipText}
              onBlur={handleSkipChange}
            />
          </small>
        </div>
      )}

      {stepType === TOOLTIP && !isActionDriven && (
        <div className="appcues-actions-right">
          {/* eslint-disable-next-line jsx-a11y/anchor-is-valid */}
          <a
            className="appcues-button appcues-button-success"
            disabled={disabled}
          >
            <ContentEditable
              defaultValue={nextText}
              onBlur={handleNextChange}
            />
          </a>
        </div>
      )}
    </div>
  );
}

TooltipActions.propTypes = {
  disabled: PropTypes.bool,
  nextText: PropTypes.string,
  onSave: PropTypes.func,
  skipText: PropTypes.string,
  skippable: PropTypes.bool,
  skippableDisplayType: PropTypes.string,
  stepType: PropTypes.oneOf([HOTSPOT, TOOLTIP]),
  uiConditions: PropTypes.shape({
    next: PropTypes.shape({
      type: PropTypes.string,
    }),
  }),
};
