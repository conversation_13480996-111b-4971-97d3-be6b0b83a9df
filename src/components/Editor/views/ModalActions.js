import React from 'react';
import PropTypes from 'prop-types';
import ContentEditable from './ContentEditable';

export default function ModalActions({
  buttonRowHidden,
  disabled,
  index,
  isButtonCentered,
  nextButtonHidden,
  nextText,
  onSave,
  prevButtonHidden,
  prevText,
}) {
  const handlePrevChange = ({ target: { value } }) => {
    if (value !== prevText) {
      onSave({ prevText: value });
    }
  };

  const handleNextChange = ({ target: { value } }) => {
    if (value !== nextText) {
      onSave({ nextText: value });
    }
  };

  return (
    <div className={`appcues-actions ${buttonRowHidden ? 'hidden' : ''}`}>
      {/* previous button */}
      {!prevButtonHidden && !isButtonCentered && (
        <div
          className={`appcues-actions-left ${
            nextButtonHidden ? 'appcues-actions-full-row' : ''
          }`}
        >
          {index !== 0 && (
            // eslint-disable-next-line jsx-a11y/anchor-is-valid
            <a
              className="appcues-button"
              data-step="prev"
              disabled={disabled}
              type="button"
            >
              <ContentEditable
                defaultValue={prevText}
                onBlur={handlePrevChange}
              />
            </a>
          )}
        </div>
      )}

      {/* next button */}
      {!nextButtonHidden && (
        <div
          className={`appcues-actions-right ${
            isButtonCentered ? 'appcues-actions-align-center' : ''
          } ${
            isButtonCentered || prevButtonHidden
              ? 'appcues-actions-full-row'
              : ''
          }`}
        >
          {/* eslint-disable-next-line jsx-a11y/anchor-is-valid */}
          <a
            className="appcues-button appcues-button-success"
            data-step="next"
            disabled={disabled}
            type="button"
          >
            <ContentEditable
              defaultValue={nextText}
              onBlur={handleNextChange}
            />
          </a>
        </div>
      )}
    </div>
  );
}

ModalActions.propTypes = {
  buttonRowHidden: PropTypes.bool,
  disabled: PropTypes.bool,
  index: PropTypes.number,
  isButtonCentered: PropTypes.bool,
  nextButtonHidden: PropTypes.bool,
  nextText: PropTypes.string,
  onSave: PropTypes.func,
  prevButtonHidden: PropTypes.bool,
  prevText: PropTypes.string,
};
