import React from 'react';
import PropTypes from 'prop-types';

const AppcuesLayer = ({ children, style }) => (
  <appcues-layer role="alert" style={style}>
    <div className="hotspots appcues-hotspots">
      <div className="hotspot">{children}</div>
    </div>
  </appcues-layer>
);

AppcuesLayer.propTypes = {
  children: PropTypes.node,
  style: PropTypes.objectOf(PropTypes.string),
};

export default AppcuesLayer;
