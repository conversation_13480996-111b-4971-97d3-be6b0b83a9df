import React, { useMemo } from 'react';
import PropTypes from 'prop-types';
import shapes from './shapes';

/**
 * NOTE: Adapted from the `javascript-sdk` hotspot component
 */

const getStyles = ({ borderRadius, opacity, position, xPadding, yPadding }) => {
  const { fixed, relativeBoundingRect = {} } = position;

  const {
    body: { scrollTop = 0 } = {},
    documentElement: {
      clientHeight: documentClientHeight,
      clientWidth: documentClientWidth,
      scrollHeight: documentScrollHeight,
      scrollWidth: documentScrollWidth,
    },
  } = document;

  const scrollHeight = Math.max(
    documentScrollHeight,
    documentClientHeight,
    window.innerHeight
  );

  const scrollWidth = Math.max(documentScrollWidth, documentClientWidth);

  const getPanelStyles = ({ top, left, width, height }) => ({
    top: `${top}px`,
    left: `${left}px`,
    width: `${width}px`,
    height: `${height}px`,
    background: `rgba(0,0,0,${opacity})`,
    position: 'absolute',
    pointerEvents:
      height > scrollHeight || width > scrollWidth ? 'none' : 'all',
  });

  const { top = 0, right = 0, bottom = 0, left = 0 } = relativeBoundingRect;
  const height = bottom - top;
  const width = right - left;

  const container = {
    position: fixed ? 'fixed' : 'absolute',
    top: '0',
    left: '0',
    pointerEvents: 'none',
    height: `${scrollHeight}px`,
    width: `${scrollWidth}px`,
  };

  const elementContainer = {
    position: 'absolute',
    top: `${top - yPadding}px`,
    left: `${left - xPadding}px`,
    width: `${width + 2 * xPadding}px`,
    height: `${height + 2 * yPadding}px`,
    overflow: 'hidden',
    pointerEvents: 'none',
  };

  const elementWrapper = {
    position: 'relative',
    width: `${width + 2 * xPadding}px`,
    height: `${height + 2 * yPadding}px`,
    boxShadow: `0px 0px 0px 2000px rgba(0,0,0,${opacity})`,
    borderRadius: `${borderRadius}px`,
    pointerEvents: 'none',
  };

  const topPanel = getPanelStyles({
    top: 0,
    left: left - xPadding,
    width: width + 2 * xPadding,
    height: top < 0 ? 0 : top - yPadding,
  });

  const rightPanel = getPanelStyles({
    top: 0,
    left: right + xPadding,
    width: scrollWidth - (right + xPadding),
    height: scrollHeight + scrollTop,
  });

  const bottomPanel = getPanelStyles({
    top: bottom + yPadding,
    left: left - xPadding,
    width: width + 2 * xPadding,
    height: scrollHeight + scrollTop - (bottom + yPadding),
  });

  const leftPanel = getPanelStyles({
    top: 0,
    left: 0,
    width: left - xPadding,
    height: scrollHeight + scrollTop,
  });

  return {
    container,
    elementContainer,
    elementWrapper,
    topPanel,
    rightPanel,
    bottomPanel,
    leftPanel,
  };
};

const HardBackdrop = ({
  borderRadius,
  opacity,
  position,
  xPadding,
  yPadding,
}) => {
  const styles = useMemo(
    () =>
      getStyles({
        borderRadius,
        opacity,
        position,
        xPadding,
        yPadding,
      }),
    [borderRadius, opacity, position, xPadding, yPadding]
  );

  return (
    <div aria-label="backdrop" style={styles.container}>
      <div aria-label="element" style={styles.elementContainer}>
        <div className="apc-spotlight" style={styles.elementWrapper} />
      </div>

      <div
        aria-label="top-panel"
        className="apc-spotlight"
        style={styles.topPanel}
      />
      <div
        aria-label="right-panel"
        className="apc-spotlight"
        style={styles.rightPanel}
      />
      <div
        aria-label="bottom-panel"
        className="apc-spotlight"
        style={styles.bottomPanel}
      />
      <div
        aria-label="left-panel"
        className="apc-spotlight"
        style={styles.leftPanel}
      />
    </div>
  );
};

HardBackdrop.propTypes = {
  borderRadius: PropTypes.number,
  opacity: PropTypes.number,
  position: shapes.position,
  xPadding: PropTypes.number,
  yPadding: PropTypes.number,
};

export default HardBackdrop;
