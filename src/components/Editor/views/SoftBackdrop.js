import React, { useMemo } from 'react';
import { BACKDROP_OFFSET } from './constants';
import shapes from './shapes';

/**
 * NOTE: Adapted from the `javascript-sdk` hotspot component
 */

const getStyles = ({ boundingRect, fixed }) => {
  const { clientHeight, clientWidth, scrollHeight, scrollWidth } =
    document.documentElement;

  const { top, right, bottom, left } = boundingRect || {};
  const height = bottom - top;
  const width = right - left;

  const container = {
    position: fixed ? 'fixed' : 'absolute',
    pointerEvents: 'none',
    top: '0px',
    left: '0px',
    overflow: 'hidden',
    height: `${Math.max(scrollHeight, clientHeight, window.innerHeight)}px`,
    width: `${Math.max(scrollWidth, clientWidth)}px`,
  };

  const backdrop = {
    position: 'relative',
    top: `${top - BACKDROP_OFFSET}px`,
    left: `${left - BACKDROP_OFFSET}px`,
    height: `${height ? height + 2 * BACKDROP_OFFSET : 0}px`,
    width: `${width ? width + 2 * BACKDROP_OFFSET : 0}px`,
    boxSizing: 'border-box',
    border: `${BACKDROP_OFFSET}px solid transparent`,
    pointerEvents: 'none',
    borderImage:
      'radial-gradient(transparent 2%, rgba(0, 0, 0, 0.67) 28%) 49% 49% 49% 49%',
    boxShadow: '0 0 0 2500px rgba(0, 0, 0, 0.67)',
  };

  return { container, backdrop };
};

const SoftBackdrop = ({ position }) => {
  const styles = useMemo(() => getStyles(position), [position]);

  return (
    <div aria-label="backdrop" style={styles.container}>
      <div className="apc-spotlight" style={styles.backdrop} />
    </div>
  );
};

SoftBackdrop.propTypes = {
  position: shapes.position,
};

export default SoftBackdrop;
