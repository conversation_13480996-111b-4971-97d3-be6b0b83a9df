import React from 'react';
import PropTypes from 'prop-types';

/*
 * NOTE: The Web Component spec highly recommends custom elements to have a tag
 * name that includes a dash e.g. `<x-customelement>` to prevent the possibility
 * of future HTML tag names from colliding. React also assumes this convention
 * and will handle elements with a dash in their tag names as custom elements.
 *
 * Unfortunately, we have a couple elements e.g. `<appcues>` and `<cue>` that do
 * not follow those guidelines so <PERSON><PERSON> complains when we try to use them. To
 * get around this, we can provide a special prop called `is` with the name of
 * the tag and <PERSON><PERSON> will recognize that we want to treat this as a custom
 * element and handle it accordingly.
 *
 * One thing to note is that custom elements DO NOT use the React convention for
 * attribute names, rather the HTML convention so instead of using props like
 * `className` and `tabIndex`, we would use the HTML convention of `class` and
 * `tabindex`
 */

const Appcues = ({ backdrop, children, index, patternType, position }) => (
  <appcues
    is="appcues"
    class={`active ${backdrop ? 'fullscreen' : ''} cue-step-${index}`}
    data-pattern-type={patternType}
    data-position={position}
  >
    {children}
  </appcues>
);

Appcues.propTypes = {
  backdrop: PropTypes.bool,
  children: PropTypes.node,
  index: PropTypes.number,
  patternType: PropTypes.oneOf(['fullscreen', 'left', 'modal', 'shorty']),
  position: PropTypes.oneOf([
    'top',
    'topRight',
    'right',
    'bottomRight',
    'bottom',
    'bottomLeft',
    'left',
    'topLeft',
    'center',
  ]),
};

export default Appcues;
