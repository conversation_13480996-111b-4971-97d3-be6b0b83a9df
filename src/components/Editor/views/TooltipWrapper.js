import React, { forwardRef } from 'react';
import PropTypes from 'prop-types';
import shapes from './shapes';

const TooltipWrapper = forwardRef(
  ({ alignment, children, onMouseEnter, onMouseLeave }, ref) => (
    <div
      className="tooltip"
      onMouseEnter={onMouseEnter}
      onMouseLeave={onMouseLeave}
      ref={ref}
    >
      <div className={`content content-${alignment}`}>
        <div className="panel panel-default">
          <div className="panel-content">{children}</div>
        </div>
      </div>
    </div>
  )
);

TooltipWrapper.propTypes = {
  alignment: shapes.alignment,
  children: PropTypes.node,
  onMouseEnter: PropTypes.func,
  onMouseLeave: PropTypes.func,
};

export default TooltipWrapper;
