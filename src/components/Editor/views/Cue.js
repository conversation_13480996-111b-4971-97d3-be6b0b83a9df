import React from 'react';
import PropTypes from 'prop-types';

/*
 * NOTE:
 *  - See `AppcuesContainer` for disclaimer re: Web Components
 *  - legacy buttons omitted in favor of new button patterns
 */

const Cue = ({
  buttonRowHidden,
  children,
  onMouseEnter,
  onMouseLeave,
  formatVersion,
}) => (
  <cue
    is="cue"
    class={`active 
      ${buttonRowHidden ? 'appcues-actions-hidden' : ''} 
      ${formatVersion === 2 ? 'full-buttons' : ''}
    `}
    onMouseEnter={onMouseEnter}
    onMouseLeave={onMouseLeave}
  >
    {children}
  </cue>
);

Cue.propTypes = {
  buttonRowHidden: PropTypes.bool,
  children: PropTypes.node,
  onMouseEnter: PropTypes.func,
  onMouseLeave: PropTypes.func,
  formatVersion: PropTypes.number,
};

export default Cue;
