import React from 'react';
import PropTypes from 'prop-types';
import { connect } from 'react-redux';
import styled from 'styled-components';
import root from 'react-shadow/styled-components';
import hash from 'object-hash';
import { selectAccountId, selectUserId } from 'ext/entities/account';
import { selectThemeOrDefault, themeShape } from 'ext/entities/themes';
import { selectFlowsSummary } from 'ext/entities/flows-summary';
import {
  userPropertiesShape,
  selectUserProperties,
} from 'ext/entities/user-properties';
import { selectControls } from 'entities/application';
import { selectFlow, flowShape } from 'entities/flows';
import {
  selectStepGroup,
  stepGroupShape,
  MODAL,
  SLIDEOUT,
  HOTSPOT,
  TOOLTIP,
} from 'entities/step-groups';
import {
  selectStepChild,
  stepChildShape,
  defaultContentShape,
  selectDefaultContent,
  update,
  target,
} from 'entities/step-children';
import ModalEditor from './ModalEditor';
import TooltipEditor from './TooltipEditor';

const ShadowMain = styled(root.main)`
  left: 0;
  position: fixed;
  top: 0;

  // NOTE: Because almost everything else implicitly has z-index equal to 0,
  //       setting this to -1 achieves the same result of ensuring things like
  //       the bottom bar and sidebar layer above the editor without having to
  //       edit all of their z-index values.
  z-index: -1;
`;

export const Editor = ({
  accountId,
  disabled = false,
  defaultContent,
  fullscreen,
  onSave,
  onTarget,
  stepGroup,
  stepChild,
  theme,
  userId,
  userProperties,
  formatVersion,
  flows,
  flow,
}) => {
  const { stepType } = stepGroup;
  const { id } = stepChild;

  const isModalType = stepType === MODAL || stepType === SLIDEOUT;
  const isTooltipType = stepType === TOOLTIP || stepType === HOTSPOT;

  /* istanbul ignore next */
  const handleSave = delta => {
    const next = { ...stepChild, ...delta };

    /**
     * NOTE: Due to a quirk in the WYSIWYG triggering `onSave` during the
     *       initial renders, we want to ensure that it does not trigger an
     *       actual save unless there are changes. Additionally, this can help
     *       prevent unneccesary saves if any edits made by the user do not
     *       result in changes to the actual content.
     */
    if (hash(stepChild) !== hash(next)) {
      onSave(id, delta);
    }
  };

  const handleTarget = () => {
    onTarget(id);
  };

  return (
    <ShadowMain id="editor" aria-label="editor">
      {isModalType && (
        <ModalEditor
          accountId={accountId}
          defaultContent={defaultContent}
          disabled={disabled}
          fullscreen={fullscreen}
          onSave={handleSave}
          stepGroup={stepGroup}
          stepChild={stepChild}
          theme={theme}
          userId={userId}
          userProperties={userProperties}
          formatVersion={formatVersion}
          flows={flows}
          flow={flow}
        />
      )}

      {isTooltipType && (
        <TooltipEditor
          accountId={accountId}
          defaultContent={defaultContent}
          disabled={disabled}
          fullscreen={fullscreen}
          onSave={handleSave}
          onTarget={handleTarget}
          stepGroup={stepGroup}
          stepChild={stepChild}
          theme={theme}
          userId={userId}
          userProperties={userProperties}
          formatVersion={formatVersion}
          flows={flows}
          flow={flow}
        />
      )}
    </ShadowMain>
  );
};

Editor.propTypes = {
  accountId: PropTypes.string,
  defaultContent: defaultContentShape,
  disabled: PropTypes.bool,
  fullscreen: PropTypes.bool,
  onSave: PropTypes.func,
  onTarget: PropTypes.func,
  stepGroup: stepGroupShape,
  stepChild: stepChildShape,
  theme: themeShape,
  userId: PropTypes.string,
  userProperties: userPropertiesShape,
  formatVersion: PropTypes.number,
  flows: PropTypes.objectOf(
    PropTypes.shape({
      id: PropTypes.string,
      name: PropTypes.string,
    })
  ),
  flow: flowShape,
};

const mapStateToProps = (state, { selected }) => {
  const flow = selectFlow(state);
  const stepGroup = selectStepGroup(state, selected.stepGroup);
  const { collapsed } = selectControls(state);
  const defaultContent = flow.lockedForLocalization
    ? selectDefaultContent(state, selected.stepChild)
    : null;

  return {
    stepGroup,
    defaultContent,
    accountId: selectAccountId(state),
    disabled: flow.lockedForLocalization,
    formatVersion: flow.formatVersion,
    fullscreen: collapsed,
    stepChild: selectStepChild(state, selected.stepChild),
    theme: selectThemeOrDefault(state, stepGroup.style),
    userId: selectUserId(state),
    userProperties: selectUserProperties(state),
    flows: selectFlowsSummary(state),
    flow,
  };
};

const mapDispatchToProps = {
  onSave: update,
  onTarget: target,
};

export default connect(mapStateToProps, mapDispatchToProps)(Editor);
