import { useState, useLayoutEffect, useMemo } from 'react';
import isEmpty from 'lodash.isempty';
import isEqual from 'lodash.isequal';
import { useLayoutInterval } from 'ext/lib/hooks/use-interval';
import { calculatePositionDetails, calculateRegion } from 'vendor/dom';
import usePrevious from 'lib/hooks/use-previous';

/**
 * Create viewport centered positioning when an error occurs trying to calculate
 * position details for selector
 *
 * @return {object} Centered position details
 */
const getCenteredStyle = () => {
  const { clientHeight: height, clientWidth: width } = document.documentElement;

  const x = width / 2;
  const y = height / 2;

  return {
    boundingRect: { top: y, right: x, bottom: y, left: x },
    fixed: true,
    viewport: { height, width },
  };
};

/**
 * Get the alignment (or edge combo...) of the target element within the
 * viewport dimensions
 *
 * @param {object} options
 * @param {Coordinates} options.coordinates - (x,y) coordinates of target
 * @param {Viewport} options.viewport - Viewport dimensions
 * @return {string} Alignment representation of target element
 */
const getAlignment = ({ coordinates, viewport }) => {
  const { x, y } = coordinates;
  const { height, width } = viewport;

  const { xRegion, yRegion } = calculateRegion(x, y, width, height);

  const edgeX = xRegion === 0 ? 'right' : xRegion === 3 ? 'left' : '';
  const edgeY = yRegion > 1 ? 'top' : 'bottom';

  return edgeX ? `${edgeY}-${edgeX}` : edgeY;
};

/*
 * Get dimensions of tooltip content
 *
 * @param {HTMLElement} $tooltip - Tooltip element
 * @retuen {Dimension} Tooltip content dimensions
 */
const getDimensions = $tooltip => {
  if (!$tooltip) {
    return { height: 0, width: 0 };
  }
  const { height, width } = $tooltip.getBoundingClientRect();
  return { height, width };
};

/**
 * Calculate positioning details for step child
 *
 * @param {Partial<StepChild>} child - Step child positioning information
 * @return {Positioning} Positioning information for step child
 */
const getPositioning = ({
  offsetXPercentage,
  offsetYPercentage,
  selectorSettings,
  tooltipAlignment,
}) => {
  const details = calculatePositionDetails(null, selectorSettings);

  // TODO: Propagate and notify editor on error
  const position = details.error ? getCenteredStyle() : details;

  const { boundingRect, viewport } = position;
  const { top, right, bottom, left } = boundingRect;

  const { iframeParent, padding } = details;
  const { scrollX, scrollY } =
    details.fixed || iframeParent ? { scrollX: 0, scrollY: 0 } : window;
  const { left: paddingLeft } = padding || { left: 0 };
  const { top: paddingTop } = padding || { top: 0 };
  const xOffset = offsetXPercentage * (right - left);
  const yOffset = offsetYPercentage * (bottom - top);

  const coordinates = {
    x: left + paddingLeft + xOffset - scrollX,
    y: top + paddingTop + yOffset - scrollY,
  };

  const alignment = tooltipAlignment || getAlignment({ coordinates, viewport });

  return { alignment, coordinates, position };
};

/**
 * Hook to compute layout information such as tooltip content dimensionsand
 * positioning information for step child
 *
 * @param {React.RefObject} tooltip - Ref to tooltip element
 * @param {Partial<StepChild>} child - Step child positioning information
 * @return {Layout} Information on tooltip dimensions and selector positioning
 */
export default (
  tooltip,
  {
    id: stepChild,
    offsetXPercentage,
    offsetYPercentage,
    selectorSettings,
    tooltipAlignment,
  }
) => {
  const { current: $tooltip } = tooltip;

  const initial = {
    alignment: tooltipAlignment || 'bottom',
    coordinates: {},
    dimensions: { height: 0, width: 0 },
    position: {},
  };

  const [layout, setLayout] = useState(initial);
  const [, forceUpdate] = useState(false);

  // Keep track of previous step child ID to allow hook to respond immediately
  // rather than on the next render cycle
  const previous = usePrevious(stepChild);
  const changed = previous !== stepChild;

  // If tooltip positioning has not been calculated or the editor is currently
  // transitioning between different step children, keep the visibility hidden.
  // Otherwise show the tooltip as position has been calculated and prevent FOUC
  const visibility = isEmpty(layout.position) || changed ? 'hidden' : 'visible';

  // Expose handler to force re-render once editor is ready e.g. the modal
  // within the iframe has been rendered
  const handleLoad = () => {
    forceUpdate(true);
  };

  const updateStyles = useMemo(
    () => () => {
      if (!$tooltip) {
        return;
      }

      const updated = {
        dimensions: getDimensions($tooltip),
        ...getPositioning({
          offsetXPercentage,
          offsetYPercentage,
          selectorSettings,
          tooltipAlignment,
        }),
      };

      if (!isEqual(updated, layout)) {
        setLayout(updated);
      }
    },
    [
      $tooltip,
      layout,
      offsetXPercentage,
      offsetYPercentage,
      selectorSettings,
      tooltipAlignment,
    ]
  );

  useLayoutInterval(updateStyles, 200);

  useLayoutEffect(updateStyles);

  return [{ ...layout, visibility }, handleLoad];
};
