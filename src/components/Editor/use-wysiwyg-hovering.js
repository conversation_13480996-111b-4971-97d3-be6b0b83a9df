import { useEffect, useState } from 'react';

/*
 * NOTE: This hook is coupled to the WYSIWYG and not a general hover state hook.
 *       The WYSIWYG requires certain behaviors to use a manually managed hover
 *       state, specifically the horizonal add button. In most cases, the
 *       mouseenter/mouseleave handlers that are bound to the hover state will
 *       work as expected. However, there is an edge case where the add button
 *       row will stay in the hovered state after the add content block menu is
 *       closed due to mouseleave not being called when the menu is clicked.
 *
 *       To resolve this, we attach a global mousedown/click handler on the
 *       document when hovering so that when a click occurs anywhere, it will
 *       reset the hover state as well. This works because the add content block
 *       menu is designed to close on click also so this ensures that whenever
 *       the menu is closed, the hover state is reset as well.
 */

export default function useWysiwygHovering() {
  const [hovering, setHovering] = useState(false);

  // eslint-disable-next-line consistent-return
  useEffect(() => {
    if (hovering) {
      const reset = () => {
        setHovering(false);
      };

      document.addEventListener('mousedown', reset);

      return () => {
        document.removeEventListener('mousedown', reset);
      };
    }
  }, [hovering, setHovering]);

  return [hovering, setHovering];
}
