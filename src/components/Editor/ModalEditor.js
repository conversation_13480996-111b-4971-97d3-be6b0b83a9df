import React from 'react';
import PropTypes from 'prop-types';
import { IFrame } from 'ext/components/ui';
import variables from 'ext/styles/variables.css';
import useAnalytics from 'ext/lib/hooks/use-analytics';
import { themeShape } from 'ext/entities/themes';
import { userPropertiesShape } from 'ext/entities/user-properties';
import { stepGroupShape, SLIDEOUT } from 'entities/step-groups';
import { stepChildShape, defaultContentShape } from 'entities/step-children';
import { flowShape } from 'entities/flows';
import Wysiwyg from 'vendor/wysiwyg';
import {
  Appcues,
  AppcuesContainer,
  ModalBackdrop,
  Content,
  Cue,
  EditorFrame,
  GlobalStyling,
  InjectableStyles,
  ModalActions,
  ModalContainer,
  ProgressBar,
  SkipButton,
} from './views';
import DefaultContent from './DefaultContent';
import useWysiwygHovering from './use-wysiwyg-hovering';
import { handler } from './stubs';

/*
 * NOTE: There is currently FOUC on initial load (including when flows are
 * switched) of this editor due to when the iframe is first loaded and when the
 * external CSS is loaded (I think...). The old builder used to poll for whether
 * this CSS has been loaded which _may_ fix this problem, but it's also somewhat
 * minor and the added complexity of adding the fix at the moment seems
 * unnecessary. That beind said, it is something we should eventually fix once
 * other critical parts have been resolved.
 */

const EDITOR_TYPES = [
  'Image',
  'RichText',
  'Hero',
  'Emoji',
  'Video',
  'HTML',
  'TextAreaInput',
  'TextInput',
  'SelectionField',
  'Rating',
  'Button',
  'MultiSelect',
];

const ModalEditor = ({
  accountId,
  defaultContent,
  disabled,
  fullscreen,
  onSave,
  stepGroup,
  stepChild,
  theme = {},
  userId,
  userProperties,
  formatVersion,
  flows,
  flow,
}) => {
  const { track } = useAnalytics();

  const [hovering, setHovering] = useWysiwygHovering();
  const {
    backdrop,
    isProgressBarHidden,
    patternType,
    position,
    skippable,
    steps,
    stepType,
  } = stepGroup;
  const {
    actionsHidden,
    id,
    isButtonCentered,
    nextButtonHidden,
    prevButtonHidden,
    rows,
  } = stepChild;

  const { nextText, prevText, html } = defaultContent || stepChild;

  const index = steps.indexOf(id);
  const progress = Math.ceil((100 * (index + 1)) / steps.length);

  const size =
    patternType === SLIDEOUT
      ? { height: 'auto', width: 'fit-content' }
      : { height: '100%', width: '100%', marginTop: '32px' };

  const buttonRowHidden =
    actionsHidden ||
    (index === 0 && nextButtonHidden) ||
    (nextButtonHidden && prevButtonHidden);

  return (
    <IFrame $fullscreen={fullscreen}>
      {context => (
        <EditorFrame>
          <InjectableStyles type="container" />

          <AppcuesContainer
            backdrop={backdrop}
            patternType={patternType}
            position={position}
            style={size}
          >
            <InjectableStyles type="modal" />
            {formatVersion !== 2 && (
              <InjectableStyles type="modal-step-legacy-render" />
            )}

            <GlobalStyling styles={variables.toString()} />
            <GlobalStyling styles={theme && theme.globalStyling} />

            {backdrop && <ModalBackdrop patternType={patternType} />}

            <Appcues
              backdrop={backdrop}
              index={index}
              patternType={patternType}
              position={position}
            >
              <ModalContainer backdrop={backdrop}>
                {!isProgressBarHidden && (
                  <ProgressBar
                    currentStep={index + 1}
                    numberOfSteps={steps.length}
                    progress={progress}
                  />
                )}

                {skippable && <SkipButton />}

                <Cue
                  buttonRowHidden={buttonRowHidden}
                  onMouseEnter={() => setHovering(true)}
                  onMouseLeave={() => setHovering(false)}
                  formatVersion={formatVersion}
                >
                  <Content>
                    {disabled ? (
                      <DefaultContent html={html} />
                    ) : (
                      <Wysiwyg
                        allowedEditorTypes={EDITOR_TYPES}
                        basePadding={20}
                        cloudinary={{ accountId, userId }}
                        disabled={disabled}
                        height={size.height}
                        isHoveringOverContainer={hovering}
                        numPages={steps.length}
                        onEditEnd={handler('onEditEnd')}
                        onEditStart={handler('onEditStart')}
                        onEditorMenuClose={handler('onEditorMenuClose')}
                        onEditorMenuOpen={handler('onEditorMenuOpen')}
                        onSave={onSave}
                        patternType={stepType}
                        rows={rows}
                        shouldDisableXSS
                        stepChild={id}
                        theme={theme}
                        track={(title, properties = {}) =>
                          track(title, { ...properties, source: 'WYSIWYG' })
                        }
                        userProperties={userProperties}
                        window={context.window}
                        flows={flows}
                        flow={flow}
                      />
                    )}
                  </Content>
                  {formatVersion !== 2 && (
                    <ModalActions
                      buttonRowHidden={buttonRowHidden}
                      disabled={disabled}
                      index={index}
                      isButtonCentered={isButtonCentered}
                      nextButtonHidden={nextButtonHidden}
                      nextText={nextText}
                      onSave={onSave}
                      prevButtonHidden={prevButtonHidden}
                      prevText={prevText}
                    />
                  )}
                </Cue>
              </ModalContainer>
            </Appcues>
          </AppcuesContainer>
        </EditorFrame>
      )}
    </IFrame>
  );
};

ModalEditor.propTypes = {
  accountId: PropTypes.string,
  disabled: PropTypes.bool,
  defaultContent: defaultContentShape,
  fullscreen: PropTypes.bool,
  onSave: PropTypes.func,
  stepGroup: stepGroupShape,
  stepChild: stepChildShape,
  theme: themeShape,
  userId: PropTypes.string,
  userProperties: userPropertiesShape,
  formatVersion: PropTypes.number,
  flows: PropTypes.objectOf(
    PropTypes.shape({
      id: PropTypes.string,
      name: PropTypes.string,
    })
  ),
  flow: flowShape,
};

export default ModalEditor;
