import styled from 'styled-components';
import { easing } from 'ext/lib/style';

export const Heading = styled.h3`
  color: var(--text-color);
  margin: 0 0 8px;
`;

export const Label = styled.label`
  margin: 0 20px 0 0;
`;

export const Value = styled.span`
  font-family: Menlo, Monaco, Source Code Pro, monospace;
  margin: 0;
  word-break: break-all;
`;

export const Table = styled.div`
  align-items: center;
  display: grid;
  grid-template-columns: auto 1fr;

  ${Label}, ${Value} {
    padding: 4px 0;
    transition: ${easing('color')};
  }
`;

export const Container = styled.section`
  color: var(--text-color);
  font-size: var(--xx-small);
  padding: 24px;
`;
