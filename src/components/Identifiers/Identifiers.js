import React from 'react';
import PropTypes from 'prop-types';
import { Container, Heading, Table, Label, Value } from './styled';

export default function Identifiers({ flow, stepGroup, stepChild }) {
  return (
    <Container aria-label="Identifiers">
      <Heading>Advanced identifiers</Heading>

      <Table>
        {flow && (
          <>
            <Label id="flow-id">Flow ID:</Label>
            <Value aria-labelledby="flow-id">{flow}</Value>
          </>
        )}

        {stepGroup && (
          <>
            <Label id="step-group-id">Step ID:</Label>
            <Value aria-labelledby="step-group-id">{stepGroup}</Value>
          </>
        )}

        {stepChild && (
          <>
            <Label id="step-child-id">Step Child ID:</Label>
            <Value aria-labelledby="step-child-id">{stepChild}</Value>
          </>
        )}
      </Table>
    </Container>
  );
}

Identifiers.propTypes = {
  flow: PropTypes.string,
  stepGroup: PropTypes.string,
  stepChild: PropTypes.string,
};
