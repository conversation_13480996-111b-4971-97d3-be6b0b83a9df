import React from 'react';
import {
  <PERSON><PERSON>,
  ButtonGroup,
  Heading,
  Link,
  Modal,
  ModalHeader,
  MoreInfo,
  P,
  StatusMessage,
} from 'ext/components/ui';
import useToggle from 'ext/lib/hooks/use-toggle';

export default function LocalizedBanner() {
  const [visible, toggle] = useToggle(false);
  return (
    <>
      <StatusMessage aria-label="localized">
        Edit functionality is limited.
        <MoreInfo onClick={toggle}>Learn more</MoreInfo>
      </StatusMessage>

      <Modal onClose={toggle} visible={visible}>
        <ModalHeader>
          <Heading>Edit functionality is limited</Heading>
        </ModalHeader>
        <P>
          Because this flow has been locked for translation, the words in this
          flow cannot be edited. You can still reposition steps and change
          styling options.
        </P>
        <P>
          <Link href="https://docs.appcues.com/article/584-localizing-content">
            Learn more in the localization guide
          </Link>
        </P>
        <ButtonGroup right>
          <Button onClick={toggle}>Cancel</Button>
        </ButtonGroup>
      </Modal>
    </>
  );
}
