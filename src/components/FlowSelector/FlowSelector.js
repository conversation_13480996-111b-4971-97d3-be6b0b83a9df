import React, { forwardRef, useEffect, useMemo, useState } from 'react';
import PropTypes from 'prop-types';
import { connect } from 'react-redux';
import search from 'ext/lib/search';
import { attemptInteraction } from 'ext/lib/track';
import Fade from 'ext/components/Fade';
import { FontIcon, Input, Search } from 'ext/components/ui';
import { selectHasMultipleAccounts } from 'ext/entities/accounts';
import { selectCurrentAccountName } from 'ext/lib/selectors';
import useAnalytics from 'ext/lib/hooks/use-analytics';
import { unload } from 'ext/lib/crx';
import { selectFlowsSummary } from 'ext/entities/flows-summary';
import { sortList } from 'ext/lib/sort-list';
import { CREATE_FLOW } from 'lib/metrics/interaction-types';
import { focus } from 'entities/flows';
import CreateFlowButton from 'components/CreateFlowButton';
import ListItem from './ListItem';
import { FlowListEmpty, NoFlowsFound } from './StatusMessages';
import {
  CloseButton,
  Container,
  Heading,
  List,
  ListWrapper,
  Menu,
  Section,
} from './styled';

/*
 * TODO:
 *    - add loading state and animation
 *    - handle error states
 */
export const FlowSelector = forwardRef(
  (
    {
      flows = {},
      accountName = '',
      onClose,
      onCreate,
      onLoad,
      onSelect,
      recordMetric,
      onQuit,
      selected,
      visible = true,
    },
    ref
  ) => {
    const [query, setQuery] = useState('');
    const { track } = useAnalytics();

    useEffect(() => {
      if (onLoad) {
        onLoad();
      }
    }, [onLoad]);

    const flowsList = Object.values(flows);

    const handleChange = ({ target: { value } }) => {
      setQuery(value);
    };

    const handleSelect = id => {
      track('Builder interaction', {
        name: 'Clicked on Flow',
        component: 'FlowSelector',
      });
      onSelect(id);
      onClose();
    };

    const handleCreate = () => {
      track('Builder view', {
        name: 'Opened Create New Flow Modal',
        component: 'FlowSelector',
      });
      recordMetric(CREATE_FLOW);
      onCreate();
      onClose();
    };

    const processedFlows = useMemo(() => {
      const sorted = sortList(flowsList, selected);

      return search(sorted, 'name', query);
      // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [flows, selected, query]);

    return (
      <Container ref={ref}>
        <Fade from="bottom" visible={visible} onExited={() => setQuery('')}>
          <Menu>
            <Section justify="flex-start">
              <Heading>
                {accountName ? `Flows in ${accountName}` : 'Your Flows'}
              </Heading>
            </Section>

            {flowsList.length > 5 && (
              <Search wrapped>
                <FontIcon icon="search" />
                <Input
                  onChange={handleChange}
                  placeholder="Search your flows..."
                  role="textbox"
                  value={query}
                />
              </Search>
            )}

            <ListWrapper>
              {flowsList.length === 0 && <FlowListEmpty />}

              {flowsList.length > 0 && processedFlows.length === 0 && (
                <NoFlowsFound />
              )}

              {processedFlows.length > 0 && (
                <List role="list">
                  {processedFlows.map(flow => (
                    <ListItem
                      key={flow.id}
                      flow={flow}
                      isSelected={flow.id === selected}
                      onClick={handleSelect}
                    />
                  ))}
                </List>
              )}
            </ListWrapper>

            <CreateFlowButton onClick={handleCreate} />

            <Section justify="flex-end">
              <CloseButton
                onClick={() => {
                  track('Builder interaction', {
                    name: 'Clicked Close Builder',
                    component: 'FlowSelector',
                  });
                  onQuit();
                }}
                role="button"
              >
                Close Builder
              </CloseButton>
            </Section>
          </Menu>
        </Fade>
      </Container>
    );
  }
);

FlowSelector.propTypes = {
  flows: PropTypes.objectOf(
    PropTypes.shape({
      id: PropTypes.string,
      name: PropTypes.string,
    })
  ),
  onClose: PropTypes.func,
  onCreate: PropTypes.func,
  onLoad: PropTypes.func,
  onSelect: PropTypes.func,
  onQuit: PropTypes.func,
  recordMetric: PropTypes.func,
  selected: PropTypes.string,
  visible: PropTypes.bool,
  accountName: PropTypes.string,
};

const mapStateToProps = state => {
  const multiple = selectHasMultipleAccounts(state);
  const name = selectCurrentAccountName(state);

  return {
    flows: selectFlowsSummary(state),
    accountName: multiple ? name : null,
  };
};

const mapDispatchToProps = {
  onQuit: () => unload(),
  onSelect: focus,
  recordMetric: attemptInteraction,
};

const options = { forwardRef: true };

export default connect(
  mapStateToProps,
  mapDispatchToProps,
  null,
  options
)(FlowSelector);
