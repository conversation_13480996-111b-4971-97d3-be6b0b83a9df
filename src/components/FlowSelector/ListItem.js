import React from 'react';
import PropTypes from 'prop-types';
import {
  CurrentBadge,
  FlowName,
  ListItemWrapper,
  StateIndicator,
} from './styled';

const FALLBACK_FLOW_NAME = 'Untitled Flow';

const ListItem = ({ flow, isSelected, onClick }) => {
  const { id, state, name } = flow;

  const handleSelect = () => {
    if (!isSelected) {
      onClick(id);
    }
  };

  return (
    <ListItemWrapper
      isSelected={isSelected}
      onClick={handleSelect}
      role="listitem"
    >
      <FlowName title={name}>{name || FALLBACK_FLOW_NAME}</FlowName>
      <StateIndicator aria-label={state} published={state === 'PUBLISHED'} />
      {isSelected && <CurrentBadge aria-label="current">Current</CurrentBadge>}
    </ListItemWrapper>
  );
};

ListItem.propTypes = {
  flow: PropTypes.shape({
    id: PropTypes.string,
    name: PropTypes.string,
    state: PropTypes.string,
  }),
  isSelected: PropTypes.bool,
  onClick: PropTypes.func,
};

export default ListItem;
