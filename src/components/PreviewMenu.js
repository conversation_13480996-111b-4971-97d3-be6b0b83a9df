import React, { useCallback } from 'react';
import { connect } from 'react-redux';
import PropTypes from 'prop-types';
import useAnalytics from 'ext/lib/hooks/use-analytics';
import {
  FontIcon,
  StepIcon,
  Menu,
  List,
  ListItem,
  ListBody,
  OptionTitle,
  OptionDesc,
} from 'ext/components/ui';
import PreviewManager from 'ext/components/PreviewManager';
import { requestPreview } from 'lib/preview-mode';
import {
  selectStepGroup,
  getStepLabel,
  ACTION,
  STEP_TYPES,
} from 'entities/step-groups';

export function PreviewMenu({ flow, onClick, stepGroup, stepLabel, stepType }) {
  const { track } = useAnalytics();

  const handleClickFlow = useCallback(() => {
    track('Builder interaction', {
      name: 'Clicked Preview Flow',
      component: 'PreviewManager',
    });
    onClick(flow);
  }, [onClick, flow, track]);

  const handleClickStep = useCallback(() => {
    track('Builder interaction', {
      name: 'Clicked Preview Flow Group',
      component: 'PreviewManager',
    });
    onClick(flow, stepGroup);
  }, [onClick, flow, stepGroup, track]);

  return (
    <PreviewManager>
      <Menu>
        <List aria-label="Preview menu">
          <ListItem key="flow" onClick={handleClickFlow}>
            <FontIcon icon="eye" />
            <ListBody>
              <OptionTitle>preview flow</OptionTitle>
              <OptionDesc>
                See the entirety of this flow as a user would
              </OptionDesc>
            </ListBody>
          </ListItem>

          {stepType !== ACTION && (
            <ListItem key="step" onClick={handleClickStep}>
              <StepIcon type={stepType} />
              <ListBody>
                <OptionTitle>preview {stepLabel} group</OptionTitle>
                <OptionDesc>
                  Only view the steps in this {stepLabel} group
                </OptionDesc>
              </ListBody>
            </ListItem>
          )}
        </List>
      </Menu>
    </PreviewManager>
  );
}

PreviewMenu.propTypes = {
  flow: PropTypes.string.isRequired,
  stepGroup: PropTypes.string.isRequired,
  stepLabel: PropTypes.string.isRequired,
  stepType: PropTypes.oneOf(STEP_TYPES).isRequired,
  onClick: PropTypes.func.isRequired,
};

const mapStateToProps = (state, { flow, stepGroup }) => {
  const { stepType } = selectStepGroup(state, stepGroup);
  return {
    flow,
    stepGroup,
    stepType,
    stepLabel: getStepLabel(stepType),
  };
};

export default connect(mapStateToProps, { onClick: requestPreview })(
  PreviewMenu
);
