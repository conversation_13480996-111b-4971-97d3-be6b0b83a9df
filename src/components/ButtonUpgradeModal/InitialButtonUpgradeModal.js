import React from 'react';
import PropTypes from 'prop-types';
import { <PERSON><PERSON>, Link, ModalHeader, P } from 'ext/components/ui';
import BaseButtonUpgradeModal from './BaseButtonUpgradeModal';
import { LEARN_MORE_LINK } from './constants';
import { Disclaimer } from './styled';

function InitialButtonUpgradeModal({ onClose, visible }) {
  return (
    <BaseButtonUpgradeModal onClose={onClose} visible={visible}>
      <ModalHeader>
        <Heading>New more powerful buttons!</Heading>
      </ModalHeader>
      <P>
        This Flow is now using new buttons with more design options, actions,
        and flexibility. <Link href={LEARN_MORE_LINK}>Learn More</Link>
      </P>
      <Disclaimer>
        *You can switch back if you don’t like the new styling on your buttons.
      </Disclaimer>
    </BaseButtonUpgradeModal>
  );
}

InitialButtonUpgradeModal.propTypes = {
  visible: PropTypes.bool,
  onClose: PropTypes.func,
};

export default InitialButtonUpgradeModal;
