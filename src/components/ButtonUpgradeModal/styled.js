import styled from 'styled-components';
import { <PERSON><PERSON>, <PERSON><PERSON>, P } from 'ext/components/ui';

export const Wrapper = styled(Modal)`
  --modal-dialog-width: 500px;
`;

export const Container = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  width: 388px;

  ${P} {
    line-height: 21px;
    margin: 0;
  }

  ${Button} {
    margin-top: 24px;
  }
`;

export const Disclaimer = styled.em`
  font-size: var(--x-small);
  line-height: 150%;
  margin-top: 14px;
`;

export const CloseButton = styled.button`
  position: absolute;
  top: 0px;
  right: 0px;
  padding: 0px;
  margin: 14px;
  font-size: var(--x-large);
  background: transparent;
  border: none;
  cursor: pointer;
  line-height: 0.75em;
`;
