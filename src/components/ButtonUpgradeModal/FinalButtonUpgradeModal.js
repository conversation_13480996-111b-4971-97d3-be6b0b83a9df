import React from 'react';
import PropTypes from 'prop-types';
import { Heading, ModalHeader, P } from 'ext/components/ui';
import BaseButtonUpgradeModal from './BaseButtonUpgradeModal';

function FinalButtonUpgradeModal({ onClose, visible }) {
  return (
    <BaseButtonUpgradeModal onClose={onClose} visible={visible}>
      <ModalHeader>
        <Heading>New buttons added!</Heading>
      </ModalHeader>
      <P>This Flow is now using the newest buttons.</P>
    </BaseButtonUpgradeModal>
  );
}

FinalButtonUpgradeModal.propTypes = {
  visible: PropTypes.bool,
  onClose: PropTypes.func,
};

export default FinalButtonUpgradeModal;
