import React from 'react';
import styled from 'styled-components';

const Container = styled.div``;

export default function TadaIcon() {
  return (
    <Container aria-label="tada icon">
      <svg
        width="160"
        height="160"
        viewBox="0 0 160 160"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M80 160C124.183 160 160 124.183 160 80C160 35.8172 124.183 0 80 0C35.8172 0 0 35.8172 0 80C0 124.183 35.8172 160 80 160Z"
          fill="#F4FBFF"
        />
        <path
          d="M59 138C73.9117 138 86 136.209 86 134C86 131.791 73.9117 130 59 130C44.0883 130 32 131.791 32 134C32 136.209 44.0883 138 59 138Z"
          fill="#CCEDFF"
        />
        <path
          d="M86.6958 103.696C88.3425 103.142 90.1275 103.158 91.7641 103.741L63.7368 66C63.661 67.5304 63.1329 69.004 62.2194 70.2342L34.9159 107.568C31.822 112.706 27.7125 117.376 29.9544 121.113C32.1962 124.849 38.3606 121.113 43.5523 119.187L86.6958 103.696Z"
          fill="url(#paint0_linear)"
        />
        <path
          d="M92.1179 65.3617C92.0335 65.5308 91.9933 65.7185 92.0009 65.9073C92.0086 66.0962 92.0639 66.28 92.1617 66.4417C92.2595 66.6034 92.3967 66.7377 92.5604 66.8321C92.724 66.9265 92.909 66.978 93.0979 66.9817H102.398C102.587 66.9801 102.773 66.9297 102.937 66.8352C103.101 66.7407 103.238 66.6055 103.335 66.4426C103.431 66.2796 103.484 66.0945 103.488 65.9052C103.492 65.7158 103.447 65.5286 103.358 65.3617L98.7179 56.6417C98.6302 56.4503 98.4893 56.288 98.312 56.1742C98.1347 56.0605 97.9285 56 97.7179 56C97.5073 56 97.3011 56.0605 97.1238 56.1742C96.9465 56.288 96.8057 56.4503 96.7179 56.6417L92.1179 65.3617Z"
          fill="var(--lils-guest-bathroom-soaps)"
          stroke="var(--blurple)"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path
          d="M131.024 83.3495C130.892 83.3575 130.764 83.3988 130.652 83.4696C130.541 83.5403 130.449 83.6382 130.385 83.7542C130.321 83.8701 130.287 84.0003 130.288 84.1326C130.288 84.265 130.321 84.3952 130.385 84.511L133.641 90.1512C133.709 90.2655 133.804 90.3605 133.919 90.427C134.034 90.4934 134.164 90.5292 134.296 90.5307C134.429 90.5322 134.56 90.4994 134.676 90.4356C134.792 90.3717 134.89 90.2789 134.96 90.1662L138.624 84.2989C138.709 84.1786 138.758 84.0364 138.765 83.889C138.772 83.7417 138.736 83.5955 138.663 83.4677C138.589 83.34 138.48 83.2361 138.349 83.1684C138.218 83.1008 138.07 83.0721 137.923 83.086L131.024 83.3495Z"
          fill="var(--lils-guest-bathroom-soaps)"
          stroke="var(--blurple)"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path
          d="M51.0243 47.3495C50.8922 47.3575 50.7643 47.3988 50.6525 47.4696C50.5407 47.5403 50.4485 47.6382 50.3847 47.7542C50.3209 47.8701 50.2875 48.0003 50.2875 48.1326C50.2876 48.265 50.3211 48.3952 50.385 48.511L53.6413 54.1512C53.7086 54.2655 53.8043 54.3605 53.9191 54.427C54.0338 54.4934 54.1638 54.5292 54.2964 54.5307C54.4291 54.5322 54.5598 54.4994 54.6761 54.4356C54.7923 54.3717 54.8901 54.2789 54.96 54.1662L58.6237 48.2989C58.7091 48.1786 58.7582 48.0364 58.7651 47.889C58.772 47.7417 58.7365 47.5955 58.6627 47.4677C58.589 47.34 58.4801 47.2361 58.349 47.1684C58.218 47.1008 58.0702 47.0721 57.9234 47.086L51.0243 47.3495Z"
          fill="var(--lils-guest-bathroom-soaps)"
          stroke="var(--blurple)"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path
          d="M65.5147 38C63.8323 37.4985 62.2664 36.6739 60.9063 35.5735C59.5462 34.473 58.4187 33.1183 57.5882 31.5866C56.7577 30.0549 56.2405 28.3764 56.066 26.6469C55.8916 24.9175 56.0635 23.171 56.5718 21.5072C57.4088 18.8317 59.2769 16.5895 61.7721 15.2657C64.2673 13.9419 67.189 13.6428 69.9054 14.4332C70.9842 14.7547 71.9883 15.2836 72.8602 15.9896C73.732 16.6955 74.4545 17.5647 74.9862 18.5473C75.5179 19.5299 75.8484 20.6066 75.9588 21.7157C76.0692 22.8249 75.9572 23.9446 75.6294 25.0109C75.1034 26.731 73.9087 28.1742 72.3076 29.0235C70.7065 29.8728 68.8301 30.0587 67.0905 29.5405C66.402 29.3348 65.7613 28.9968 65.205 28.5458C64.6488 28.0948 64.1879 27.5397 63.8489 26.9123C63.5098 26.2849 63.2992 25.5975 63.2292 24.8895C63.1591 24.1814 63.231 23.4667 63.4406 22.7861C63.6055 22.2412 63.8776 21.7338 64.2414 21.2932C64.6051 20.8526 65.0534 20.4875 65.5603 20.2189C66.0672 19.9502 66.6228 19.7833 67.1951 19.7278C67.7675 19.6722 68.3452 19.7291 68.8953 19.8952C69.3367 20.0248 69.7479 20.2396 70.105 20.5271C70.4621 20.8147 70.7581 21.1693 70.976 21.5706C71.1938 21.9718 71.3292 22.4118 71.3743 22.8651C71.4194 23.3184 71.3734 23.776 71.2388 24.2116C71.021 24.8994 70.5396 25.4756 69.8977 25.8167C69.2558 26.1578 68.5044 26.2368 67.8043 26.0367"
          stroke="var(--blurple)"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path
          d="M122.66 56C121.483 55.6656 120.386 55.1159 119.434 54.3823C118.482 53.6487 117.693 52.7455 117.112 51.7244C116.53 50.7033 116.168 49.5843 116.046 48.4313C115.924 47.2783 116.044 46.114 116.4 45.0048C116.986 43.2211 118.294 41.7263 120.04 40.8438C121.787 39.9613 123.832 39.7619 125.734 40.2888C126.489 40.5031 127.192 40.8557 127.802 41.3264C128.412 41.797 128.918 42.3765 129.29 43.0315C129.663 43.6866 129.894 44.4044 129.971 45.1438C130.048 45.8832 129.97 46.6298 129.741 47.3406C129.372 48.4873 128.536 49.4495 127.415 50.0157C126.295 50.5819 124.981 50.7058 123.763 50.3603C123.281 50.2232 122.833 49.9979 122.444 49.6972C122.054 49.3966 121.732 49.0265 121.494 48.6082C121.257 48.1899 121.109 47.7317 121.06 47.2596C121.011 46.7876 121.062 46.3111 121.208 45.8574C121.324 45.4941 121.514 45.1559 121.769 44.8622C122.024 44.5684 122.337 44.325 122.692 44.1459C123.047 43.9668 123.436 43.8555 123.837 43.8185C124.237 43.7815 124.642 43.8194 125.027 43.9302C125.336 44.0165 125.623 44.1597 125.873 44.3514C126.123 44.5431 126.331 44.7795 126.483 45.0471C126.636 45.3146 126.73 45.6079 126.762 45.9101C126.794 46.2123 126.761 46.5173 126.667 46.8077C126.515 47.2663 126.178 47.6504 125.728 47.8778C125.279 48.1052 124.753 48.1579 124.263 48.0245"
          stroke="var(--blurple)"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path
          d="M112.66 120C111.483 119.666 110.386 119.116 109.434 118.382C108.482 117.649 107.693 116.746 107.112 115.724C106.53 114.703 106.168 113.584 106.046 112.431C105.924 111.278 106.044 110.114 106.4 109.005C106.986 107.221 108.294 105.726 110.04 104.844C111.787 103.961 113.832 103.762 115.734 104.289C116.489 104.503 117.192 104.856 117.802 105.326C118.412 105.797 118.918 106.376 119.29 107.032C119.663 107.687 119.894 108.404 119.971 109.144C120.048 109.883 119.97 110.63 119.741 111.341C119.372 112.487 118.536 113.449 117.415 114.016C116.295 114.582 114.981 114.706 113.763 114.36C113.281 114.223 112.833 113.998 112.444 113.697C112.054 113.397 111.732 113.026 111.494 112.608C111.257 112.19 111.109 111.732 111.06 111.26C111.011 110.788 111.062 110.311 111.208 109.857C111.324 109.494 111.514 109.156 111.769 108.862C112.024 108.568 112.337 108.325 112.692 108.146C113.047 107.967 113.436 107.856 113.837 107.819C114.237 107.781 114.642 107.819 115.027 107.93C115.336 108.017 115.623 108.16 115.873 108.351C116.123 108.543 116.331 108.78 116.483 109.047C116.636 109.315 116.73 109.608 116.762 109.91C116.794 110.212 116.761 110.517 116.667 110.808C116.515 111.266 116.178 111.65 115.728 111.878C115.279 112.105 114.753 112.158 114.263 112.024"
          stroke="var(--blurple)"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path
          d="M79.1675 61.0339L78.4692 59.0322C78.368 58.7422 78.3258 58.4349 78.3453 58.1284C78.3647 57.8218 78.4454 57.5223 78.5824 57.2474C78.7195 56.9726 78.9103 56.7279 79.1435 56.528C79.3766 56.328 79.6475 56.1768 79.94 56.0833L81.8284 55.4245C82.3709 55.192 82.8032 54.7597 83.0359 54.2172C83.2685 53.6748 83.2837 53.0636 83.0783 52.5103L82.4526 50.7163C82.2517 50.1403 82.2878 49.5081 82.553 48.9588C82.8182 48.4095 83.2908 47.988 83.8667 47.7871L85.7551 47.1284C86.0419 47.0312 86.3064 46.8778 86.5333 46.6773C86.7601 46.4767 86.9448 46.233 87.0764 45.9603C87.2081 45.6876 87.2841 45.3915 87.3 45.0891C87.316 44.7867 87.2715 44.4842 87.1693 44.1992L86.5435 42.4052C86.3426 41.8292 86.3787 41.197 86.6439 40.6477C86.9091 40.0984 87.3817 39.6769 87.9576 39.476L89.846 38.8173C90.4188 38.612 90.8879 38.1899 91.1524 37.642C91.4169 37.0941 91.4556 36.4642 91.2602 35.8881L90.6015 33.9997"
          stroke="var(--blurple)"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path
          d="M114 73.3474L114.97 71.4625C115.111 71.1893 115.305 70.9471 115.54 70.7501C115.776 70.553 116.049 70.4052 116.342 70.3152C116.636 70.2253 116.945 70.1951 117.25 70.2265C117.556 70.2578 117.852 70.35 118.121 70.4977L119.899 71.413C120.442 71.6461 121.053 71.6618 121.606 71.4568C122.16 71.2519 122.613 70.842 122.873 70.3119L123.743 68.6226C124.022 68.0802 124.505 67.671 125.086 67.4849C125.667 67.2988 126.298 67.3511 126.84 67.6303L128.619 68.5457C128.887 68.6866 129.18 68.7728 129.482 68.7991C129.783 68.8255 130.087 68.7914 130.376 68.6991C130.664 68.6067 130.931 68.4578 131.161 68.2611C131.391 68.0644 131.58 67.8238 131.716 67.5534L132.586 65.8641C132.865 65.3217 133.348 64.9125 133.929 64.7264C134.51 64.5403 135.141 64.5926 135.684 64.8718L137.462 65.7871C138.005 66.0609 138.634 66.1103 139.214 65.9247C139.793 65.7391 140.276 65.3334 140.559 64.7948L141.475 63.0166"
          stroke="var(--blurple)"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path
          d="M92 94.1636L92.6383 92.9785C92.7307 92.8068 92.8566 92.6553 93.0085 92.5331C93.1604 92.4108 93.3352 92.3201 93.5228 92.2665C93.7103 92.2129 93.9066 92.1974 94.1002 92.2209C94.2938 92.2444 94.4807 92.3065 94.6499 92.4034L95.7679 93.0056C96.1094 93.1599 96.4971 93.1771 96.8509 93.0536C97.2047 92.9301 97.4975 92.6752 97.6686 92.3418L98.2406 91.2798C98.4243 90.9388 98.7359 90.6847 99.1068 90.5735C99.4778 90.4623 99.8778 90.5029 100.219 90.6866L101.337 91.2887C101.505 91.3814 101.69 91.4395 101.882 91.4598C102.073 91.4801 102.266 91.4621 102.45 91.4069C102.634 91.3517 102.806 91.2604 102.954 91.1382C103.103 91.0161 103.225 90.8656 103.315 90.6955L103.887 89.6335C104.071 89.2925 104.382 89.0384 104.753 88.9272C105.124 88.816 105.524 88.8566 105.865 89.0403L106.983 89.6424C107.325 89.8227 107.723 89.8615 108.093 89.7506C108.463 89.6396 108.775 89.3877 108.961 89.0492L109.563 87.9313"
          stroke="var(--blurple)"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path d="M63 71L57 79L64 111L72 108L63 71Z" fill="var(--white)" />
        <path d="M46 92L40 100L45 119L52 116L46 92Z" fill="var(--white)" />
        <path
          d="M86.6965 103.696C88.3432 103.142 90.1283 103.158 91.7649 103.741L63.7376 66C63.6617 67.5304 63.1337 69.004 62.2202 70.2342L34.9167 107.568C31.8229 111.772 27.7125 118.077 29.9543 121.113C32.1962 124.148 39.9534 120.452 43.5531 119.187L86.6965 103.696Z"
          stroke="var(--blurple)"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <defs>
          <linearGradient
            id="paint0_linear"
            x1="29.3125"
            y1="94.3549"
            x2="91.7641"
            y2="94.3549"
            gradientUnits="userSpaceOnUse"
          >
            <stop stopColor="var(--pink-power-ranger)" />
            <stop offset="0.0001" stopColor="var(--pink-power-ranger)" />
            <stop offset="1" stopColor="#8960FF" />
          </linearGradient>
        </defs>
      </svg>
    </Container>
  );
}
