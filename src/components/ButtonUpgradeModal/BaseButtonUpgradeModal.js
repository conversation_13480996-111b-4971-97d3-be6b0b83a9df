import React from 'react';
import PropTypes from 'prop-types';
import { Button } from 'ext/components/ui';
import { Container, CloseButton, Wrapper } from './styled';
import TadaIcon from './TadaIcon';

function BaseButtonUpgradeModal({ onClose, visible = false, children }) {
  return (
    <Wrapper visible={visible} theme="light" wide>
      <Container>
        <TadaIcon />
        {children}
        <Button kind="primary" onClick={onClose}>
          Got It
        </Button>
        <CloseButton aria-label="Close modal" onClick={onClose}>
          &times;
        </CloseButton>
      </Container>
    </Wrapper>
  );
}

BaseButtonUpgradeModal.propTypes = {
  visible: PropTypes.bool,
  onClose: PropTypes.func,
  children: PropTypes.node,
};

export default BaseButtonUpgradeModal;
