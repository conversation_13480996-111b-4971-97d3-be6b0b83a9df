import React, { useState } from 'react';
import PropTypes from 'prop-types';
import {
  Button,
  DebouncedInput,
  FontIcon,
  RadioButton,
  RadioButtonGroup,
  Tabs,
} from 'ext/components/ui';
import useAnalytics from 'ext/lib/hooks/use-analytics';
import AlignmentPicker from 'ext/components/AlignmentPicker';
import SelectorBuilder from 'components/SelectorBuilder';
import SelectorStatus from 'components/SelectorStatus';
import { TOOLTIP } from 'entities/step-groups';
import { stepChildShape } from 'entities/step-children';
import useSelectorValidity from 'lib/hooks/use-selector-validity';
import { Control, ControlGroup, Label } from './styled';

/**
 * TODO:
 *   - Respond to target selector update
 */

const round = (number, precision) => Number(number.toFixed(precision));

const tabs = [
  { label: 'Design', value: 'design' },
  { label: 'Placement', value: 'placement' },
];

export default function HotspotChildSettings({
  onChange,
  onTarget,
  stepChild,
}) {
  const [tab, setTab] = useState('design');
  const { status, count } = useSelectorValidity(stepChild);

  const {
    autoSelector,
    offsetXPercentage = 0,
    offsetYPercentage = 0,
    tooltipAlignment,
    wasPositionedByTargetingInterface,
    zIndexOverride,
  } = stepChild;

  const { track } = useAnalytics();

  return (
    <>
      <Tabs onClick={value => setTab(value)} selected={tab} tabs={tabs} />

      {tab === 'design' && (
        <Control>
          <Label id="alignment">Alignment</Label>
          <RadioButtonGroup aria-labelledby="alignment">
            <RadioButton
              selected={!tooltipAlignment}
              onClick={() => onChange({ tooltipAlignment: null })}
            >
              Auto
            </RadioButton>
            <RadioButton
              selected={tooltipAlignment}
              onClick={() => onChange({ tooltipAlignment: 'bottom-right' })}
            >
              Fixed
            </RadioButton>
          </RadioButtonGroup>
          <AlignmentPicker
            alignment={tooltipAlignment}
            onClick={value => onChange({ tooltipAlignment: value })}
          />
        </Control>
      )}

      {tab === 'placement' && (
        <>
          <Control>
            <Label id="fine-tune-position">Fine tune position</Label>
            <ControlGroup aria-labelledby="fine-tune-position">
              <Control>
                <DebouncedInput
                  id="percent-from-left"
                  type="number"
                  align="center"
                  defaultValue={round(offsetXPercentage * 100, 2)}
                  onChange={({ target: { valueAsNumber } }) => {
                    if (!Number.isNaN(valueAsNumber)) {
                      onChange({
                        offsetXPercentage: round(valueAsNumber / 100, 4),
                      });
                    }
                  }}
                />
                <Label htmlFor="percent-from-left" small>
                  % from left
                </Label>
              </Control>
              <Control>
                <DebouncedInput
                  id="percent-from-top"
                  type="number"
                  align="center"
                  defaultValue={round(offsetYPercentage * 100, 2)}
                  onChange={({ target: { valueAsNumber } }) => {
                    if (!Number.isNaN(valueAsNumber)) {
                      onChange({
                        offsetYPercentage: round(valueAsNumber / 100, 4),
                      });
                    }
                  }}
                />
                <Label htmlFor="percent-from-top" small>
                  % from top
                </Label>
              </Control>
              <Control>
                <DebouncedInput
                  id="z-index"
                  type="number"
                  align="center"
                  step="1"
                  defaultValue={zIndexOverride || ''}
                  onChange={({ target: { valueAsNumber } }) => {
                    if (!Number.isNaN(valueAsNumber)) {
                      onChange({ zIndexOverride: valueAsNumber });
                    }
                  }}
                  placeholder="#"
                />
                <Label htmlFor="z-index" small>
                  z-index
                </Label>
              </Control>
            </ControlGroup>
          </Control>

          <Control>
            <Button
              onClick={() => {
                track('Builder view', {
                  name: 'Entered Targeting Mode',
                  component: 'HotspotChildSettings',
                });
                onTarget();
              }}
            >
              <FontIcon icon="crosshairs" /> Place hotspot on element
            </Button>
          </Control>

          <Control>
            <Label id="target-element">
              Target element
              {status && <SelectorStatus short status={status} />}
            </Label>
            <RadioButtonGroup aria-labelledby="target-element">
              <RadioButton
                selected={wasPositionedByTargetingInterface}
                onClick={() =>
                  onChange({
                    includeOrderedMatchSelector: false,
                    includeTextMatchSelector: false,
                    selector: autoSelector,
                    selectorSettings: { selector: autoSelector },
                    uiConditions: {
                      next: {
                        type: 'WAIT_FOR_MOUSE_EVENT',
                        params: {
                          event: 'click',
                          selector: [{ selector: autoSelector }],
                        },
                      },
                    },
                    wasPositionedByTargetingInterface: true,
                  })
                }
              >
                Auto
              </RadioButton>
              <RadioButton
                selected={!wasPositionedByTargetingInterface}
                onClick={() =>
                  onChange({ wasPositionedByTargetingInterface: false })
                }
              >
                Manual
              </RadioButton>
            </RadioButtonGroup>

            {!wasPositionedByTargetingInterface && (
              <SelectorBuilder
                count={count}
                status={status}
                onChange={onChange}
                stepChild={stepChild}
                stepType={TOOLTIP}
              />
            )}
          </Control>
        </>
      )}
    </>
  );
}

HotspotChildSettings.propTypes = {
  onChange: PropTypes.func,
  onTarget: PropTypes.func,
  stepChild: stepChildShape,
};
