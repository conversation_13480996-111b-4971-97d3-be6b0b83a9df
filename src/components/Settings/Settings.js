import React from 'react';
import PropTypes from 'prop-types';
import { connect } from 'react-redux';
import { FontIcon, Tooltip } from 'ext/components/ui';
import useToggle from 'ext/lib/hooks/use-toggle';
import { attemptInteraction } from 'ext/lib/track';
import useAnalytics from 'ext/lib/hooks/use-analytics';
import { get } from 'ext/lib/session-storage';
import DeleteModal from 'ext/components/DeleteModal';
import {
  selectThemeOrDefault,
  themeShape,
  selectThemes,
} from 'ext/entities/themes';
import {
  UPDATE_STEP_CHILD,
  REMOVE_STEP_CHILD,
  CLONE_STEP_CHILD,
  TARGET_SELECTOR,
  UPDATE_STEP_GROUP,
  REMOVE_STEP_GROUP,
  CREATE_TEMPLATE,
} from 'lib/metrics/interaction-types';
import Identifiers from 'components/Identifiers';
import {
  selectFlow,
  flowShape,
  undoFlowConversion,
  redoFlowConversion,
} from 'entities/flows';
import {
  getStepLabel,
  selectStepGroup,
  stepGroupShape,
  update as updateStepGroup,
  remove as removeStepGroup,
  ACTION,
  HOTSPOT,
  TOOLTIP,
} from 'entities/step-groups';
import {
  selectStepChild,
  stepChildShape,
  target,
  clone as cloneStepChild,
  update as updateStepChild,
  remove as removeStepChild,
} from 'entities/step-children';
import {
  create as createTemplate,
  filterTemplateStep,
} from 'entities/templates';
import TemplateForm from 'components/TemplateForm';
import UndoModal from './UndoModal';
import NavigateSettings from './NavigateSettings';
import GroupSettings from './GroupSettings';
import ChildSettings from './ChildSettings';
import {
  Action,
  Actions,
  Container,
  Banner,
  BannerLink,
  LineBreak,
} from './styled';

/*
 * NOTE: Currently the UI controls in these settings handle all of the
 *       conditional logic around how certain settings are updated. For example
 *       in the selector builder, pre-existing text and order filters can change
 *       what we send in the PATCH payload. Rather than have these UI controls
 *       manage that logic, we should have e.g. a reducer or saga manage this
 *       and instead aim to have each UI control have a 1:1 relationship with
 *       some settings value on the step. This will make manaagement of this
 *       component simpler and also reduce the overall mental overhead of
 *       figuring out what each e.g. checkbox controls.
 */

/**
 * Get hostname of flow preview URL with fallback to current hostname
 *
 * @param {string} url - Flow preview URL
 * @return {string} Preview hostname or current location hostname
 */
const getHostname = url => {
  try {
    const { hostname } = new URL(url);
    return hostname;
  } catch {
    // TODO: add invariant
    return window.location.hostname;
  }
};

export function Settings({
  flow,
  stepGroup,
  stepChild,
  themes,
  theme,
  onGroupUpdate,
  onGroupDelete,
  onChildUpdate,
  onChildDelete,
  onChildClone,
  onFlowConversionUndo,
  onFlowConversionRedo,
  onTarget,
  onSaveTemplate,
  recordMetric,
}) {
  const { stepType, steps } = stepGroup;
  const { lockedForLocalization: locked } = flow;

  const [isTemplateCreateVisible, toggleTemplateCreate] = useToggle();
  const [deleting, toggleDeleting] = useToggle();
  const [undoing, toggleUndoing] = useToggle();

  const preConversionData = get(`apc_btn:${flow.id}`);

  const { track } = useAnalytics();

  const handleGroupUpdate = delta => {
    recordMetric(UPDATE_STEP_GROUP);
    onGroupUpdate(stepGroup.id, delta);
  };

  const handleChildUpdate = delta => {
    recordMetric(UPDATE_STEP_CHILD);
    onChildUpdate(stepChild.id, delta);
  };

  const handleChildClone = () => {
    track('Builder interaction', {
      name: 'Clicked Clone Step Button',
      component: 'Settings',
    });
    recordMetric(CLONE_STEP_CHILD);
    onChildClone(stepChild.id);
  };

  const handleSaveTemplate = name => {
    recordMetric(CREATE_TEMPLATE);
    onSaveTemplate({
      name,
      step: filterTemplateStep(stepChild),
      stepGroupId: stepGroup.id,
      stepType,
    });
  };

  const handleTarget = () => {
    track('Builder view', {
      name: 'Entered Targeting Mode',
      component: 'Settings',
    });
    recordMetric(TARGET_SELECTOR);
    onTarget(stepChild.id);
  };

  const handleDelete = () => {
    // If last step in group, remove entire group, otherwise only remove the
    // child from the group
    if (!steps || steps.length === 1) {
      recordMetric(REMOVE_STEP_GROUP);
      onGroupDelete(stepGroup.id, flow.id);
    } else {
      recordMetric(REMOVE_STEP_CHILD);
      onChildDelete(stepChild.id, stepGroup.id);
    }
  };

  const handleUndoFlowConversion = () => {
    onFlowConversionUndo(preConversionData);
  };

  const handleRedoFlowConversion = () => {
    onFlowConversionRedo();
  };

  if (stepType === ACTION) {
    return (
      <>
        <NavigateSettings
          hostname={getHostname(flow.previewUrl)}
          onDelete={toggleDeleting}
          onSave={handleGroupUpdate}
          stepGroup={stepGroup}
        />
        <DeleteModal
          onDelete={handleDelete}
          onClose={toggleDeleting}
          visible={deleting}
          entityName={getStepLabel(stepType)}
        />
      </>
    );
  }

  return (
    <>
      {/* TOOLBAR */}
      <Actions role="toolbar">
        {(stepType === TOOLTIP || stepType === HOTSPOT) && (
          <Tooltip
            label={`Reposition ${getStepLabel(stepType)}`}
            placement="bottom"
          >
            <Action onClick={handleTarget}>
              <FontIcon aria-label="target" icon="crosshairs" />
            </Action>
          </Tooltip>
        )}

        <Tooltip label="Clone step" placement="bottom">
          <Action disabled={locked} onClick={handleChildClone}>
            <FontIcon aria-label="clone" icon="clone" />
          </Action>
        </Tooltip>

        <Tooltip label="Save as template" placement="bottom">
          <Action
            disabled={locked}
            onClick={() => {
              track('Builder view', {
                name: 'Opened Save As Template Modal',
                component: 'Settings',
              });
              toggleTemplateCreate();
            }}
          >
            <FontIcon aria-label="save" icon="folder-open" />
          </Action>
        </Tooltip>

        <Tooltip label="Delete step" placement="bottom">
          <Action
            disabled={locked}
            onClick={() => {
              track('Builder view', {
                name: 'Opened Delete Flow Step Modal',
                component: 'Settings',
              });
              toggleDeleting();
            }}
          >
            <FontIcon aria-label="delete" icon="trash" />
          </Action>
        </Tooltip>
      </Actions>

      {preConversionData && flow.formatVersion === 2 && (
        <Banner type="undo" aria-label="undo-button-upgrade">
          The flow is now using new buttons!&nbsp;
          <BannerLink
            href="https://docs.appcues.com/article/730-updated-buttons"
            target="_blank"
            rel="noopener noreferrer"
          >
            Learn more
          </BannerLink>
          <LineBreak />
          <BannerLink onClick={toggleUndoing}>Switch back</BannerLink>&nbsp;to
          the old button version.
        </Banner>
      )}

      {flow.formatVersion === 1 && flow.formatVersionUpdatedAt && (
        <Banner type="redo" aria-label="redo-button-upgrade">
          <BannerLink
            onClick={handleRedoFlowConversion}
            aria-label="redo-button"
          >
            Switch
          </BannerLink>
          &nbsp;to the newest buttons with more design options, actions, and
          flexibility.{' '}
          <BannerLink
            href="https://docs.appcues.com/article/730-updated-buttons"
            target="_blank"
            rel="noopener noreferrer"
          >
            Learn More
          </BannerLink>
        </Banner>
      )}

      {/* MODALS */}
      <DeleteModal
        onDelete={handleDelete}
        onClose={toggleDeleting}
        visible={deleting}
        entityName={getStepLabel(stepType)}
      />
      <TemplateForm
        visible={isTemplateCreateVisible}
        onClose={toggleTemplateCreate}
        onSave={handleSaveTemplate}
      />
      <UndoModal
        onUndo={handleUndoFlowConversion}
        onClose={toggleUndoing}
        visible={undoing}
      />

      {/* SETTINGS */}
      <Container>
        <GroupSettings
          locked={locked}
          onChange={handleGroupUpdate}
          stepGroup={stepGroup}
          stepChild={stepChild}
          themes={themes}
          theme={theme}
        />

        {stepChild && (
          <ChildSettings
            index={steps.indexOf(stepChild.id)}
            locked={locked}
            onChange={handleChildUpdate}
            onTarget={handleTarget}
            stepChild={stepChild}
            stepType={stepType}
            formatVersion={flow.formatVersion}
          />
        )}

        <Identifiers
          flow={flow.id}
          stepGroup={stepGroup.id}
          stepChild={stepChild && stepChild.id}
        />
      </Container>
    </>
  );
}

Settings.propTypes = {
  flow: flowShape,
  stepGroup: stepGroupShape,
  stepChild: stepChildShape,
  themes: PropTypes.objectOf(themeShape),
  theme: themeShape,
  onGroupUpdate: PropTypes.func,
  onGroupDelete: PropTypes.func,
  onChildUpdate: PropTypes.func,
  onChildDelete: PropTypes.func,
  onChildClone: PropTypes.func,
  onTarget: PropTypes.func,
  onSaveTemplate: PropTypes.func,
  recordMetric: PropTypes.func,
  onFlowConversionUndo: PropTypes.func,
  onFlowConversionRedo: PropTypes.func,
};

const mapStateToProps = (state, { selected }) => {
  const stepGroup = selectStepGroup(state, selected.stepGroup);

  return {
    flow: selectFlow(state),
    stepGroup,
    stepChild: selectStepChild(state, selected.stepChild),
    themes: selectThemes(state),
    theme: selectThemeOrDefault(state, stepGroup.style),
  };
};

const mapDispatchToProps = {
  onGroupUpdate: updateStepGroup,
  onGroupDelete: removeStepGroup,
  onChildUpdate: updateStepChild,
  onChildDelete: removeStepChild,
  onChildClone: cloneStepChild,
  onFlowConversionUndo: undoFlowConversion,
  onFlowConversionRedo: redoFlowConversion,
  onTarget: target,
  onSaveTemplate: createTemplate,
  recordMetric: attemptInteraction,
};

export default connect(mapStateToProps, mapDispatchToProps)(Settings);
