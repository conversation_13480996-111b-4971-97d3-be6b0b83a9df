import styled, { css } from 'styled-components';
import {
  Button,
  Checkbox,
  FontIcon,
  Heading,
  RadioButton,
  Text,
  Tooltip,
} from 'ext/components/ui';

export const Action = styled.button`
  align-items: center;
  border: none;
  background: none;
  cursor: pointer;
  display: flex;
  font-size: var(--regular);
  justify-content: center;
  padding: 16px;

  ${FontIcon} {
    color: rgba(255, 255, 255, 0.6);
  }

  &:hover ${FontIcon} {
    color: var(--white);
  }

  ${({ disabled }) =>
    disabled &&
    css`
      cursor: default;
      ${FontIcon}, &:hover ${FontIcon} {
        color: var(--tint-white);
      }
    `}
`;

export const Banner = styled.div`
  align-items: center;
  background: ${({ type }) => {
    switch (type) {
      case 'undo':
        return 'var(--text-color);';
      case 'redo':
        return 'var(--warning-x-light)';
      default:
        return 'var(--text-color);';
    }
  }};

  display: flex;
  font-size: var(--x-small);
  height: 40px;
  justify-content: center;
  text-align: center;

  ${({ type }) =>
    type === 'undo' &&
    `
    height: 50px;
    line-height: 1.4em;
  `}
`;

export const BannerLink = styled.a`
  color: var(--background);
  cursor: pointer;
  display: contents;
  font-weight: var(--bold);
  text-decoration: underline;
`;

export const Actions = styled.section`
  align-items: center;
  background: var(--background);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  display: flex;
  flex-shrink: 0;
  height: 50px;
  justify-content: space-around;
`;

export const Container = styled.div`
  overflow-y: auto;
  flex: 1;
`;

export const Control = styled.div`
  display: flex;
  flex-direction: column;
  margin-bottom: 24px;
  padding: 0 24px;

  &:last-of-type {
    margin-bottom: 0;
  }

  ${RadioButton} > span {
    align-items: center;
    display: flex;
    font-size: var(--regular);
    flex-direction: column;
    justify-content: center;

    & > svg {
      height: 15px;
      margin: 4px 0;
    }
  }

  ${Button} > ${FontIcon} {
    margin-right: 8px;
  }
`;

export const Options = styled.div`
  margin-bottom: 20px;

  > ${Checkbox} {
    margin-top: 20px;
  }
`;

export const Separator = styled.h2`
  border-bottom: 2px solid rgba(255, 255, 255, 0.25);
  font-size: var(--large);
  font-weight: var(--bold);
  margin: 0 0 24px;
  padding: 0 16px;
`;

export const Label = styled.label`
  align-items: center;
  display: flex;
  font-size: var(--regular);
  font-weight: var(--bold);
  color: var(--white);
  justify-content: space-between;
  margin-bottom: 8px;

  ${({ small }) =>
    small &&
    `
      font-size: var(--x-small);
      font-weight: var(--normal);
      justify-content: center;
      margin-bottom: 0;
      margin-top: 8px;
    `}
`;

export const SubLabel = styled(Label)`
  font-weight: var(--medium);
`;

export const Link = styled.a`
  color: var(--primary-light);
  cursor: pointer;
  font-size: var(--x-small);
  font-weight: var(--bold);
  text-decoration: none;

  && ${FontIcon} {
    margin-left: 2px;
  }
`;

export const HelpText = styled.div`
  font-size: var(--x-small);
  margin: 12px 0;

  & > ${Text} {
    display: block;
  }
`;

export const ControlGroup = styled.div`
  display: flex;
  justify-content: space-between;
  margin-top: 8px;

  ${Control} {
    flex-grow: 1;
    flex-shrink: 1;
    margin-bottom: 0;
    padding: 0 4px 0 0;
    width: 100%;

    &:last-of-type {
      padding: 0;
    }
  }
`;

export const Footer = styled.footer`
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  display: flex;
  justify-content: space-between;
  margin-top: auto;
  padding: 12px 24px;
  width: 100%;

  ${Button} > ${FontIcon} {
    margin-right: 8px;
  }
`;

export const StatusText = styled.span`
  color: var(--success-light);
  font-size: var(--x-small);
  margin-top: 12px;

  ${FontIcon} {
    margin-right: 4px;
  }

  ${({ kind }) =>
    kind === 'error' &&
    `
      color: var(--warning);
    `}
`;

export const VideoDialog = styled.div`
  align-items: center;
  display: flex;
  flex-direction: column;

  ${Heading} {
    font-size: var(--x-large);
    margin: 20px 0;
  }

  ${FontIcon} {
    font-size: var(--xx-large);
  }

  ${Link} {
    font-size: inherit;
  }

  ${Text} {
    margin: 20px;
  }
`;

export const Video = styled.iframe`
  border: none;
  height: 360px;
  width: 640px;
`;

export const StepGroup = styled.section`
  color: var(--text-color);
  padding: 24px 0;
`;

export const StepChild = styled.section`
  color: var(--text-color);
  padding-bottom: 24px;
`;

export const NavigateStepGroup = styled(StepGroup)`
  display: flex;
  flex-direction: column;
  height: 100%;
  padding-bottom: 0;
`;

export const StyledTooltip = styled(Tooltip)`
  width: 175px;
  white-space: break-spaces;
`;

export const LineBreak = styled.br``;
