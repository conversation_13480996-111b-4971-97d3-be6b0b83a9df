import React from 'react';
import PropTypes from 'prop-types';
import { Checkbox, RadioButton, RadioButtonGroup } from 'ext/components/ui';
import { stepChildShape } from 'entities/step-children';
import { Control, Label, Options } from './styled';

export default function ModalChildSettings({
  index,
  locked,
  stepChild,
  onChange,
}) {
  const {
    actionsHidden,
    isButtonCentered,
    nextButtonHidden,
    prevButtonHidden,
  } = stepChild;

  const canAlignButtons =
    !actionsHidden && (prevButtonHidden || isButtonCentered || index === 0);
  const showPrevButton = !isButtonCentered && !prevButtonHidden;

  return (
    <Control>
      <Label>Buttons</Label>
      <Options>
        {index > 0 && (
          <Checkbox
            label="Back"
            checked={showPrevButton}
            disabled={locked}
            onChange={value =>
              onChange({
                actionsHidden: !value && nextButtonHidden,
                isButtonCentered: false,
                prevButtonHidden: !value,
              })
            }
          />
        )}
        <Checkbox
          label="Next"
          checked={!nextButtonHidden}
          disabled={locked}
          onChange={value =>
            onChange({
              actionsHidden: !value && (prevButtonHidden || index === 0),
              nextButtonHidden: !value,
            })
          }
        />
      </Options>

      {canAlignButtons && (
        <RadioButtonGroup>
          <RadioButton
            disabled={locked}
            selected={isButtonCentered}
            onClick={() =>
              onChange({ isButtonCentered: true, prevButtonHidden: true })
            }
          >
            Centered
          </RadioButton>
          <RadioButton
            disabled={locked}
            selected={!isButtonCentered}
            onClick={() =>
              onChange({ isButtonCentered: false, prevButtonHidden: true })
            }
          >
            Right
          </RadioButton>
        </RadioButtonGroup>
      )}
    </Control>
  );
}

ModalChildSettings.propTypes = {
  index: PropTypes.number,
  locked: PropTypes.bool,
  onChange: PropTypes.func,
  stepChild: stepChildShape,
};
