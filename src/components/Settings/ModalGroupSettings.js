import React from 'react';
import PropTypes from 'prop-types';
import { Checkbox, Dropdown } from 'ext/components/ui';
import { stepGroupShape } from 'entities/step-groups';
import { Control, Label, Options } from './styled';

const options = [
  {
    label: 'Standard',
    value: 'modal',
    description:
      'Displays the content in the middle of the screen with your app masked behind it.',
  },
  {
    label: 'Sidebar',
    value: 'left',
    description:
      'Slides the content out from the side of the screen with your app visible in the background.',
  },
  {
    label: 'Full screen',
    value: 'fullscreen',
    description:
      'Displays the content in the middle of the screen with a full coverage background so the app is not visible.',
  },
];

export default function ModalGroupSettings({ locked, onChange, stepGroup }) {
  const { isProgressBarHidden, patternType, skippable } = stepGroup;

  return (
    <>
      <Control>
        <Label htmlFor="type">Type</Label>
        <Dropdown
          id="type"
          defaultValue={patternType}
          onChange={value => onChange({ patternType: value })}
          options={options}
        />
      </Control>

      <Control>
        <Label>Options</Label>
        <Options>
          <Checkbox
            label="Show progress bar"
            checked={!isProgressBarHidden}
            onChange={value => onChange({ isProgressBarHidden: !value })}
          />
          <Checkbox
            disabled={locked}
            label="Skippable"
            checked={skippable}
            onChange={value => onChange({ skippable: value })}
          />
        </Options>
      </Control>
    </>
  );
}

ModalGroupSettings.propTypes = {
  locked: PropTypes.bool,
  onChange: PropTypes.func,
  stepGroup: stepGroupShape,
};
