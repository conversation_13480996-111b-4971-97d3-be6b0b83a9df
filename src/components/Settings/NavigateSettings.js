import React, { useState, useEffect } from 'react';
import PropType from 'prop-types';
import useToggle from 'ext/lib/hooks/use-toggle';
import {
  Bold,
  Button,
  FontIcon,
  RadioButton,
  RadioButtonGroup,
  Text,
} from 'ext/components/ui';
import {
  checkForWildcards,
  checkForPlaceholders,
  doesLocationMatch,
} from 'ext/vendor/url-match';
import useAnalytics from 'ext/lib/hooks/use-analytics';
import { stepGroupShape } from 'entities/step-groups';
import { NAVIGATE_KNOWLEDGE_ARTICLE } from 'lib/constants';
import PathInput from 'components/PathInput';
import InstructionalVideo from './InstructionalVideo';
import {
  Container,
  Control,
  StatusText,
  Footer,
  HelpText,
  Label,
  Link,
  NavigateStepGroup,
} from './styled';

/**
 * NOTE: Currently on save the status indicator in the bottom "shows" that
 *       something happened, but from within the sidebar, it can look like
 *       either nothing happened or a possible error since the save button
 *       immedately becomes disabled again. We should consider whether it makes
 *       sense for this use-case of settings - manual save - to provide more
 *       feedback on save to communicate to the user that their changes did
 *       indeed save.
 *
 * TODO:
 *   - Update styles to match the sidebar footer of the event builder e.g.
 *     sticky footer
 */

const REDIRECT = 'redirect';
const WAIT_FOR_PAGE = 'wait-for-page';

export default function NavigateSettings({
  hostname,
  onDelete,
  onSave,
  stepGroup,
}) {
  const {
    actionType: initialActionType,
    previewUrl: initialPreviewUrl,
    params: initialParams = {},
  } = stepGroup;

  const [actionType, setActionType] = useState(null);
  const [buildUrl, setBuildUrl] = useState(null);
  const [targetUrl, setTargetUrl] = useState(null);
  const [pageCheckLimit, setPageCheckLimit] = useState(null);
  const [showVideo, toggleShowVideo] = useToggle();
  const { track } = useAnalytics();

  // Resolved values that will use the staged values if they exist, otherwise
  // falling back to the initial provided values. The staged values initialize
  // with `null` to ensure that we can allow empty strings as valid inputs for
  // the URL values.
  const resolved = {
    actionType: actionType === null ? initialActionType : actionType,
    previewUrl: buildUrl === null ? initialPreviewUrl : buildUrl,
    params: {
      url: targetUrl === null ? initialParams.url : targetUrl,
      page_check_limit:
        pageCheckLimit === null
          ? initialParams.page_check_limit
          : pageCheckLimit,
    },
  };

  const hasWildcards = checkForWildcards(resolved.params.url);
  const hasPlaceholders = checkForPlaceholders(resolved.params.url);
  const matchesUrl = doesLocationMatch(
    resolved.params.url,
    resolved.previewUrl
  );

  const isRedirect = resolved.actionType === REDIRECT;
  const hasPatterns = isRedirect ? hasPlaceholders : hasWildcards;
  const hasInvalidWildCards = isRedirect && hasWildcards;

  const changed =
    resolved.actionType !== initialActionType ||
    resolved.previewUrl !== initialPreviewUrl ||
    resolved.params.url !== initialParams.url ||
    resolved.params.page_check_limit !== initialParams.page_check_limit;

  // If the action type is changed, reset controls back to their initial value
  // to ensure condtional fields such as the build URL do not hold onto stale
  // values that may be in uneditable states
  const handleActionTypeChange = type => {
    if (type !== resolved.actionType) {
      setActionType(type);
      setBuildUrl(null);
      setTargetUrl(null);
      setPageCheckLimit(null);
    }
  };

  // When the target URL is changed, determine if the current target URL has
  // patterns such as globs or placeholders based on the action type. If it
  // does, revert back to the initially provided build URL if the previous
  // target URL did not have patterns. Otherwise if the current target URL does
  // not have patterns, set the build URL as the current target URL
  const handleTargetUrlChange = ({ target: { value } }) => {
    setTargetUrl(value);

    const stillHasPatterns = isRedirect
      ? checkForPlaceholders(value)
      : checkForWildcards(value);

    if (stillHasPatterns) {
      if (!hasPatterns) {
        setBuildUrl(initialPreviewUrl);
      }
    } else {
      setBuildUrl(value);
    }
  };

  // To ensure we only send changes deltas, compare the staged values with the
  // initial values and only add them to the patch if they differ.
  const handleSave = () => {
    track('Builder interaction', {
      name: 'Updated Navigate Settings',
      component: 'NavigateSettings',
    });

    const delta = {};

    if (resolved.actionType !== initialActionType) {
      delta.actionType = resolved.actionType;
    }

    if (resolved.previewUrl !== initialPreviewUrl) {
      delta.previewUrl = resolved.previewUrl;
    }

    if (resolved.params.url !== initialParams.url) {
      delta.params = {
        ...initialParams,
        ...delta.params,
        url: resolved.params.url,
      };
    }

    if (resolved.params.page_check_limit !== initialParams.page_check_limit) {
      delta.params = {
        ...initialParams,
        ...delta.params,
        page_check_limit: resolved.params.page_check_limit,
      };
    }

    onSave(delta);
  };

  useEffect(() => {
    setActionType(stepGroup.actionType);
  }, [stepGroup, setActionType]);

  return (
    <Container>
      <NavigateStepGroup aria-label="Navigate settings">
        <Control>
          <Label id="navigate-type">How should the user get here?</Label>
          <RadioButtonGroup aria-labelledby="navigate-type">
            <RadioButton
              aria-label="Redirect"
              onClick={() => handleActionTypeChange(REDIRECT)}
              selected={isRedirect}
            >
              <FontIcon icon="reply" flip="horizontal" />
              Redirect
            </RadioButton>
            <RadioButton
              aria-label="Let Them Navigate"
              onClick={() => handleActionTypeChange(WAIT_FOR_PAGE)}
              selected={!isRedirect}
            >
              <FontIcon icon="hiking" />
              Let Them Navigate
            </RadioButton>
          </RadioButtonGroup>

          <HelpText>
            {isRedirect && (
              <Text>
                <Bold>Redirect</Bold> takes the user directly to a URL when they
                progress to the next step. If you want them to navigate on their
                own, try “Let Them Navigate”.
              </Text>
            )}

            {!isRedirect && (
              <Text>
                <Bold>Let Them Navigate</Bold> depends on you guiding the user
                to the right place in the previous step. Once the URL pattern
                matches, the flow will continue.
              </Text>
            )}
          </HelpText>

          <HelpText>
            <Text>Not sure what to choose? </Text>
            <Text>
              <Link
                href={NAVIGATE_KNOWLEDGE_ARTICLE}
                rel="noreferrer"
                target="_blank"
              >
                Read more
              </Link>
              {` or `}
              <Text as={Link} onClick={toggleShowVideo}>
                watch a short video.
              </Text>
            </Text>

            <InstructionalVideo onClose={toggleShowVideo} visible={showVideo} />
          </HelpText>
        </Control>

        <Control>
          <Label htmlFor="target-url">
            {isRedirect ? 'Redirect to' : 'Expect page to match'}
          </Label>
          <PathInput
            id="target-url"
            hostname={hostname}
            onChange={handleTargetUrlChange}
            value={resolved.params.url}
          />

          {hasInvalidWildCards && (
            <StatusText kind="error" role="alert">
              <FontIcon icon="exclamation-triangle" />
              Wildcards (*) can only be used in the <Bold>Navigate</Bold>{' '}
              action.
            </StatusText>
          )}

          <HelpText>
            {isRedirect ? (
              <Text>
                <Bold>Tip:</Bold> you can insert{' '}
                <Bold>{`{{profile-attribute}}`}</Bold> as part of the URL.
              </Text>
            ) : (
              <Text>
                <Bold>Tip:</Bold> you can use asterisks <Bold>*</Bold> as
                wildcards to match any string.
              </Text>
            )}
          </HelpText>
        </Control>

        {hasPatterns && (
          <Control>
            <Label htmlFor="build-url">Build URL</Label>
            {/* TODO: Add tooltip on hover showing URL with hostname */}
            <PathInput
              id="build-url"
              hostname={hostname}
              onChange={({ target: { value } }) => setBuildUrl(value)}
              value={resolved.previewUrl}
            />

            {matchesUrl && (
              <StatusText role="status">
                <FontIcon icon="check" />
                This matches where users will go. Great!
              </StatusText>
            )}

            {!matchesUrl && (
              <StatusText kind="error" role="alert">
                <FontIcon icon="exclamation-triangle" />
                This doesn’t match where users will go. Use a matching preview
                URL.
              </StatusText>
            )}
          </Control>
        )}

        <Footer>
          <Button
            kind="danger"
            onClick={() => {
              track('Builder view', {
                name: 'Opened Delete Flow Step Modal',
                component: 'NavigateSettings',
              });
              onDelete();
            }}
            type="button"
          >
            <FontIcon aria-label="delete" icon="trash" />
            Delete page change
          </Button>
          <Button
            disabled={!changed || hasInvalidWildCards}
            kind="primary"
            onClick={handleSave}
            type="button"
          >
            Save
          </Button>
        </Footer>
      </NavigateStepGroup>
    </Container>
  );
}

NavigateSettings.propTypes = {
  hostname: PropType.string,
  onDelete: PropType.func,
  onSave: PropType.func,
  stepGroup: stepGroupShape,
};
