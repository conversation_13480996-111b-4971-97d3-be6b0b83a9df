import React from 'react';

export const DismissLinkIcon = () => (
  <svg width="48px" height="24px" viewBox="0 0 48 24">
    <g stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
      <g transform="translate(-63.000000, -648.000000)">
        <g transform="translate(27.000000, 637.000000)">
          <g transform="translate(36.000000, 11.000000)">
            <path
              d="M11.3359295,22.4368683 L14.5933874,19.1794104 L15.007601,19.1794104 L44.9260997,19.1794104 C46.0306692,19.1794104 46.9260997,18.2839799 46.9260997,17.1794104 L46.9260997,3 C46.9260997,1.8954305 46.0306692,1 44.9260997,1 L3,1 C1.8954305,1 1,1.8954305 1,3 L1,17.1794104 C1,18.2839799 1.8954305,19.1794104 3,19.1794104 L8.07847155,19.1794104 L11.3359295,22.4368683 Z"
              stroke="var(--white)"
              strokeWidth="2"
              transform="translate(23.963050, 11.925541) rotate(-360.000000) translate(-23.963050, -11.925541) "
            />
            <g
              id="Group-19"
              transform="translate(7.000000, 11.000000)"
              fill="var(--white)"
            >
              <polygon fillRule="nonzero" points="5 3 5 0 21 0 21 3" />
              <rect x="0" y="0" width="1" height="1" />
              <rect x="1" y="1" width="1" height="1" />
              <rect x="2" y="0" width="1" height="1" />
              <rect x="2" y="2" width="1" height="1" />
              <rect x="0" y="2" width="1" height="1" />
            </g>
          </g>
        </g>
      </g>
    </g>
  </svg>
);

export const CloseXIcon = () => (
  <svg width="48px" height="24px" viewBox="0 0 48 24">
    <g stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
      <g transform="translate(-185.000000, -652.000000)">
        <g transform="translate(27.000000, 637.000000)">
          <g transform="translate(158.000000, 15.000000)">
            <path
              d="M11.3370148,22.4388095 L14.5947716,19.1810528 L44.9300003,19.1810528 C46.0345698,19.1810528 46.9300003,18.2856223 46.9300003,17.1810528 L46.9300003,3 C46.9300003,1.8954305 46.0345698,1 44.9300003,1 L3,1 C1.8954305,1 1,1.8954305 1,3 L1,17.1810528 C1,18.2856223 1.8954305,19.1810528 3,19.1810528 L8.07925809,19.1810528 L11.3370148,22.4388095 Z"
              stroke="var(--white)"
              strokeWidth="2"
            />
            <polygon
              fill="var(--white)"
              points="36.3746207 10.8993563 38.4866667 8.77243678 36.3746207 6.63064368 37.4603908 5.53 39.5873103 7.6717931 41.7142299 5.53 42.8 6.63064368 40.687954 8.77243678 42.8 10.8993563 41.7142299 12 39.5873103 9.8582069 37.4603908 12"
            />
          </g>
        </g>
      </g>
    </g>
  </svg>
);
