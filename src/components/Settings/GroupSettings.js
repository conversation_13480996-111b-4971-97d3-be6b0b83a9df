import React, { useMemo } from 'react';
import PropTypes from 'prop-types';
import { Dropdown, FontIcon } from 'ext/components/ui';
import { themeShape } from 'ext/entities/themes';
import {
  stepGroupShape,
  MODAL,
  SLIDEOUT,
  TOOLTIP,
  HOTSPOT,
} from 'entities/step-groups';
import { stepChildShape } from 'entities/step-children';
import ModalGroupSettings from './ModalGroupSettings';
import SlideoutGroupSettings from './SlideoutGroupSettings';
import TooltipGroupSettings from './TooltipGroupSettings';
import HotspotGroupSettings from './HotspotGroupSettings';
import { Control, Label, Link, StepGroup } from './styled';

export default function GroupSettings({
  locked,
  onChange,
  stepChild,
  stepGroup,
  themes,
  theme,
}) {
  const { stepType } = stepGroup;

  const options = useMemo(() => {
    return Object.values(themes).map(({ id, name }) => ({
      label: name,
      value: id,
    }));
  }, [themes]);

  return (
    <StepGroup aria-label="Group settings">
      <Control>
        <Label htmlFor="theme">
          Theme
          <Link href={`${STUDIO_URL}/themes`} rel="noreferrer" target="_blank">
            View all themes <FontIcon icon="arrow-circle-right" />
          </Link>
        </Label>
        <Dropdown
          id="theme"
          defaultValue={theme && theme.id}
          onChange={value => onChange({ style: value })}
          options={options}
          placeholder="Select theme"
        />
      </Control>

      {stepType === MODAL && (
        <ModalGroupSettings
          onChange={onChange}
          stepGroup={stepGroup}
          locked={locked}
        />
      )}

      {stepType === SLIDEOUT && (
        <SlideoutGroupSettings
          onChange={onChange}
          stepGroup={stepGroup}
          locked={locked}
        />
      )}

      {stepType === TOOLTIP && (
        <TooltipGroupSettings
          onChange={onChange}
          stepGroup={stepGroup}
          stepChild={stepChild}
          locked={locked}
        />
      )}

      {stepType === HOTSPOT && (
        <HotspotGroupSettings
          onChange={onChange}
          stepGroup={stepGroup}
          locked={locked}
        />
      )}
    </StepGroup>
  );
}

GroupSettings.propTypes = {
  locked: PropTypes.bool,
  onChange: PropTypes.func,
  stepGroup: stepGroupShape,
  stepChild: stepChildShape,
  themes: PropTypes.objectOf(themeShape),
  theme: themeShape,
};
