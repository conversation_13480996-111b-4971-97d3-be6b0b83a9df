import React from 'react';
import PropTypes from 'prop-types';
import styled from 'styled-components';
import { FontIcon, Text } from 'ext/components/ui';
import {
  getStepLabel,
  STEP_TYPES,
  MODAL,
  SLIDEOUT,
  TOOLTIP,
  HOTSPOT,
} from 'entities/step-groups';
import { stepChildShape } from 'entities/step-children';
import ModalChildSettings from './ModalChildSettings';
import SlideoutChildSettings from './SlideoutChildSettings';
import TooltipChildSettings from './TooltipChildSettings';
import HotspotChildSettings from './HotspotChildSettings';
import { Separator, StepChild, StyledTooltip } from './styled';

const TooltipContainer = styled.div`
  align-items: center;
  display: flex;
  margin-bottom: 1rem;
  ${Text} {
    font-size: var(--regular);
    padding-right: 5px;
    font-weight: var(--normal);
  }
`;

const hasButtonWithNextAction = rows =>
  rows
    .flatMap(row => row?.zones ?? [])
    .some(
      zone =>
        zone?.type === 'Button' &&
        zone?.persistedState?.buttonActionType === 'next'
    );

export default function ChildSettings({
  index,
  locked,
  stepType,
  stepChild,
  onChange,
  onTarget,
  formatVersion,
}) {
  // We don't want to render this component for modals and slideouts on v2 since
  // their only child setting is default buttons
  if ((stepType === MODAL || stepType === SLIDEOUT) && formatVersion === 2) {
    return null;
  }

  const toolTipText = hasButtonWithNextAction(stepChild?.rows ?? [])
    ? 'Remove the next button to progress on click of element'
    : 'Add a button with a next action to progress on click of button';

  return (
    <StepChild aria-label="Child settings">
      <Separator>
        Individual {getStepLabel(stepType)}
        {stepType === TOOLTIP && formatVersion === 2 && (
          <TooltipContainer>
            <Text>
              {hasButtonWithNextAction(stepChild?.rows ?? [])
                ? 'Progresses on click of button'
                : 'Progresses on click of element'}
            </Text>
            <StyledTooltip placement="bottom" label={toolTipText} wrapped>
              <FontIcon
                size="xs"
                icon="question-circle"
                aria-label={toolTipText}
              />
            </StyledTooltip>
          </TooltipContainer>
        )}
      </Separator>

      {stepType === MODAL && (
        <ModalChildSettings
          index={index}
          locked={locked}
          onChange={onChange}
          stepChild={stepChild}
        />
      )}

      {stepType === SLIDEOUT && (
        <SlideoutChildSettings
          index={index}
          locked={locked}
          onChange={onChange}
          stepChild={stepChild}
        />
      )}

      {stepType === TOOLTIP && (
        <TooltipChildSettings
          locked={locked}
          onChange={onChange}
          onTarget={onTarget}
          stepChild={stepChild}
          formatVersion={formatVersion}
        />
      )}

      {stepType === HOTSPOT && (
        <HotspotChildSettings
          locked={locked}
          onChange={onChange}
          onTarget={onTarget}
          stepChild={stepChild}
        />
      )}
    </StepChild>
  );
}

ChildSettings.propTypes = {
  index: PropTypes.number,
  locked: PropTypes.bool,
  onChange: PropTypes.func,
  onTarget: PropTypes.func,
  stepChild: stepChildShape,
  stepType: PropTypes.oneOf(STEP_TYPES),
  formatVersion: PropTypes.number,
};
