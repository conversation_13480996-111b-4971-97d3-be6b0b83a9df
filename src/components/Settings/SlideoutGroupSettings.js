import React from 'react';
import PropTypes from 'prop-types';
import { Checkbox } from 'ext/components/ui';
import PositionDropdown from 'components/PositionDropdown';
import { stepGroupShape } from 'entities/step-groups';
import { Control, Label, Options } from './styled';

export default function SlideoutGroupSettings({ locked, onChange, stepGroup }) {
  const { backdrop, isProgressBarHidden, position, skippable } = stepGroup;

  return (
    <>
      <Control>
        <Label htmlFor="position">Position</Label>
        <PositionDropdown
          id="position"
          defaultValue={position}
          onChange={value => onChange({ position: value })}
        />
      </Control>

      <Control>
        <Label>Options</Label>
        <Options>
          <Checkbox
            label="Show backdrop"
            checked={backdrop}
            onChange={value => onChange({ backdrop: value })}
          />
          <Checkbox
            label="Show progress bar"
            checked={!isProgressBarHidden}
            onChange={value => onChange({ isProgressBarHidden: !value })}
          />
          <Checkbox
            disabled={locked}
            label="Skippable"
            checked={skippable}
            onChange={value => onChange({ skippable: value })}
          />
        </Options>
      </Control>
    </>
  );
}

SlideoutGroupSettings.propTypes = {
  locked: PropTypes.bool,
  onChange: PropTypes.func,
  stepGroup: stepGroupShape,
};
