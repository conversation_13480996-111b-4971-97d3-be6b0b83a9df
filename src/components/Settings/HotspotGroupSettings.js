import React from 'react';
import PropTypes from 'prop-types';
import { connect } from 'react-redux';
import { FontIcon, RadioButton, RadioButtonGroup } from 'ext/components/ui';
import { stepGroupShape, updateSkippable } from 'entities/step-groups';

import { DismissLinkIcon } from './icons';
import { Control, Label } from './styled';

export function HotspotGroupSettings({
  locked,
  onChange,
  stepGroup,
  onUpdateSkippable,
}) {
  const { beaconStyle, skippable } = stepGroup;

  const onChangeSkippableSettings = options => {
    onUpdateSkippable(stepGroup.id, options);
  };

  return (
    <>
      <Control>
        <Label id="beacon">Beacon</Label>
        <RadioButtonGroup aria-labelledby="beacon">
          <RadioButton
            aria-label="Hotspot beacon"
            selected={beaconStyle === null || beaconStyle === 'hotspot'}
            onClick={() => onChange({ beaconStyle: 'hotspot' })}
          >
            <FontIcon icon="bullseye" />
          </RadioButton>
          <RadioButton
            aria-label="Question beacon"
            selected={beaconStyle === 'question'}
            onClick={() => onChange({ beaconStyle: 'question' })}
          >
            <FontIcon icon="question-circle" />
          </RadioButton>
        </RadioButtonGroup>
      </Control>

      <Control>
        <Label id="skippable">Skippable</Label>
        <RadioButtonGroup aria-labelledby="skippable">
          <RadioButton
            aria-label="Not skippable"
            disabled={locked}
            selected={!skippable}
            onClick={() => onChangeSkippableSettings({ skippable: false })}
          >
            <FontIcon icon="ban" />
            Not
          </RadioButton>
          <RadioButton
            aria-label="Dismiss link"
            disabled={locked}
            selected={skippable}
            onClick={() => onChangeSkippableSettings({ skippable: true })}
          >
            <DismissLinkIcon />
            Dismiss link
          </RadioButton>
        </RadioButtonGroup>
      </Control>
    </>
  );
}

HotspotGroupSettings.propTypes = {
  locked: PropTypes.bool,
  onChange: PropTypes.func,
  stepGroup: stepGroupShape,
  onUpdateSkippable: PropTypes.func,
};

const mapDispatchToProps = {
  onUpdateSkippable: updateSkippable,
};

export default connect(null, mapDispatchToProps)(HotspotGroupSettings);
