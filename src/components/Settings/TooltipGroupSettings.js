import React, { useMemo } from 'react';
import { connect } from 'react-redux';
import PropTypes from 'prop-types';
import {
  FontIcon,
  RadioButton,
  RadioButtonGroup,
  Tooltip,
} from 'ext/components/ui';
import { IFRAME_ROOT_TOKEN } from 'ext/lib/evaluate-selector';
import { stepGroupShape, updateSkippable } from 'entities/step-groups';
import { stepChildShape } from 'entities/step-children';
import { DismissLinkIcon, CloseXIcon } from './icons';
import { Control, Label } from './styled';

export function TooltipGroupSettings({
  locked,
  onChange,
  stepGroup,
  stepChild,
  onUpdateSkippable,
}) {
  const {
    backdrop,
    backdropSolidEdge,
    beaconStyle,
    skippable,
    skippableDisplayType,
  } = stepGroup;

  const onChangeSkippableSettings = options => {
    onUpdateSkippable(stepGroup.id, options);
  };

  const isTargetInIframe = useMemo(
    () => stepChild?.selector?.includes(IFRAME_ROOT_TOKEN) || false,
    [stepChild]
  );

  return (
    <>
      <Control>
        <Label id="beacon">Beacon</Label>
        <RadioButtonGroup aria-labelledby="beacon">
          <RadioButton
            aria-label="Hidden beacon"
            selected={beaconStyle === 'hidden'}
            onClick={() => onChange({ beaconStyle: 'hidden' })}
          >
            None
          </RadioButton>
          <RadioButton
            aria-label="Hotspot beacon"
            selected={beaconStyle === null || beaconStyle === 'hotspot'}
            onClick={() => onChange({ beaconStyle: 'hotspot' })}
          >
            <FontIcon icon="bullseye" />
          </RadioButton>
          <RadioButton
            aria-label="Question beacon"
            selected={beaconStyle === 'question'}
            onClick={() => onChange({ beaconStyle: 'question' })}
          >
            <FontIcon icon="question-circle" />
          </RadioButton>
        </RadioButtonGroup>
      </Control>

      <Control>
        <Label id="backdrop">Backdrop</Label>
        <Tooltip
          label={
            <span style={{ textAlign: 'center' }}>
              Unavailable for target <br />
              elements inside an iframe.
            </span>
          }
          disabled={!isTargetInIframe}
        >
          <RadioButtonGroup aria-labelledby="backdrop">
            <RadioButton
              aria-label="No backdrop"
              disabled={isTargetInIframe}
              selected={!backdrop}
              onClick={() =>
                onChange({ backdrop: false, backdropSolidEdge: false })
              }
            >
              <FontIcon icon="ban" />
              None
            </RadioButton>
            <RadioButton
              aria-label="Soft backdrop"
              disabled={isTargetInIframe}
              selected={backdrop && !backdropSolidEdge}
              onClick={() =>
                onChange({ backdrop: true, backdropSolidEdge: false })
              }
            >
              <FontIcon icon="certificate" />
              Soft
            </RadioButton>
            <RadioButton
              aria-label="Hard backdrop"
              disabled={isTargetInIframe}
              selected={backdrop && backdropSolidEdge}
              onClick={() =>
                onChange({
                  backdrop: true,
                  backdropSolidEdge: true,
                  backdropSolidEdgeOpacity: 0.5,
                })
              }
            >
              <FontIcon icon="square" />
              Hard
            </RadioButton>
          </RadioButtonGroup>
        </Tooltip>
      </Control>

      <Control>
        <Label id="skippable">Skippable</Label>
        <RadioButtonGroup aria-labelledby="skippable">
          <RadioButton
            aria-label="Not skippable"
            disabled={locked}
            selected={!skippable}
            onClick={() =>
              onChangeSkippableSettings({
                skippable: false,
                skippableDisplayType: null,
              })
            }
          >
            <FontIcon icon="ban" />
            Not
          </RadioButton>
          <RadioButton
            aria-label="Dismiss link"
            disabled={locked}
            selected={skippable && skippableDisplayType === 'text'}
            onClick={() =>
              onChangeSkippableSettings({
                skippable: true,
                skippableDisplayType: 'text',
              })
            }
          >
            <DismissLinkIcon />
            Dismiss link
          </RadioButton>
          <RadioButton
            aria-label="Close X"
            disabled={locked}
            selected={skippable && skippableDisplayType === 'exit-symbol'}
            onClick={() =>
              onChangeSkippableSettings({
                skippable: true,
                skippableDisplayType: 'exit-symbol',
              })
            }
          >
            <CloseXIcon />
            Close X
          </RadioButton>
        </RadioButtonGroup>
      </Control>
    </>
  );
}

TooltipGroupSettings.propTypes = {
  locked: PropTypes.bool,
  onChange: PropTypes.func,
  stepGroup: stepGroupShape,
  stepChild: stepChildShape,
  onUpdateSkippable: PropTypes.func,
};

const mapDispatchToProps = {
  onUpdateSkippable: updateSkippable,
};

export default connect(null, mapDispatchToProps)(TooltipGroupSettings);
