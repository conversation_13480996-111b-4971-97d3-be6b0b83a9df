import React from 'react';
import PropTypes from 'prop-types';
import {
  ButtonG<PERSON>,
  <PERSON><PERSON>,
  <PERSON>ing,
  <PERSON>dal,
  <PERSON>dalHeader,
  P,
} from 'ext/components/ui';

export function UndoModal({ onClose, onUndo, visible }) {
  const handleUndo = () => {
    onUndo();
    onClose();
  };

  return (
    <Modal onClose={onClose} visible={visible}>
      <ModalHeader>
        <Heading>Flow changes</Heading>
      </ModalHeader>
      <P>
        Switching back to old buttons will revert this flow back to the last
        saved version at the beginning of this session.
      </P>
      <ButtonGroup right>
        <Button kind="tertiary" onClick={onClose} type="button">
          Cancel
        </Button>
        <Button kind="danger" onClick={handleUndo} type="button">
          Switch back
        </Button>
      </ButtonGroup>
    </Modal>
  );
}

UndoModal.propTypes = {
  onClose: PropTypes.func,
  onUndo: PropTypes.func,
  visible: PropTypes.bool,
};

export default UndoModal;
