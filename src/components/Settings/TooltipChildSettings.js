import React, { useState } from 'react';
import PropTypes from 'prop-types';
import {
  Button,
  DebouncedInput,
  FontIcon,
  RadioButton,
  RadioButtonGroup,
  Tabs,
} from 'ext/components/ui';
import useAnalytics from 'ext/lib/hooks/use-analytics';
import AlignmentPicker from 'ext/components/AlignmentPicker';
import SelectorBuilder from 'components/SelectorBuilder';
import SelectorStatus from 'components/SelectorStatus';
import { TOOLTIP } from 'entities/step-groups';
import { stepChildShape } from 'entities/step-children';
import useSelectorValidity from 'lib/hooks/use-selector-validity';
import { Control, ControlGroup, Label, SubLabel } from './styled';

/**
 * TODO:
 *   - Respond to target selector update
 */

const round = (number, precision) => Number(number.toFixed(precision));

const tabs = [
  { label: 'Design', value: 'design' },
  { label: 'Placement', value: 'placement' },
];

export default function TooltipChildSettings({
  locked,
  onChange,
  onTarget,
  stepChild,
  formatVersion,
}) {
  const [tab, setTab] = useState('design');
  const { status, count } = useSelectorValidity(stepChild);

  const {
    autoSelector,
    backdropSolidEdgeBorderRadius = 0,
    backdropSolidEdgeXPadding = 0,
    backdropSolidEdgeYPadding = 0,
    hideNextButton,
    offsetXPercentage = 0,
    offsetYPercentage = 0,
    selectorSettings,
    tooltipAlignment,
    wasPositionedByTargetingInterface,
    zIndexOverride,
  } = stepChild;

  const { track } = useAnalytics();

  return (
    <>
      <Tabs onClick={value => setTab(value)} selected={tab} tabs={tabs} />

      {tab === 'design' && (
        <>
          <Control>
            <Label id="backdrop-adjustments">Backdrop adjustments</Label>
            <ControlGroup aria-labelledby="backdrop-adjustments">
              <Control>
                <SubLabel htmlFor="horizontal-padding">
                  Horizontal padding (PX)
                </SubLabel>
                <DebouncedInput
                  id="horizontal-padding"
                  type="number"
                  align="center"
                  step="1"
                  defaultValue={backdropSolidEdgeXPadding}
                  onChange={({ target: { valueAsNumber } }) => {
                    if (!Number.isNaN(valueAsNumber)) {
                      onChange({ backdropSolidEdgeXPadding: valueAsNumber });
                    }
                  }}
                />
              </Control>

              <Control>
                <SubLabel htmlFor="vertical-padding">
                  Vertical padding (PX)
                </SubLabel>
                <DebouncedInput
                  id="vertical-padding"
                  type="number"
                  align="center"
                  step="1"
                  defaultValue={backdropSolidEdgeYPadding}
                  onChange={({ target: { valueAsNumber } }) => {
                    if (!Number.isNaN(valueAsNumber)) {
                      onChange({ backdropSolidEdgeYPadding: valueAsNumber });
                    }
                  }}
                />
              </Control>

              <Control>
                <SubLabel htmlFor="border-radius">Border radius (PX)</SubLabel>
                <DebouncedInput
                  id="border-radius"
                  type="number"
                  align="center"
                  min="0"
                  step="1"
                  defaultValue={backdropSolidEdgeBorderRadius}
                  onChange={({ target: { valueAsNumber } }) => {
                    if (!Number.isNaN(valueAsNumber)) {
                      onChange({
                        backdropSolidEdgeBorderRadius: valueAsNumber,
                      });
                    }
                  }}
                />
              </Control>
            </ControlGroup>
          </Control>

          {formatVersion !== 2 && (
            <Control>
              <Label id="progress">Progress on click of</Label>
              <RadioButtonGroup aria-labelledby="progress">
                <RadioButton
                  disabled={locked}
                  selected={!hideNextButton}
                  onClick={() =>
                    onChange({
                      hideNextButton: false,
                      uiConditions: { next: null },
                    })
                  }
                >
                  <FontIcon icon="digital-tachograph" />
                  Button
                </RadioButton>
                <RadioButton
                  disabled={locked}
                  selected={hideNextButton}
                  onClick={() =>
                    onChange({
                      hideNextButton: true,
                      uiConditions: {
                        next: {
                          type: 'WAIT_FOR_MOUSE_EVENT',
                          params: {
                            event: 'click',
                            selector: [selectorSettings],
                          },
                        },
                      },
                    })
                  }
                >
                  <FontIcon icon="crosshairs" />
                  Element
                </RadioButton>
              </RadioButtonGroup>
            </Control>
          )}

          <Control>
            <Label id="alignment">Alignment</Label>
            <RadioButtonGroup aria-labelledby="alignment">
              <RadioButton
                selected={!tooltipAlignment}
                onClick={() => onChange({ tooltipAlignment: null })}
              >
                Auto
              </RadioButton>
              <RadioButton
                selected={tooltipAlignment}
                onClick={() => onChange({ tooltipAlignment: 'bottom-right' })}
              >
                Fixed
              </RadioButton>
            </RadioButtonGroup>
            <AlignmentPicker
              alignment={tooltipAlignment}
              onClick={value => onChange({ tooltipAlignment: value })}
            />
          </Control>
        </>
      )}

      {tab === 'placement' && (
        <>
          <Control>
            <Label id="fine-tune-position">Fine tune position</Label>
            <ControlGroup aria-labelledby="fine-tune-position">
              <Control>
                <DebouncedInput
                  id="percent-from-left"
                  type="number"
                  align="center"
                  defaultValue={round(offsetXPercentage * 100, 2)}
                  onChange={({ target: { valueAsNumber } }) => {
                    if (!Number.isNaN(valueAsNumber)) {
                      onChange({
                        offsetXPercentage: round(valueAsNumber / 100, 4),
                      });
                    }
                  }}
                />
                <Label htmlFor="percent-from-left" small>
                  % from left
                </Label>
              </Control>
              <Control>
                <DebouncedInput
                  id="percent-from-top"
                  type="number"
                  align="center"
                  defaultValue={round(offsetYPercentage * 100, 2)}
                  onChange={({ target: { valueAsNumber } }) => {
                    if (!Number.isNaN(valueAsNumber)) {
                      onChange({
                        offsetYPercentage: round(valueAsNumber / 100, 4),
                      });
                    }
                  }}
                />
                <Label htmlFor="percent-from-top" small>
                  % from top
                </Label>
              </Control>
              <Control>
                <DebouncedInput
                  id="z-index"
                  type="number"
                  align="center"
                  step="1"
                  defaultValue={zIndexOverride || ''}
                  onChange={({ target: { valueAsNumber } }) => {
                    if (!Number.isNaN(valueAsNumber)) {
                      onChange({ zIndexOverride: valueAsNumber });
                    }
                  }}
                  placeholder="#"
                />
                <Label htmlFor="z-index" small>
                  z-index
                </Label>
              </Control>
            </ControlGroup>
          </Control>

          <Control>
            <Button
              onClick={() => {
                track('Builder view', {
                  name: 'Entered Targeting Mode',
                  component: 'TooltipChildSettings',
                });
                onTarget();
              }}
            >
              <FontIcon icon="crosshairs" /> Place tooltip on element
            </Button>
          </Control>

          <Control>
            <Label id="target-element">
              Target element
              {status && <SelectorStatus short status={status} />}
            </Label>
            <RadioButtonGroup aria-labelledby="target-element">
              <RadioButton
                selected={wasPositionedByTargetingInterface}
                onClick={() =>
                  onChange({
                    includeOrderedMatchSelector: false,
                    includeTextMatchSelector: false,
                    selector: autoSelector,
                    selectorSettings: { selector: autoSelector },
                    uiConditions: {
                      next: {
                        type: 'WAIT_FOR_MOUSE_EVENT',
                        params: {
                          event: 'click',
                          selector: [{ selector: autoSelector }],
                        },
                      },
                    },
                    wasPositionedByTargetingInterface: true,
                  })
                }
              >
                Auto
              </RadioButton>
              <RadioButton
                selected={!wasPositionedByTargetingInterface}
                onClick={() =>
                  onChange({ wasPositionedByTargetingInterface: false })
                }
              >
                Manual
              </RadioButton>
            </RadioButtonGroup>

            {!wasPositionedByTargetingInterface && (
              <SelectorBuilder
                count={count}
                status={status}
                onChange={onChange}
                stepChild={stepChild}
                stepType={TOOLTIP}
              />
            )}
          </Control>
        </>
      )}
    </>
  );
}

TooltipChildSettings.propTypes = {
  locked: PropTypes.bool,
  onChange: PropTypes.func,
  onTarget: PropTypes.func,
  stepChild: stepChildShape,
  formatVersion: PropTypes.number,
};
