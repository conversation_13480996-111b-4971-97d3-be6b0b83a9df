import React from 'react';
import PropTypes from 'prop-types';
import {
  Button,
  <PERSON>ontIcon,
  <PERSON><PERSON>,
  Modal,
  ModalHeader,
  Text,
} from 'ext/components/ui';
import {
  NAVIGATE_KNOWLEDGE_ARTICLE,
  NAVIGATE_INSTRUCTIONAL_VIDEO,
} from 'lib/constants';
import { Link, Video, VideoDialog } from './styled';

export function InstructionalVideo({ onClose, visible }) {
  return (
    <Modal contained={false} onClose={onClose} visible={visible}>
      <VideoDialog>
        <FontIcon icon="compass" />
        <ModalHeader>
          <Heading>Building Across Pages</Heading>
        </ModalHeader>

        <Video
          allowFullScreen
          src={NAVIGATE_INSTRUCTIONAL_VIDEO}
          title="Instructional video"
        />

        <Text>
          Watch this 60-second intro to building flows across pages, or
          <Link
            href={NAVIGATE_KNOWLEDGE_ARTICLE}
            rel="noreferrer"
            target="_blank"
          >
            {` read this doc `}
          </Link>
          if you prefer.
        </Text>

        <Button kind="primary" onClick={onClose} type="button">
          Continue
        </Button>
      </VideoDialog>
    </Modal>
  );
}

InstructionalVideo.propTypes = {
  onClose: PropTypes.func,
  visible: PropTypes.bool,
};

export default InstructionalVideo;
