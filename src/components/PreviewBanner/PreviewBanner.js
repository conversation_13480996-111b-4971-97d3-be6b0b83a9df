import React from 'react';
import PropTypes from 'prop-types';
import { connect } from 'react-redux';
import {
  PreviewExperienceContainer,
  PreviewExperienceButton,
} from 'ext/components/PreviewExperience';
import useAnalytics from 'ext/lib/hooks/use-analytics';
import { reload, exit, selectPreview } from 'lib/preview-mode';

export function PreviewBanner({ done, onClickRestart, onClickExit }) {
  const { track } = useAnalytics();

  const handleFlowRestart = () => {
    track('Builder interaction', {
      name: 'Clicked Restart Flow Preview',
      component: 'PreviewBanner',
    });
    onClickRestart();
  };

  return (
    <PreviewExperienceContainer done={done}>
      <PreviewExperienceButton
        onClick={handleFlowRestart}
        ariaLabel="Restart"
        icon="sync"
      />
      <PreviewExperienceButton
        onClick={onClickExit}
        ariaLabel="Exit"
        icon="times"
      />
    </PreviewExperienceContainer>
  );
}

PreviewBanner.propTypes = {
  done: PropTypes.bool,
  onClickRestart: PropTypes.func.isRequired,
  onClickExit: PropTypes.func.isRequired,
};

export default connect(state => ({ done: selectPreview(state).done }), {
  onClickRestart: reload,
  onClickExit: exit,
})(PreviewBanner);
