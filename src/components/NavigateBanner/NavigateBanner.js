import React from 'react';
import PropTypes from 'prop-types';
import { connect } from 'react-redux';
import { Bold, Text } from 'ext/components/ui';
import { exit } from 'lib/navigation-mode';
import { Container, Return } from './styled';

export function NavigateBanner({ onExit }) {
  return (
    <Container>
      <Text>
        <Bold>Navigate:</Bold> go where you want the next step to be, then{' '}
        <Return onClick={onExit} role="button">
          switch back to <Bold>Build mode</Bold>.
        </Return>
      </Text>
    </Container>
  );
}

NavigateBanner.propTypes = {
  onExit: PropTypes.func,
};

const mapDispatchToProps = {
  onExit: () => exit(window.location.href),
};

export default connect(null, mapDispatchToProps)(NavigateBanner);
