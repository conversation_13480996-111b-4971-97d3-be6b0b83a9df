import React from 'react';
import PropTypes from 'prop-types';
import { connect } from 'react-redux';
import ErrorBoundary from 'ext/components/ErrorBoundary';
import TargetingInterface from 'ext/components/TargetingInterface';
import { reload as reloadBuilder } from 'ext/lib/crx';
import { selectHidePermissionModal } from 'ext/entities/permissions';
import DomainPermissionModalContainer from 'ext/components/DomainPermissionModal';
import PreviewBanner from 'components/PreviewBanner';
import NavigateMode from 'components/NavigateMode';
import EditMode from 'components/EditMode';
import { selectMode } from 'entities/mode';

/**
 * Render application mode
 *
 * @param {Mode} mode - Current mode of application
 * @return {React.Element} Rendered mode
 */
export const getMode = mode => {
  switch (mode) {
    case 'preview':
      return <PreviewBanner />;
    case 'target':
      return <TargetingInterface />;
    case 'navigate':
      return <NavigateMode />;
    case 'edit':
    default:
      return <EditMode />;
  }
};

export function App({ mode, reload, hidePermissionModal }) {
  return (
    <ErrorBoundary reload={reload}>
      {!hidePermissionModal ? (
        <DomainPermissionModalContainer />
      ) : (
        <div data-theme="dark">{getMode(mode)}</div>
      )}
    </ErrorBoundary>
  );
}

App.propTypes = {
  mode: PropTypes.oneOf(['preview', 'navigate', 'edit', 'target', 'welcome']),
  reload: PropTypes.func,
  hidePermissionModal: PropTypes.bool.isRequired,
};

const mapStateToProps = state => ({
  mode: selectMode(state),
  hidePermissionModal: selectHidePermissionModal(state),
});

const mapDispatchToProps = {
  reload: () => reloadBuilder(),
};

export default connect(mapStateToProps, mapDispatchToProps)(App);
