/**
 * Move element in a list and reorder
 *
 * @param {array} list - original list of elements
 * @param {number} from - element source index
 * @param {number} to - element destination index
 * @return {array} reordered list with moved element
 */
export const move = (list, from, to) => {
  const result = [...list.slice(0, from), ...list.slice(from + 1)];
  result.splice(to, 0, list[from]);
  return result;
};

/**
 * Process step groups to paged chunks and id lookup
 *
 * @param {object<StepGroup>} groups - keyed step groups
 * @param {string[]} steps - order of step group by id
 * @return {[StepGroup[][], string[][]]} pages step groups and ids
 */
export const process = (groups, steps = []) => {
  const pages = steps.reduce((acc, id, index) => {
    const group = groups[id];
    if (group) {
      if (index === 0 || group.stepType === 'action') {
        acc.push([]);
      }
      acc[acc.length - 1].push({ ...group, index });
    }
    return acc;
  }, []);

  const ids = pages.map(page => page.map(({ id }) => id));

  return [pages, ids];
};
