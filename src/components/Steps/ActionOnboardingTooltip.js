import React from 'react';
import PropTypes from 'prop-types';
import styled from 'styled-components';
import { FontIcon, Heading, Text, Tooltip } from 'ext/components/ui';
import { easing } from 'ext/lib/style/easing';
import useSingleton from 'lib/hooks/use-singleton';

const StyledTooltip = styled(Tooltip)`
  align-items: center;
  background: var(--primary);
  border-color: var(--primary);
  display: flex;
  flex-direction: column;
  pointer-events: unset;
  position: relative;
  width: 250px;

  ${Heading} {
    font-size: var(--medium-large);
    font-weight: var(--bold);
    margin: 0;
    padding: 12px 16px;
    text-align: center;
    width: 100%;
  }

  ${Text} {
    font-size: var(--regular);
    text-align: center;
    white-space: normal;
    word-break: break-word;
  }
`;

const Close = styled.div`
  align-items: center;
  cursor: pointer;
  display: flex;
  justify-content: center;
  opacity: 0.6;
  position: absolute;
  right: 0px;
  top: 0px;
  transition: ${easing('opacity')};
  height: 24px;
  width: 24px;

  &:hover {
    opacity: 1;
  }
`;

const OnboardingImage = () => (
  <svg role="img" width="119px" height="32px" viewBox="0 0 119 32">
    <g stroke="none" strokeWidth="1" fill="white" fillRule="evenodd">
      <g transform="translate(-616.000000, -630.000000)" fill="var(--white)">
        <g transform="translate(557.000000, 617.000000)">
          <g transform="translate(59.000000, 13.000000)">
            <path
              d="M59.2815387,20.6224875 C81.1041332,20.6224875 100.860785,24.183034 118.551493,31.3041269 C100.860785,20.7641973 81.1041332,15.4942325 59.2815387,15.4942325 C37.4589443,15.4942325 17.702293,20.7641973 0.0115848156,31.3041269 C17.702293,24.183034 37.4589443,20.6224875 59.2815387,20.6224875 Z"
              id="horizon"
              fillOpacity="0.25"
            />
            <path
              d="M38.086774,18.1720179 C38.2742733,18.3360802 38.3680233,18.5470172 38.3680233,18.8048289 C38.3680233,19.0626406 38.2742733,19.2735776 38.086774,19.4376399 L31.8992887,24.7813772 C31.7117893,24.9454395 31.5067113,25.0157518 31.2840558,24.9923142 C31.0614003,24.9688766 30.8739004,24.8809861 30.7215572,24.7286429 C30.5692139,24.5762997 30.4930421,24.3829408 30.4930421,24.1485662 L30.4930421,21.0548236 C28.5711715,21.0782612 27.1063316,21.2774791 26.0985213,21.6524784 C24.973524,22.0509148 24.2586821,22.7247415 23.9539952,23.673958 C23.6493082,24.6231744 23.7547767,25.9298121 24.2704007,27.5938705 C24.340713,27.7813699 24.3231349,27.9454321 24.2176664,28.0860568 C24.1121979,28.2266815 23.9715732,28.3145719 23.7957924,28.3497281 C23.6200116,28.3848842 23.4500903,28.3438685 23.286028,28.2266815 C22.4657176,27.6407452 21.7860314,26.8673095 21.2469704,25.9063745 C20.6610341,24.8751271 20.3680662,23.8438792 20.3680662,22.8126319 C20.3680662,20.3517002 21.3524389,18.6056105 23.3211842,17.5743631 C24.8914931,16.7540528 27.2821124,16.3204597 30.4930421,16.273585 L30.4930421,13.4610917 C30.4930421,13.2267171 30.5692139,13.0333581 30.7215572,12.8810149 C30.8739004,12.7286717 31.0614003,12.6407813 31.2840558,12.6173437 C31.5067113,12.5939061 31.7117893,12.6642184 31.8992887,12.8282807 L38.086774,18.1720179 Z"
              id="share"
            />
            <path
              d="M62.1073507,7.78922305 C62.1073507,8.14078498 62.230397,8.43961185 62.4764905,8.68570528 C62.7225839,8.93179872 63.0214108,9.05484503 63.3729727,9.05484503 L70.5448306,9.05484503 L70.5448306,26.3516788 C70.5448306,26.7032407 70.4217843,27.0020676 70.1756909,27.248161 C69.9295974,27.4942545 69.6307706,27.6173008 69.2792086,27.6173008 L51.5605009,27.6173008 C51.2089389,27.6173008 50.9101121,27.4942545 50.6640186,27.248161 C50.4179252,27.0020676 50.2948789,26.7032407 50.2948789,26.3516788 L50.2948789,1.88298713 C50.2948789,1.5314252 50.4179252,1.23259832 50.6640186,0.98650489 C50.9101121,0.740411458 51.2089389,0.617365145 51.5605009,0.617365145 L62.1073507,0.617365145 L62.1073507,7.78922305 Z M70.5448306,7.36734905 L63.7948467,7.36734905 L63.7948467,0.617365145 L64.1112522,0.617365145 C64.4628141,0.617365145 64.761641,0.740411458 65.0077344,0.98650489 L70.1756909,6.15446132 C70.4217843,6.40055475 70.5448306,6.69938163 70.5448306,7.05094356 L70.5448306,7.36734905 Z"
              id="file"
            />
            <path
              d="M96.3779114,10.4923464 C96.612286,10.4923464 96.8115039,10.5743772 96.9755662,10.7384395 C97.1396285,10.9025018 97.2216594,11.1017197 97.2216594,11.3360944 L97.2216594,11.898593 C97.2216594,12.1329676 97.1396285,12.3321856 96.9755662,12.4962478 C96.8115039,12.6603101 96.612286,12.742341 96.3779114,12.742341 C96.3779114,14.3126499 95.979475,15.7247556 95.1826017,16.978659 C94.3857285,18.2325625 93.3661996,19.0704509 92.1240153,19.4923249 C93.3661996,19.9141989 94.3857285,20.7520874 95.1826017,22.0059908 C95.979475,23.2598942 96.3779114,24.6719999 96.3779114,26.2423088 C96.612286,26.2423088 96.8115039,26.3243397 96.9755662,26.488402 C97.1396285,26.6524643 97.2216594,26.8516822 97.2216594,27.0860568 L97.2216594,27.6485555 C97.2216594,27.8829301 97.1396285,28.082148 96.9755662,28.2462103 C96.8115039,28.4102726 96.612286,28.4923035 96.3779114,28.4923035 L84.5654395,28.4923035 C84.3310649,28.4923035 84.131847,28.4102726 83.9677847,28.2462103 C83.8037224,28.082148 83.7216916,27.8829301 83.7216916,27.6485555 L83.7216916,27.0860568 C83.7216916,26.8516822 83.8037224,26.6524643 83.9677847,26.488402 C84.131847,26.3243397 84.3310649,26.2423088 84.5654395,26.2423088 C84.5654395,24.6719999 84.9638759,23.2598942 85.7607492,22.0059908 C86.5576225,20.7520874 87.5771513,19.9141989 88.8193357,19.4923249 C87.5771513,19.0704509 86.5576225,18.2325625 85.7607492,16.978659 C84.9638759,15.7247556 84.5654395,14.3126499 84.5654395,12.742341 C84.3310649,12.742341 84.131847,12.6603101 83.9677847,12.4962478 C83.8037224,12.3321856 83.7216916,12.1329676 83.7216916,11.898593 L83.7216916,11.3360944 C83.7216916,11.1017197 83.8037224,10.9025018 83.9677847,10.7384395 C84.131847,10.5743772 84.3310649,10.4923464 84.5654395,10.4923464 L96.3779114,10.4923464 Z M93.7411989,23.9923142 C93.436512,23.1485662 92.9912009,22.4688805 92.4052646,21.9532565 C91.8193283,21.4376326 91.1747988,21.1798209 90.4716755,21.1798209 C89.7685521,21.1798209 89.1240226,21.4376326 88.5380863,21.9532565 C87.95215,22.4688805 87.506839,23.1485662 87.202152,23.9923142 L93.7411989,23.9923142 Z M93.7411989,14.9923356 C93.9990106,14.2892123 94.1279168,13.5392143 94.1279168,12.742341 L86.8154342,12.742341 C86.8154342,13.5392143 86.9443403,14.2892123 87.202152,14.9923356 L93.7411989,14.9923356 Z"
              id="hourglass-half"
            />
          </g>
        </g>
      </g>
    </g>
  </svg>
);

const ActionOnboardingTooltip = ({ children, onClose, ...rest }) => {
  const allocated = useSingleton('ActionOnboardingTooltip');

  if (!allocated) {
    return children;
  }

  const content = (
    <>
      <Close onClick={onClose} role="button">
        <FontIcon icon="times" size="sm" />
      </Close>

      <OnboardingImage />

      <Heading>How does the user get here?</Heading>
      <Text>
        Click the icon below to change how the user reaches this page.
      </Text>
    </>
  );
  return (
    // eslint-disable-next-line @appcues/jsx-props-no-spreading
    <StyledTooltip label={content} {...rest}>
      {children}
    </StyledTooltip>
  );
};

ActionOnboardingTooltip.propTypes = {
  children: PropTypes.node,
  onClose: PropTypes.func,
};

export default ActionOnboardingTooltip;
