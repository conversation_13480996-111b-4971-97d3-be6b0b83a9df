import React, { useRef } from 'react';
import PropTypes from 'prop-types';
import useToggle from 'ext/lib/hooks/use-toggle';
import Tether from 'ext/components/Tether';
import { Button, FontIcon } from 'ext/components/ui';
import useClickOutside from 'ext/lib/hooks/use-click-outside';
import useAnalytics from 'ext/lib/hooks/use-analytics';
import { AddWrapper } from './styled';
import AddMenu from './AddMenu';

export default function AddManager({ disabled, stepGroup, open: initial }) {
  const $button = useRef();
  const $menu = useRef();

  const [visible, toggle] = useToggle(initial);
  const { track } = useAnalytics();

  useClickOutside([$button, $menu], visible && toggle);

  return (
    <AddWrapper centered={initial}>
      <Tether
        attachment={<AddMenu stepGroup={stepGroup} onClick={toggle} />}
        placement="top"
        visible={visible}
        offset={{ y: 14 }}
      >
        <Button
          ref={$button}
          kind="secondary"
          onClick={() => {
            if (!visible) {
              track('Builder view', {
                name: 'Opened Add Step Menu',
                component: 'AddManager',
              });
            }
            toggle();
          }}
          disabled={disabled}
        >
          <FontIcon icon="plus" />
          Add
        </Button>
      </Tether>
    </AddWrapper>
  );
}

AddManager.propTypes = {
  stepGroup: PropTypes.string,
  open: PropTypes.bool,
  disabled: PropTypes.bool,
};
