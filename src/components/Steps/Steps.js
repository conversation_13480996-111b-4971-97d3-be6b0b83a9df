import React, {
  useCallback,
  useEffect,
  useMemo,
  useState,
  useRef,
} from 'react';
import PropTypes from 'prop-types';
import { connect } from 'react-redux';
import { useDragLayer } from 'react-dnd';
import { attemptInteraction } from 'ext/lib/track';
import move from 'ext/lib/move';
import { ScrollControl } from 'ext/components/ScrollControl';
import useOverflowable from 'ext/lib/hooks/use-overflowable';
import {
  MOVE_STEP_GROUP,
  MOVE_STEP_CHILD,
} from 'lib/metrics/interaction-types';
import { toggleSidebar } from 'entities/application';
import { moveStepGroup } from 'entities/flows';
import {
  focus as focusGroup,
  moveStepChild,
  selectStepGroups,
  STEP_TYPES,
} from 'entities/step-groups';
import { focus as focusChild } from 'entities/step-children';
import { Centered, Container, PageGroup, StepGroups } from './styled';
import { process } from './transforms';
import { STEP_GROUP, STEP_CHILD } from './use-dnd';
import AddManager from './AddManager';
import StepGroup from './StepGroup';

/**
 * NOTE: Currently there is a bug(?) in `react-dnd` where unmounting drag/drop
 *       zones while a drag is in progress will throw an invariant violation
 *       re: being unable to find a valid element. Despite the warning, the
 *       functionality continues to work because the issue seems to be the
 *       internal redux store in `react-dnd` continue to dispatch actions on
 *       unmounted components. This is why we are currently using the `key`
 *       index as the `PageGroup` key, but should use something more unique once
 *       the underlying issue is resolved.
 */

export const Steps = ({
  groupOrder,
  groups,
  locked,
  onClickSelected,
  onSelect,
  onChildMove,
  onGroupMove,
  recordMetric,
  selected = {},
}) => {
  const $overflow = useRef();

  const [order, setOrder] = useState(groupOrder);

  const { dragging } = useDragLayer(monitor => ({
    dragging: monitor.isDragging(),
  }));

  const [overflowing, scroll] = useOverflowable($overflow);

  useEffect(() => {
    setOrder(groupOrder);
  }, [groupOrder]);

  const [pages, ids] = useMemo(() => {
    return process(groups, order);
  }, [groups, order]);

  const handleClick = (child, parent) => {
    if (
      selected.stepGroup === parent &&
      (typeof child === 'undefined' || selected.stepChild === child)
    ) {
      onClickSelected();
    } else {
      onSelect(child, parent);
    }
  };

  const handleDrag = useCallback(
    (from, to) => {
      setOrder(current => move(current, from, to));
    },
    [setOrder]
  );

  const handleDrop = useCallback(
    (item, commit) => {
      if (commit) {
        const payload = {
          from: item.index,
          to: item.target,
        };

        if (item.type === STEP_GROUP) {
          recordMetric(MOVE_STEP_GROUP);
          onGroupMove(item.id, payload);
        } else if (item.type === STEP_CHILD) {
          recordMetric(MOVE_STEP_CHILD);
          onChildMove(item.stepChildId, { ...payload, parent: item.id });
        }
      } else {
        setOrder(groupOrder);
      }
    },
    [groupOrder, onGroupMove, onChildMove, setOrder, recordMetric]
  );

  if (pages.length === 0) {
    return null;
  }

  return (
    <>
      {overflowing && (
        <ScrollControl disabled={scroll.start} side="left" target={$overflow} />
      )}

      <Container dragging={dragging} ref={$overflow} scrollable={overflowing}>
        <Centered>
          {pages.map((page, key) => (
            // eslint-disable-next-line react/no-array-index-key
            <PageGroup key={key}>
              <StepGroups role="group">
                {page.map(({ actionType, id, index, steps, stepType }) => (
                  <StepGroup
                    key={id}
                    actionType={actionType}
                    id={id}
                    index={index}
                    onClick={handleClick}
                    onDrag={handleDrag}
                    onDrop={handleDrop}
                    selected={selected}
                    steps={steps}
                    stepType={stepType}
                  />
                ))}

                {ids[key].includes(selected.stepGroup) && (
                  <AddManager
                    stepGroup={selected.stepGroup}
                    disabled={locked}
                  />
                )}
              </StepGroups>
            </PageGroup>
          ))}
        </Centered>
      </Container>

      {overflowing && (
        <ScrollControl disabled={scroll.end} side="right" target={$overflow} />
      )}
    </>
  );
};

Steps.propTypes = {
  groupOrder: PropTypes.arrayOf(PropTypes.string),
  groups: PropTypes.objectOf(
    PropTypes.shape({
      actionType: PropTypes.oneOf(['redirect', 'wait-for-page']),
      id: PropTypes.string,
      index: PropTypes.number,
      steps: PropTypes.arrayOf(PropTypes.string),
      stepType: PropTypes.oneOf(STEP_TYPES),
    })
  ),
  locked: PropTypes.bool,
  onClickSelected: PropTypes.func,
  onSelect: PropTypes.func,
  onChildMove: PropTypes.func,
  onGroupMove: PropTypes.func,
  recordMetric: PropTypes.func,
  selected: PropTypes.shape({
    stepGroup: PropTypes.string,
    stepChild: PropTypes.string,
  }),
};

const mapStateToProps = state => ({
  groups: selectStepGroups(state),
});

const mapDispatchToProps = {
  onClickSelected: toggleSidebar,
  onSelect: (child, parent) =>
    child ? focusChild(child, parent) : focusGroup(parent),
  onGroupMove: moveStepGroup,
  onChildMove: moveStepChild,
  recordMetric: attemptInteraction,
};

export default connect(mapStateToProps, mapDispatchToProps)(Steps);
