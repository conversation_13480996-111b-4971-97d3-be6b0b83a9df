import React, { forwardRef } from 'react';
import PropTypes from 'prop-types';
import { connect } from 'react-redux';
import {
  Menu,
  List,
  ListItem,
  ListBody,
  MenuHeading,
  StepIcon,
  FontIcon,
} from 'ext/components/ui';
import { attemptInteraction } from 'ext/lib/track';
import useAnalytics from 'ext/lib/hooks/use-analytics';
import {
  ADD_STEP_CHILD,
  ADD_STEP_GROUP,
  ADD_ACTION_STEP_GROUP,
} from 'lib/metrics/interaction-types';
import { start } from 'lib/navigation-mode';
import {
  add as addGroup,
  getStepLabel,
  selectStepGroup,
  STEP_TYPES,
  MODAL,
  SLIDEOUT,
  HOTSPOT,
  TOOLTIP,
  ACTION,
} from 'entities/step-groups';
import { add as addChild } from 'entities/step-children';

export const AddMenu = forwardRef(
  (
    {
      stepType = '',
      onAddAction,
      onAddGroup,
      onAddChild,
      onClick,
      recordMetric,
    },
    ref
  ) => {
    const label = getStepLabel(stepType);
    const canNavigate = stepType && stepType !== ACTION;
    const { track } = useAnalytics();

    const createHandleAddGroup = type => () => {
      track('Builder interaction', {
        name: `Clicked Add ${getStepLabel(type)} Group`,
        component: 'AddMenu',
      });
      recordMetric(ADD_STEP_GROUP);
      onAddGroup(type);
    };
    const handleAddChild = () => {
      track('Builder interaction', {
        name: 'Clicked Add to Current Group',
        component: 'AddMenu',
      });
      recordMetric(ADD_STEP_CHILD);
      onAddChild(stepType);
    };
    const handleAddAction = () => {
      track('Builder view', {
        name: 'Entered Navigate Mode',
        component: 'AddMenu',
      });
      recordMetric(ADD_ACTION_STEP_GROUP);
      onAddAction();
    };

    return (
      <Menu onClick={onClick} ref={ref}>
        {canNavigate && (
          <>
            <MenuHeading>Build on a new page</MenuHeading>
            <List aria-label="Add navigation" role="list">
              <ListItem onClick={handleAddAction}>
                <FontIcon icon="reply" flip="horizontal" />
                <ListBody>Add navigate step</ListBody>
              </ListItem>
            </List>
          </>
        )}
        <MenuHeading>Add a new group</MenuHeading>
        <List aria-label="Add group" role="list">
          <ListItem onClick={createHandleAddGroup(MODAL)}>
            <StepIcon type={MODAL} />
            <ListBody>Modal group</ListBody>
          </ListItem>
          <ListItem onClick={createHandleAddGroup(SLIDEOUT)}>
            <StepIcon type={SLIDEOUT} />
            <ListBody>Slideout group</ListBody>
          </ListItem>
          <ListItem onClick={createHandleAddGroup(TOOLTIP)}>
            <StepIcon type={TOOLTIP} />
            <ListBody>Tooltip group</ListBody>
          </ListItem>
          <ListItem onClick={createHandleAddGroup(HOTSPOT)}>
            <StepIcon type={HOTSPOT} />
            <ListBody>Hotspot group</ListBody>
          </ListItem>
        </List>
        {stepType && canNavigate && (
          <>
            <MenuHeading>Add to current group</MenuHeading>
            <List aria-label={`Add ${label}`} role="list">
              <ListItem onClick={handleAddChild}>
                <StepIcon type={stepType} />
                <ListBody>Add {label}</ListBody>
              </ListItem>
            </List>
          </>
        )}
      </Menu>
    );
  }
);

AddMenu.propTypes = {
  onAddAction: PropTypes.func,
  onAddChild: PropTypes.func,
  onAddGroup: PropTypes.func,
  stepType: PropTypes.oneOf(STEP_TYPES),
  onClick: PropTypes.func,
  recordMetric: PropTypes.func,
};

const mapStateToProps = (state, { stepGroup }) => {
  if (!stepGroup) return {};
  const { stepType } = selectStepGroup(state, stepGroup);
  return { stepType };
};

const mapDispatchToProps = {
  onAddAction: start,
  onAddChild: addChild,
  onAddGroup: addGroup,
  recordMetric: attemptInteraction,
};

const options = { forwardRef: true };

export default connect(
  mapStateToProps,
  mapDispatchToProps,
  null,
  options
)(AddMenu);
