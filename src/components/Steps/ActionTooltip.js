import React from 'react';
import PropTypes from 'prop-types';
import { connect } from 'react-redux';
import styled from 'styled-components';
import { FontIcon, Heading, Text, Tooltip } from 'ext/components/ui';
import { attemptInteraction } from 'ext/lib/track';
import { UPDATE_USAGE_PROPERTY } from 'lib/metrics/interaction-types';
import { selectStepGroup } from 'entities/step-groups';
import { update, selectUsageProperty } from 'entities/usage-properties';
import ActionOnboardingTooltip from './ActionOnboardingTooltip';

const ONBOARDING_PROPERTY = 'hasMadeNewPageChangeType';

const Body = styled.section`
  padding: 12px 24px;
  width: 100%;
`;

const Type = styled.div`
  align-items: center;
  display: flex;
  justify-content: center;
  padding: 8px 0;

  ${FontIcon} {
    margin-right: 8px;
  }
`;

const Description = styled.div`
  align-items: center;
  display: flex;
  flex-direction: column;
  font-size: var(--regular);
  padding: 8px 0;

  ${Text} {
    text-align: center;
    white-space: normal;
    word-break: break-word;
  }
`;

const StyledTooltip = styled(Tooltip)`
  align-items: center;
  display: flex;
  flex-direction: column;
  padding: 0;
  width: 300px;

  ${Heading} {
    background: var(--secondary);
    border-top-left-radius: inherit;
    border-top-right-radius: inherit;
    font-size: var(--medium-large);
    font-weight: var(--bold);
    margin: 0;
    padding: 8px 16px;
    text-align: center;
    width: 100%;
  }
`;

export const ActionTooltip = ({
  isOnboarding,
  onOnboardingComplete,
  recordMetric,
  type,
  url,
  ...rest
}) => {
  if (isOnboarding) {
    return (
      <ActionOnboardingTooltip
        // eslint-disable-next-line @appcues/jsx-props-no-spreading
        {...rest}
        onClose={() => {
          recordMetric();
          onOnboardingComplete();
        }}
        visible
      />
    );
  }

  const content = (
    <>
      <Heading>How does the user get here?</Heading>
      <Body>
        <Type>
          <FontIcon icon={type === 'redirect' ? 'share' : 'hiking'} size="sm" />
          <Text bold>
            {type === 'redirect' ? 'Redirect' : 'Let Them Navigate'}
          </Text>
        </Type>

        <Description>
          <Text>
            {type === 'redirect'
              ? 'Automatically take the user to the page:'
              : 'Wait for the user to reach the page:'}
          </Text>
          <Text bold>{url}</Text>
        </Description>
      </Body>
    </>
  );

  // eslint-disable-next-line @appcues/jsx-props-no-spreading
  return <StyledTooltip label={content} {...rest} />;
};

ActionTooltip.propTypes = {
  ...Tooltip.propTypes,
  id: PropTypes.string,
  isOnboarding: PropTypes.bool,
  onOnboardingComplete: PropTypes.func,
  recordMetric: PropTypes.func,
  type: PropTypes.oneOf(['redirect', 'wait-for-page']),
  url: PropTypes.string,
};

const mapStateToProps = (state, { id }) => {
  const { actionType, params = {} } = selectStepGroup(state, id);
  const onboarded = selectUsageProperty(state, ONBOARDING_PROPERTY);

  return {
    isOnboarding: onboarded !== null && !onboarded,
    type: actionType,
    url: params.url,
  };
};

const mapDispatchToProps = {
  onOnboardingComplete: () => update({ [ONBOARDING_PROPERTY]: true }),
  recordMetric: () => attemptInteraction(UPDATE_USAGE_PROPERTY),
};

export default connect(mapStateToProps, mapDispatchToProps)(ActionTooltip);
