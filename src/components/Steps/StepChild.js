import React, { forwardRef } from 'react';
import { connect } from 'react-redux';
import PropTypes from 'prop-types';
import mergeRefs from 'ext/lib/merge-refs';
import { StepIcon, Text, Tooltip, FontIcon } from 'ext/components/ui';
import { INVALID_STATUS } from 'lib/validate-selector';
import useSelectorValidity from 'lib/hooks/use-selector-validity';
import {
  getStepLabel,
  MODAL,
  SLIDEOUT,
  HOTSPOT,
  TOOLTIP,
} from 'entities/step-groups';
import { selectStepChild, stepChildShape } from 'entities/step-children';
import { useDnd, STEP_CHILD } from './use-dnd';
import { StepChildWrapper, InvalidSelectorTooltip } from './styled';

const StepChild = forwardRef(
  (
    { id, index, onClick, onDrag, onDrop, selected, stepType, stepChild = {} },
    ref
  ) => {
    const options = { id, index, onDrag, onDrop };
    const [{ dragging, draggable, droppable }, connectors] = useDnd(
      STEP_CHILD,
      options
    );
    const selectorInfo = useSelectorValidity(stepChild);
    const invalid =
      selected &&
      (stepType === TOOLTIP || stepType === HOTSPOT) &&
      INVALID_STATUS.includes(selectorInfo && selectorInfo.status);

    const handleClick = () => onClick(id);

    /**
     * NOTE: The existance of `ref` means this step child is the only step child
     *       in its step group and thus will be used as the drag handle for
     *       reordering the step group since a separate drag handle will not
     *       exist. Otherwise, we can assume that it is not the only child and
     *       the child will instead be used as both the drag and drop target for
     *       reordering itself within the step group using its own connectors.
     */
    const connector = ref || mergeRefs([connectors.drag, connectors.drop]);

    const StepChildTooltip = invalid ? InvalidSelectorTooltip : Tooltip;
    const label = invalid ? (
      <Text>
        The target element cannot be found. Click on the beacon to reposition.
      </Text>
    ) : (
      <Text capitalize>{getStepLabel(stepType)} step</Text>
    );

    return (
      <StepChildTooltip visible={draggable ? null : false} label={label}>
        <StepChildWrapper
          ref={connector}
          aria-label="step-child"
          aria-selected={selected}
          dragging={dragging}
          droppable={droppable}
          onClick={handleClick}
          selected={selected}
        >
          {invalid && <FontIcon icon="exclamation-triangle" type={stepType} />}
          <StepIcon type={stepType} />
        </StepChildWrapper>
      </StepChildTooltip>
    );
  }
);

StepChild.propTypes = {
  id: PropTypes.string,
  index: PropTypes.number,
  onClick: PropTypes.func,
  onDrag: PropTypes.func,
  onDrop: PropTypes.func,
  selected: PropTypes.bool,
  stepType: PropTypes.oneOf([MODAL, TOOLTIP, HOTSPOT, SLIDEOUT]),
  stepChild: stepChildShape,
};

const mapStateToProps = (state, { id }) => ({
  stepChild: selectStepChild(state, id),
});

export default connect(mapStateToProps, null, null, { forwardRef: true })(
  StepChild
);
