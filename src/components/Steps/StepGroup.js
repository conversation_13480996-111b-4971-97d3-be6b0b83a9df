import React, { useCallback, useEffect, useState } from 'react';
import PropTypes from 'prop-types';
import { FontIcon, Text, Tooltip } from 'ext/components/ui';
import useAnalytics from 'ext/lib/hooks/use-analytics';
import move from 'ext/lib/move';
import { getStepLabel, STEP_TYPES } from 'entities/step-groups';
import { useDnd, STEP_GROUP } from './use-dnd';
import { <PERSON>ag<PERSON><PERSON>le, StepGroupWrapper, StepChildren } from './styled';
import Action from './Action';
import ActionTooltip from './ActionTooltip';
import StepChild from './StepChild';

const StepGroup = ({
  actionType,
  id,
  index,
  onClick,
  onDrag,
  onDrop,
  selected = {},
  stepType,
  steps,
}) => {
  const [order, setOrder] = useState(steps);

  useEffect(() => {
    setOrder(steps);
  }, [steps]);

  const options = { id, index, onDrag, onDrop };
  const [{ dragging, draggable, droppable }, connectors] = useDnd(
    STEP_GROUP,
    options
  );
  const { track } = useAnalytics();

  const handleClick = useCallback(
    stepChildId => {
      onClick(stepChildId, id);
    },
    [id, onClick]
  );

  const handleDrag = useCallback(
    (from, to) => {
      setOrder(current => move(current, from, to));
    },
    [setOrder]
  );

  const handleDrop = useCallback(
    (item, commit) => {
      if (commit) {
        onDrop({ ...item, id, stepChildId: item.id }, commit);
      } else {
        setOrder(steps);
      }
    },
    [id, onDrop, setOrder, steps]
  );

  const isSelected = stepChildId =>
    id === selected.stepGroup && stepChildId === selected.stepChild;

  const count = steps ? steps.length : 0;

  if (stepType !== 'action' && count === 0) {
    return null;
  }

  return (
    <StepGroupWrapper
      ref={connectors.drop}
      dragging={dragging}
      droppable={droppable}
    >
      {stepType === 'action' && (
        <ActionTooltip visible={draggable ? null : false} id={id} wrapped>
          <Action
            ref={connectors.drag}
            onClick={() => {
              if (id !== selected.stepGroup) {
                track('Builder interaction', {
                  name: 'Clicked Flow Step',
                  component: 'StepGroup',
                });
              }
              handleClick();
            }}
            selected={id === selected.stepGroup}
            type={actionType}
          />
        </ActionTooltip>
      )}

      {stepType !== 'action' && (
        <StepChildren ref={connectors.preview}>
          {count > 1 && (
            <Tooltip
              visible={draggable ? null : false}
              label={<Text capitalize>{getStepLabel(stepType)} group</Text>}
            >
              <DragHandle ref={connectors.drag}>
                <FontIcon
                  aria-label="drag-handle"
                  icon="grip-lines-vertical"
                  role="img"
                />
              </DragHandle>
            </Tooltip>
          )}

          {order.map((child, mark) => (
            <StepChild
              id={child}
              index={mark}
              key={child}
              onClick={() => {
                if (!isSelected(child)) {
                  track('Builder interaction', {
                    name: 'Clicked Flow Step',
                    component: 'StepGroup',
                  });
                }
                handleClick(child);
              }}
              onDrag={handleDrag}
              onDrop={handleDrop}
              ref={count === 1 ? connectors.drag : null}
              selected={isSelected(child)}
              stepType={stepType}
            />
          ))}
        </StepChildren>
      )}
    </StepGroupWrapper>
  );
};

StepGroup.propTypes = {
  actionType: PropTypes.oneOf(['redirect', 'wait-for-page']),
  id: PropTypes.string,
  index: PropTypes.number,
  onClick: PropTypes.func,
  onDrag: PropTypes.func,
  onDrop: PropTypes.func,
  selected: PropTypes.shape({
    stepGroup: PropTypes.string,
    stepChild: PropTypes.string,
  }),
  steps: PropTypes.arrayOf(PropTypes.string),
  stepType: PropTypes.oneOf(STEP_TYPES),
};

export default StepGroup;
