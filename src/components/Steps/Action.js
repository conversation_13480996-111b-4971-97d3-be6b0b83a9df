import React, { forwardRef } from 'react';
import PropTypes from 'prop-types';
import { FontIcon } from 'ext/components/ui';
import { ActionWrapper } from './styled';

const RedirectIcon = () => (
  <FontIcon aria-label="redirect" icon="share" role="img" />
);

const WaitForPageIcon = () => (
  <FontIcon aria-label="wait-for-page" icon="hiking" role="img" />
);

const Action = forwardRef(({ type, onClick, selected }, ref) => (
  <ActionWrapper ref={ref} onClick={() => onClick()} selected={selected}>
    {type === 'redirect' ? <RedirectIcon /> : <WaitForPageIcon />}
  </ActionWrapper>
));

Action.propTypes = {
  onClick: PropTypes.func,
  selected: PropTypes.bool,
  type: PropTypes.oneOf(['redirect', 'wait-for-page']),
};

export default Action;
