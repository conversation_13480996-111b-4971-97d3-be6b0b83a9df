import styled from 'styled-components';

type StyledGridType = {
  $hasItems: boolean;
};

const StyledGrid = styled.div<StyledGridType>`
  display: grid;
  grid-template-columns: ${({ $hasItems }) =>
    $hasItems ? `repeat(3, 1fr)` : `1fr`};
  gap: var(--spacing-regular);
  max-height: 500px;
  min-height: 235px;
  overflow-y: auto;
  padding: var(--spacing-small);
  border: 1px solid var(--border-default);
  border-radius: var(--border-radius-small);
`;

type GridType = {
  children: React.ReactNode;
  hasItems: boolean;
};

export const Grid = ({ children, hasItems }: GridType) => (
  <StyledGrid $hasItems={hasItems}>{children}</StyledGrid>
);
