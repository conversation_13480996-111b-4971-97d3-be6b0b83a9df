import React, { useRef } from 'react';
import PropTypes from 'prop-types';
import useToggle from 'ext/lib/hooks/use-toggle';
import useAnalytics from 'ext/lib/hooks/use-analytics';
import {
  ButtonGroup,
  DropdownButton,
  FontIcon,
  Tooltip,
  SquareButton,
} from 'ext/components/ui';
import useClickOutside from 'ext/lib/hooks/use-click-outside';
import FlowCreate from './FlowCreate';
import FlowDetails from './FlowDetails';
import FlowSelector from './FlowSelector';

export default function FlowManager({ flow }) {
  const $button = useRef();
  const $selector = useRef();

  const [isSelectorVisible, toggleSelector] = useToggle();
  const [isCreatorVisible, toggleCreator] = useToggle();
  const [isEditorVisible, toggleEditor] = useToggle();

  const { track } = useAnalytics();

  useClickOutside([$button, $selector], isSelectorVisible && toggleSelector);

  return (
    <ButtonGroup>
      <DropdownButton
        ref={$button}
        onClick={() => {
          if (!isSelectorVisible) {
            track('Builder view', {
              name: 'Opened Flow Selector',
              component: 'FlowManager',
            });
          }
          toggleSelector();
        }}
      >
        {flow ? flow.name : 'Choose existing...'}
      </DropdownButton>

      <FlowSelector
        ref={$selector}
        selected={flow && flow.id}
        onClose={toggleSelector}
        onCreate={toggleCreator}
        visible={isSelectorVisible}
      />

      <FlowCreate onClose={toggleCreator} visible={isCreatorVisible} />

      {flow && (
        <FlowDetails
          flow={flow}
          onClose={toggleEditor}
          visible={isEditorVisible}
        />
      )}

      {flow && (
        <Tooltip label="Flow details">
          <SquareButton
            onClick={() => {
              track('Builder view', {
                name: 'Opened Flow Details',
                component: 'FlowManager',
              });
              toggleEditor();
            }}
            aria-label="Edit Flow"
          >
            <FontIcon icon="edit" />
          </SquareButton>
        </Tooltip>
      )}
    </ButtonGroup>
  );
}

FlowManager.propTypes = {
  flow: PropTypes.shape({
    id: PropTypes.string,
    name: PropTypes.string,
  }),
};
