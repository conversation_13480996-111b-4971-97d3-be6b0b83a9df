import React, { useCallback, useEffect, useState } from 'react';
import PropTypes from 'prop-types';
import { connect } from 'react-redux';
import {
  Button,
  ButtonGroup,
  FontIcon,
  Heading,
  Input,
  Modal,
  ModalHeader,
  Text,
} from 'ext/components/ui';
import useAnalytics from 'ext/lib/hooks/use-analytics';
import { selectHasLocationMismatch } from 'lib/selectors';
import { update } from 'entities/flows';

import { Form, FormGroup, Label, Warning, UpdateBuildUrlText } from './styled';

function isValidURL(url) {
  try {
    // eslint-disable-next-line no-new
    new URL(url);
  } catch {
    return false;
  }
  return true;
}

export const FlowDetails = ({
  flow = {},
  onClose,
  onSave,
  visible = true,
  hasLocationMismatch,
}) => {
  const [name, setName] = useState('');
  const [buildUrl, setBuildUrl] = useState('');
  const [buildUrlError, setBuildUrlError] = useState(false);
  const { track } = useAnalytics();

  useEffect(() => {
    setName(flow.name || '');
  }, [flow, setName]);

  useEffect(() => {
    setBuildUrl(flow.previewUrl || '');
  }, [flow, setBuildUrl]);

  const trimmedName = name.trim();
  const trimmedBuildUrl = buildUrl.trim();
  const disabled =
    (trimmedName.length === 0 || trimmedName === flow.name) &&
    (trimmedBuildUrl.length === 0 || trimmedBuildUrl === flow.previewUrl);

  const handleNameChange = useCallback(
    ({ target: { value } }) => {
      setName(value);
    },
    [setName]
  );

  const handleBuildUrlChange = useCallback(
    ({ target: { value } }) => {
      if (buildUrlError) {
        setBuildUrlError(false);
      }
      setBuildUrl(value);
    },
    [setBuildUrl, buildUrlError, setBuildUrlError]
  );

  const handleSave = event => {
    track('Builder interaction', {
      name: 'Updated Flow Details',
      component: 'FlowDetails',
    });

    event.preventDefault();
    if (isValidURL(trimmedBuildUrl)) {
      setBuildUrlError(false);
      onSave({ id: flow.id, name: trimmedName, previewUrl: trimmedBuildUrl });
      onClose();
    } else {
      setBuildUrlError(true);
    }
  };

  const handleCancel = () => {
    setName(flow.name || '');
    setBuildUrl(flow.previewUrl || '');
    onClose();
  };

  return (
    <Modal onClose={onClose} visible={visible}>
      <ModalHeader>
        <Heading>Edit Flow</Heading>
      </ModalHeader>

      {hasLocationMismatch && (
        <Warning>
          <FontIcon size="xs" icon="info-circle" />
          <Text>Current URL does not match the build URL</Text>
        </Warning>
      )}

      <Form disabled={disabled} onSubmit={handleSave} role="form">
        <FormGroup>
          <Label htmlFor="name">Flow Name</Label>
          <Input
            id="name"
            onChange={handleNameChange}
            placeholder="Enter flow name..."
            role="textbox"
            value={name}
          />
        </FormGroup>

        <FormGroup>
          <Label htmlFor="id">Flow ID</Label>
          <Input id="id" readOnly role="textbox" value={flow.id} />
        </FormGroup>

        <FormGroup>
          <Label htmlFor="buildUrl">Build URL</Label>
          <Input
            id="buildUrl"
            onChange={handleBuildUrlChange}
            placeholder="Enter build URL..."
            role="textbox"
            value={buildUrl}
          />
          {buildUrlError && (
            <Warning>
              <FontIcon size="xs" icon="info-circle" />
              <Text>{trimmedBuildUrl} is an invalid URL</Text>
            </Warning>
          )}
        </FormGroup>

        {(hasLocationMismatch || buildUrlError) && (
          <UpdateBuildUrlText>
            <Text>Want to use the current URL? </Text>
            <Text
              bold
              onClick={() =>
                handleBuildUrlChange({
                  target: { value: window.location.href },
                })
              }
            >
              Switch Now
            </Text>
          </UpdateBuildUrlText>
        )}

        <ButtonGroup right>
          <Button kind="tertiary" onClick={handleCancel} type="button">
            Cancel
          </Button>
          <Button
            disabled={disabled}
            kind="primary"
            onClick={handleSave}
            type="submit"
          >
            Save
          </Button>
        </ButtonGroup>
      </Form>
    </Modal>
  );
};

FlowDetails.propTypes = {
  flow: PropTypes.shape({
    id: PropTypes.string,
    name: PropTypes.string,
  }),
  onClose: PropTypes.func,
  onSave: PropTypes.func,
  visible: PropTypes.bool,
  hasLocationMismatch: PropTypes.bool,
};

const mapStateToProps = state => {
  return {
    hasLocationMismatch: selectHasLocationMismatch(state),
  };
};

const mapDispatchToProps = {
  onSave: update,
};

export default connect(mapStateToProps, mapDispatchToProps)(FlowDetails);
