import styled from 'styled-components';
import { Text } from 'ext/components/ui';

export const Form = styled.form`
  display: flex;
  flex-direction: column;
  // width = 350px (desired width) - 68px (32px padding on each side)..from Dialog
  width: 282px;
`;

export const FormGroup = styled.div`
  & + & {
    margin-top: 16px;
  }
`;

export const Label = styled.label`
  display: block;
  margin-bottom: 8px;
  font-size: var(--medium-large);
`;

export const Warning = styled.div`
  color: var(--error-light);
  display: flex;
  align-items: baseline;
  margin: 10px 0;

  &:last-child {
    margin-bottom: 0;
  }

  ${Text} {
    font-size: var(--regular);
    margin-left: 6px;
  }
`;

export const UpdateBuildUrlText = styled.div`
  font-size: var(--small);

  ${Text}:last-of-type {
    color: var(--primary-light);
    cursor: pointer;
  }
`;
