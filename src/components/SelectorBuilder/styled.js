import styled from 'styled-components';
import { Checkbox, Dropdown, Input } from 'ext/components/ui';

export const Container = styled.div`
  border-left: 4px solid var(--primary-light);
  margin-top: 16px;
  padding: 8px 0 8px 16px;
`;

export const Heading = styled.h4`
  color: var(--text-color);
  font-size: var(--x-small);
  font-weight: var(--bold);
  margin-bottom: 4px;
  margin-top: 0;
  text-transform: uppercase;
`;

export const Description = styled.p`
  color: var(--text-color);
  font-size: var(--regular);
  font-weight: var(--normal);
  margin: 0 8px 12px;
`;

export const Link = styled.a`
  color: var(--primary-light);
  font-weight: var(--bold);
  text-decoration: none;
`;

export const Text = styled.p`
  flex-shrink: 1;
  margin: 0;
  overflow-x: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
`;

export const Badge = styled.span`
  background: var(--background-light);
  border-radius: 4px;
  color: var(--white);
  flex-grow: 1;
  font-size: var(--x-small);
  font-weight: var(--bold);
  padding: 2px 8px;
  text-transform: uppercase;
`;

export const Option = styled.div`
  align-items: center;
  display: flex;
  justify-content: space-between;
  margin-top: 12px;

  > ${Checkbox} {
    margin: 0 8px 0 2px;
  }

  ${Dropdown}, ${Input} {
    flex-grow: 1;
  }
`;

export const Options = styled.div`
  display: flex;
  flex-direction: column;
  margin-bottom: 20px;

  > ${Checkbox} {
    margin: 20px 2px 0;

    > span {
      align-items: center;
      display: flex;
      justify-content: flex-start;
      min-width: 0;
    }
  }

  ${Badge} {
    margin-left: 8px;
  }
`;

export const ManualSelector = styled.div`
  ${Input} {
    border: 2px solid var(--tint-white);
    margin-bottom: 20px;
    min-height: 48px;
    resize: vertical;

    &:focus {
      border: 2px solid var(--tint-white);
      outline: none;
    }
  }
`;
