import React, { useMemo } from 'react';
import PropTypes from 'prop-types';
import { Checkbox, DebouncedInput, Dropdown } from 'ext/components/ui';
import {
  buildSelector,
  sortSelectorOptions,
  processSelectorOptions,
  createOrderOptions,
} from 'ext/lib/build-selector';
import useSelectorParts from 'ext/lib/hooks/use-selector-parts';
import { STATUS } from 'lib/validate-selector';
import SelectorStatus from 'components/SelectorStatus';
import { TOOLTIP, HOTSPOT, STEP_TYPES_LABELS } from 'entities/step-groups';
import { stepChildShape } from 'entities/step-children';
import {
  Container,
  Heading,
  Description,
  Link,
  Text,
  Badge,
  Option,
  Options,
  ManualSelector,
} from './styled';

export default function SelectorBuilder({
  count,
  status,
  onChange,
  stepChild,
  stepType,
}) {
  const {
    includeOrderedMatchSelector,
    includeTextMatchSelector,
    selector,
    selectorOptions = {},
    selectorOrderValue,
    selectorTextValue,
  } = stepChild;

  const sorted = useMemo(() => {
    return sortSelectorOptions(selectorOptions);
  }, [selectorOptions]);

  const { parents } = useSelectorParts(selector);

  const options = useMemo(() => {
    return processSelectorOptions(sorted, selector);
  }, [sorted, selector]);

  // Consolidate selector settings before triggering onChange handler
  const handleChange = changes => {
    onChange({
      ...changes,
      uiConditions: {
        next: {
          type: 'WAIT_FOR_MOUSE_EVENT',
          params: {
            event: 'click',
            selector: [changes.selectorSettings],
          },
        },
      },
    });
  };

  const handleSelectorToggle = (type, value) => checked => {
    const updated = buildSelector(
      parents,
      sortSelectorOptions({
        ...options,
        [type]: {
          ...options[type],
          [value]: checked,
        },
      })
    );
    handleChange({
      selector: updated,
      selectorSettings: {
        selector: updated,
        ...(includeOrderedMatchSelector && {
          order_filter: selectorOrderValue,
        }),
        ...(includeTextMatchSelector && {
          text_filter: selectorTextValue,
        }),
      },
    });
  };

  return (
    <Container aria-label="Selector builder">
      <Heading>1. Build a selector</Heading>
      <Description>
        A CSS selector is used to find the element on your page to anchor the{' '}
        {STEP_TYPES_LABELS[stepType]}.{' '}
        <Link
          href="http://docs.appcues.com/article/232-css-selectors"
          target="_blank"
          rel="noopener noreferrer"
        >
          Read more
        </Link>
      </Description>

      <Options>
        {Object.entries(options).map(([type, values]) =>
          Object.entries(values).map(([value, checked]) => (
            <Checkbox
              key={value}
              label={value}
              checked={checked}
              onChange={handleSelectorToggle(type, value)}
            >
              <Text>{value}</Text>
              <Badge>{type}</Badge>
            </Checkbox>
          ))
        )}
      </Options>

      <ManualSelector>
        <DebouncedInput
          as="textarea"
          aria-label="Manual selector"
          defaultValue={selector}
          onChange={({ target: { value } }) =>
            handleChange({
              selector: value,
              selectorSettings: { selector: value },
            })
          }
        />
      </ManualSelector>

      <Heading>2. Add additional filters</Heading>
      <Description>
        If more than one item is found, use these filters to narrow the results.
      </Description>

      <Options>
        <Option>
          <Checkbox
            label="By order"
            checked={includeOrderedMatchSelector}
            onChange={checked =>
              handleChange({
                ...(!checked && { selectorOrderValue: null }),
                includeOrderedMatchSelector: checked,
                selectorSettings: {
                  selector,
                  ...(includeTextMatchSelector && {
                    text_filter: selectorTextValue,
                  }),
                },
              })
            }
          >
            <Text>By order</Text>
          </Checkbox>
          <Dropdown
            disabled={!includeOrderedMatchSelector}
            options={
              includeOrderedMatchSelector ? createOrderOptions(count) : []
            }
            defaultValue={selectorOrderValue}
            onChange={order =>
              handleChange({
                selectorOrderValue: order,
                selectorSettings: {
                  selector,
                  order_filter: order,
                  ...(includeTextMatchSelector && {
                    text_filter: selectorTextValue,
                  }),
                },
              })
            }
            placeholder={`_ of ${count}`}
          />
        </Option>

        <Option>
          <Checkbox
            label="By text"
            checked={includeTextMatchSelector}
            onChange={checked =>
              handleChange({
                includeTextMatchSelector: checked,
                selectorSettings: {
                  selector,
                  ...(checked && { text_filter: selectorTextValue }),
                  ...(includeOrderedMatchSelector && {
                    order_filter: selectorOrderValue,
                  }),
                },
              })
            }
          >
            <Text>By text</Text>
          </Checkbox>
          <DebouncedInput
            aria-label="Text filter"
            disabled={!includeTextMatchSelector}
            defaultValue={selectorTextValue}
            onChange={({ target: { value } }) =>
              handleChange({
                selectorTextValue: value,
                selectorSettings: {
                  selector,
                  text_filter: value,
                  ...(includeOrderedMatchSelector && {
                    order_filter: selectorOrderValue,
                  }),
                },
              })
            }
          />
        </Option>
      </Options>

      {status && <SelectorStatus status={status} />}
    </Container>
  );
}

SelectorBuilder.propTypes = {
  count: PropTypes.number,
  status: PropTypes.oneOf(Object.values(STATUS)),
  onChange: PropTypes.func,
  stepChild: stepChildShape,
  stepType: PropTypes.oneOf([TOOLTIP, HOTSPOT]),
};
