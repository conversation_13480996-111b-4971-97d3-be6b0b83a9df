import React, { useState } from 'react';
import PropTypes from 'prop-types';
import {
  Button,
  ButtonGroup,
  FieldSet,
  Form,
  Heading,
  Input,
  Label,
  Modal,
  ModalHeader,
} from 'ext/components/ui';

const TemplateForm = ({
  visible,
  initialName = '',
  onClose,
  onSave,
  title = 'Save New Template',
}) => {
  const [name, setName] = useState(initialName);

  const trimmedName = name.trim();
  const isDisabled = trimmedName.length === 0 || trimmedName === initialName;

  const handleNameChange = ({ target: { value } }) => {
    setName(value);
  };

  const handleSave = e => {
    e.preventDefault();
    e.stopPropagation();
    onSave(trimmedName);
    onClose();
  };

  return (
    <Modal visible={visible} onClose={onClose}>
      <ModalHeader>
        <Heading>{title}</Heading>
      </ModalHeader>

      <Form disabled={isDisabled} onSubmit={handleSave} role="form">
        <FieldSet>
          <Label htmlFor="template-name">Name</Label>
          <Input
            id="template-name"
            onChange={handleNameChange}
            placeholder="Enter template name..."
            value={name}
            type="text"
          />
        </FieldSet>

        <ButtonGroup right>
          <Button kind="tertiary" onClick={onClose} type="button">
            Cancel
          </Button>
          <Button disabled={isDisabled} kind="primary" type="submit">
            Save
          </Button>
        </ButtonGroup>
      </Form>
    </Modal>
  );
};

TemplateForm.propTypes = {
  visible: PropTypes.bool,
  title: PropTypes.string,
  initialName: PropTypes.string,
  onClose: PropTypes.func,
  onSave: PropTypes.func,
};

export default TemplateForm;
