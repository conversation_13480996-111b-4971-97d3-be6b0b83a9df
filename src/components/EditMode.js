/* globals STANDALONE */

import React, { useEffect } from 'react';
import PropTypes from 'prop-types';
import isEmpty from 'lodash.isempty';
import { connect } from 'react-redux';
import useHijackInputHotkeys from 'ext/lib/hooks/use-hijack-input-hotkeys';
import useToggle from 'ext/lib/hooks/use-toggle';
import { get, set } from 'ext/lib/local-storage';
import { EVENT_BUILDER_BETA, useGates } from 'ext/lib/gates';
import { selectIsSpoofing } from 'ext/entities/account';
import { TopMenuManager } from 'ext/components/TopMenu';
import {
  InitialButtonUpgradeModal,
  FinalButtonUpgradeModal,
  useFlowUpgraded,
  useFlowDowngraded,
} from 'components/ButtonUpgradeModal';
import Editor from 'components/Editor';
import ManagerBar from 'components/ManagerBar';
import SideBarManager from 'components/SideBarManager';
import IntroductionVideo from 'components/IntroductionVideo';
import { selectFlow, flowShape } from 'entities/flows';
import { selectSelected, selectedShape } from 'entities/selected';
import { selectSidebar } from 'entities/application';
import usePrevious from 'lib/hooks/use-previous';

const IS_CONVERSION_MODAL_RENDERED_FALLBACK_VALUE = false;
const CONVERSION_MODAL_RENDERED_KEY = 'isConversionModalRenderedAlready';

export function EditMode({ flow, selected, sidebarMode, isSpoofing }) {
  const loaded = !!(flow && selected && selected.stepChild);
  const hasSteps = flow && !isEmpty(flow.steps);

  const gates = useGates();

  useHijackInputHotkeys();

  const [modalHidden, toggleModalHidden] = useToggle(false);

  const isFlowUpgraded = useFlowUpgraded(flow);
  const isFlowDowngraded = useFlowDowngraded(flow);

  const prevUpdatedAt = usePrevious(flow && flow.formatVersionUpdatedAt);

  useEffect(() => {
    if (isFlowDowngraded) {
      toggleModalHidden();
    }
  }, [isFlowDowngraded]);

  const isFlowPermanentlyUpgraded = flow && isFlowUpgraded && !!prevUpdatedAt;

  const handleOnCloseInitialButtonUpgrade = () => {
    toggleModalHidden();
    set(CONVERSION_MODAL_RENDERED_KEY, true);
  };

  const shouldRenderConversionModal = !get(
    CONVERSION_MODAL_RENDERED_KEY,
    IS_CONVERSION_MODAL_RENDERED_FALLBACK_VALUE
  );

  return (
    <>
      <IntroductionVideo />

      {shouldRenderConversionModal && (
        <InitialButtonUpgradeModal
          visible={
            !isSpoofing &&
            !modalHidden &&
            !isFlowPermanentlyUpgraded &&
            isFlowUpgraded &&
            hasSteps
          }
          onClose={handleOnCloseInitialButtonUpgrade}
        />
      )}

      <FinalButtonUpgradeModal
        visible={!isSpoofing && !modalHidden && isFlowPermanentlyUpgraded}
        onClose={toggleModalHidden}
      />

      {(flow || gates[EVENT_BUILDER_BETA]) && !STANDALONE ? (
        <TopMenuManager />
      ) : null}

      {loaded && <Editor selected={selected} />}
      {/* We don't want to render the sidebar when the sidebar is in edit mode
      and there are no steps i.e. flow with no steps */}
      {(hasSteps || sidebarMode !== 'edit') && (
        <SideBarManager selected={selected} />
      )}

      <ManagerBar flow={flow} selected={selected} />
    </>
  );
}

EditMode.propTypes = {
  flow: flowShape,
  selected: selectedShape,
  sidebarMode: PropTypes.oneOf(['edit', 'wait', 'create']),
  isSpoofing: PropTypes.bool,
};

const mapStateToProps = state => ({
  flow: selectFlow(state),
  selected: selectSelected(state),
  sidebarMode: selectSidebar(state).mode,
  isSpoofing: selectIsSpoofing(state),
});

export default connect(mapStateToProps)(EditMode);
