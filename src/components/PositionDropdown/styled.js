import styled from 'styled-components';
import { FontIcon, Input } from 'ext/components/ui';

/**
 * NOTE: Adapted from `Dropdown` component. If needed, we can split the `Dropdown`
 *       component in the future so that the rendered element and the menu are
 *       separate components that can be combined by a manager component.
 */

export const Container = styled.div`
  color: var(--light-text-color);
  position: relative;
`;

export const Select = styled.div`
  position: relative;

  ${Input} {
    cursor: pointer;

    &[readonly] {
      color: var(--white);
      cursor: pointer;
    }
  }

  ${FontIcon} {
    color: var(--light-text-color);
    cursor: pointer;
    position: absolute;
    right: 12px;
    top: calc(50% - 0.5em);
  }
`;

export const Menu = styled.ul`
  background: var(--background-x-light);
  border-radius: 4px;
  box-shadow: 0 0 8px rgba(0, 0, 0, 0.25);
  display: grid;
  grid-gap: 4px;
  grid-template-columns: repeat(3, 54px);
  grid-template-rows: repeat(3, 54px);
  right: 0;
  margin: 4px 0 0;
  max-height: 240px;
  padding: 4px;
  position: absolute;
  top: 100%;
  width: auto;
  z-index: 1;
`;

export const Position = styled.div`
  background-color: rgba(255, 255, 255, 0.1);
  border-radius: 2px;
  cursor: pointer;

  &:hover {
    border: 2px solid var(--tint-white);
  }

  ${({ selected }) =>
    selected &&
    `
      background-color: var(--tint-white);
      border: 2px solid var(--tint-white);
  `}
`;
