import React, { useState } from 'react';
import PropTypes from 'prop-types';
import { FontIcon, Input, MenuOverlay } from 'ext/components/ui';
import useToggle from 'ext/lib/hooks/use-toggle';
import { Container, Select, Menu, Position } from './styled';

export const positions = {
  topLeft: 'Top left',
  top: 'Top middle',
  topRight: 'Top right',
  left: 'Center left',
  center: 'Center middle',
  right: 'Center right',
  bottomLeft: 'Bottom left',
  bottom: 'Bottom middle',
  bottomRight: 'Bottom right',
};

export default function PositionDropdown({ id, defaultValue, onChange }) {
  const [open, toggleOpen] = useToggle();
  const [selectedValue, setSelectedValue] = useState(defaultValue);

  const handleChange = value => event => {
    onChange(value, event);
    setSelectedValue(value);
    toggleOpen();
  };

  return (
    <Container>
      <Select onClick={toggleOpen}>
        <Input
          id={id}
          placeholder="Select a position"
          readOnly
          role="listbox"
          value={positions[selectedValue]}
        />
        <FontIcon icon="caret-down" />
      </Select>

      {open && (
        <>
          <MenuOverlay
            aria-label="overlay"
            onClick={toggleOpen}
            role="button"
          />
          <Menu aria-label="menu">
            {Object.keys(positions).map(position => (
              <Position
                key={position}
                aria-label={position}
                onClick={handleChange(position)}
                role="listitem"
                selected={position === selectedValue}
              />
            ))}
          </Menu>
        </>
      )}
    </Container>
  );
}

PositionDropdown.propTypes = {
  id: PropTypes.string,
  defaultValue: PropTypes.oneOf(Object.keys(positions)),
  onChange: PropTypes.func,
};
