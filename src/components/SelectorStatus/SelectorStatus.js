import React from 'react';
import PropTypes from 'prop-types';
import styled from 'styled-components';
import { FontIcon } from 'ext/components/ui';
import {
  VALID,
  INVALID,
  NOT_FOUND,
  NOT_UNIQUE,
  SVG,
  STATUS,
} from 'lib/validate-selector';

const Status = {
  [VALID]: {
    color: 'var(--success-light)',
    icon: 'check',
    label: 'Element valid.',
    description: 'Selector is unique and valid.',
  },
  [NOT_UNIQUE]: {
    color: 'var(--warning-light)',
    icon: 'exclamation-triangle',
    label: 'Element not unique.',
    description: 'Multiple matching items found, but we need 1!',
  },
  [NOT_FOUND]: {
    color: 'var(--warning-light)',
    icon: 'exclamation-triangle',
    label: 'Element not found.',
    description: 'Unable to find any matching elements.',
  },
  [INVALID]: {
    color: 'var(--warning)',
    icon: 'times-circle',
    label: 'Invalid selector provided.',
    description: 'Please provide a valid selector.',
  },
  [SVG]: {
    color: 'var(--warning)',
    icon: 'times-circle',
    label: 'Invalid selector provided.',
    description:
      'Please do not target SVG elements or any elements within them.',
  },
};

const Text = styled.p`
  color: ${({ color }) => color};
  font-size: var(--regular);
  font-weight: var(--normal);
  margin: 0;
`;

const Container = styled.div`
  align-items: center;
  display: flex;

  ${Text} {
    margin-left: 6px;
  }
`;

export default function SelectorStatus({ short, status }) {
  const { color, icon, label, description } = Status[status];

  return (
    <Container aria-label="Selector status">
      <FontIcon icon={icon} color={color} />
      <Text color={color}>{short ? label : description}</Text>
    </Container>
  );
}

SelectorStatus.propTypes = {
  short: PropTypes.bool,
  status: PropTypes.oneOf(Object.values(STATUS)),
};
