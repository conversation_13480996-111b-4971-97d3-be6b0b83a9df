import React from 'react';
import PropTypes from 'prop-types';
import useToggle from 'ext/lib/hooks/use-toggle';
import { FontIcon, Input, Text } from 'ext/components/ui';
import { Container, Hostname } from './styled';

export default function PathInput({ hostname, id, onChange, value }) {
  const [focused, toggleFocused] = useToggle(false);

  return (
    <Container role="combobox">
      <Hostname>
        {focused && <FontIcon icon="globe" />}
        {!focused && <Text>{hostname}</Text>}
      </Hostname>

      <Input
        aria-expanded={focused}
        id={id}
        onBlur={toggleFocused}
        onFocus={toggleFocused}
        onChange={onChange}
        value={value}
      />
    </Container>
  );
}

PathInput.propTypes = {
  hostname: PropTypes.string,
  id: PropTypes.string,
  onChange: PropTypes.func,
  value: PropTypes.string,
};
