import React, { useMemo, useState } from 'react';
import PropTypes from 'prop-types';
import { connect } from 'react-redux';
import {
  Button,
  ButtonGroup,
  Dropdown,
  Input,
  Modal,
  ModalHeader,
  Heading,
  Form,
  FieldSet,
  Label,
} from 'ext/components/ui';
import { attemptInteraction } from 'ext/lib/track';
import useAnalytics from 'ext/lib/hooks/use-analytics';
import { CREATE_FLOW } from 'lib/metrics/interaction-types';
import { create } from 'entities/flows';
import { selectTags } from 'entities/tags';

export const FlowCreate = ({
  onClose,
  onCreate,
  recordMetric,
  tags = {},
  visible = true,
}) => {
  const { track } = useAnalytics();
  const [name, setName] = useState('');
  const [tag, setTag] = useState();

  const trimmed = name.trim();
  const disabled = trimmed.length === 0;

  const options = useMemo(
    () =>
      Object.values(tags).map(({ id: value, name: label }) => ({
        label,
        value,
      })),
    [tags]
  );

  const handleCreate = event => {
    track('Builder interaction', {
      name: 'Clicked Create Flow Button',
      component: 'FlowCreate',
    });
    recordMetric(CREATE_FLOW);

    event.preventDefault();

    const flow = {
      name: trimmed,
      tagIds: tag ? [tag] : [],
      previewUrl: window.location.href,
    };

    onCreate(flow);
    onClose();
    // restore defaults
    setName('');
    setTag();
  };

  const handleNameChange = ({ target: { value } }) => {
    setName(value);
  };

  const handleTagChange = selected => {
    setTag(selected);
  };

  return (
    <Modal onClose={onClose} visible={visible}>
      <ModalHeader>
        <Heading>Create New Flow</Heading>
      </ModalHeader>

      <Form disabled={disabled} onSubmit={handleCreate} role="form">
        <FieldSet>
          <Label htmlFor="name">Flow name</Label>
          <Input
            id="name"
            onChange={handleNameChange}
            placeholder="Enter flow name..."
            role="textbox"
            value={name}
          />

          <Label htmlFor="tag">Tag</Label>
          <Dropdown
            id="tag"
            none
            onChange={handleTagChange}
            options={options}
            placeholder="Select tag"
          />
        </FieldSet>

        <ButtonGroup right>
          <Button kind="tertiary" onClick={onClose} type="button">
            Cancel
          </Button>
          <Button
            disabled={disabled}
            kind="primary"
            onClick={handleCreate}
            type="submit"
          >
            Create
          </Button>
        </ButtonGroup>
      </Form>
    </Modal>
  );
};

FlowCreate.propTypes = {
  onClose: PropTypes.func,
  onCreate: PropTypes.func,
  recordMetric: PropTypes.func,
  tags: PropTypes.objectOf(
    PropTypes.shape({
      id: PropTypes.string,
      name: PropTypes.string,
    })
  ),
  visible: PropTypes.bool,
};

const mapStateToProps = state => ({
  tags: selectTags(state),
});

const mapDispatchToProps = {
  onCreate: create,
  recordMetric: attemptInteraction,
};

export default connect(mapStateToProps, mapDispatchToProps)(FlowCreate);
