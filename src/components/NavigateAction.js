import React from 'react';
import PropTypes from 'prop-types';
import styled from 'styled-components';
import { connect } from 'react-redux';
import { FontIcon, Tooltip, Button } from 'ext/components/ui';
import useAnalytics from 'ext/lib/hooks/use-analytics';
import { selectStepGroup, ACTION } from 'entities/step-groups';
import { start } from 'lib/navigation-mode';

const Container = styled.div`
  ${Button} {
    margin-left: 8px;
    height: 40px;

    ${FontIcon} {
      margin-right: 6px;
    }
  }
`;

export function NavigateAction({ disabled, onClick }) {
  const { track } = useAnalytics();

  return (
    <Container>
      <Tooltip label="Navigate">
        <Button
          aria-label="Navigate"
          disabled={disabled}
          onClick={() => {
            track('Builder view', {
              name: 'Entered Navigate Mode',
              component: 'NavigateAction',
            });
            onClick();
          }}
        >
          <FontIcon icon="reply" flip="horizontal" />
          Navigate
        </Button>
      </Tooltip>
    </Container>
  );
}

NavigateAction.propTypes = {
  disabled: PropTypes.bool,
  onClick: PropTypes.func,
};

const mapStateToProps = (state, { selected }) => {
  const { stepGroup } = selected || {};
  const { stepType } = selectStepGroup(state, stepGroup) || {};
  return { disabled: stepType === ACTION };
};

const mapDispatchToProps = {
  onClick: () => start(),
};

export default connect(mapStateToProps, mapDispatchToProps)(NavigateAction);
