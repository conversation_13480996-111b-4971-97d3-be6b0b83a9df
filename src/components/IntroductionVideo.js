import React from 'react';
import PropTypes from 'prop-types';
import { connect } from 'react-redux';
import styled from 'styled-components';
import {
  Button,
  FontIcon,
  Heading,
  Logo,
  Modal,
  ModalHeader,
  Text,
} from 'ext/components/ui';
import { attemptInteraction } from 'ext/lib/track';
import { WELCOME_ONBOARDING_VIDEO } from 'lib/constants';
import { UPDATE_USAGE_PROPERTY } from 'lib/metrics/interaction-types';
import { update, selectUsageProperty } from 'entities/usage-properties';

const ONBOARDING_PROPERTY = 'hasSeenWelcomeVideo';

const VideoDialog = styled.div`
  align-items: center;
  display: flex;
  flex-direction: column;

  ${Heading} {
    font-size: var(--x-large);
    margin: 20px 0;
  }

  ${FontIcon} {
    font-size: var(--xx-large);
  }

  ${Text} {
    margin: 20px;
  }
`;

const Video = styled.iframe`
  border: none;
  height: 360px;
  width: 640px;
`;

export const IntroductionVideo = ({ onClose, recordMetric, visible }) => {
  return (
    <Modal contained={false} visible={visible}>
      <VideoDialog>
        <Logo />

        <ModalHeader>
          <Heading>Welcome to the Appcues Flow Builder</Heading>
        </ModalHeader>

        <Video
          allowFullScreen
          src={WELCOME_ONBOARDING_VIDEO}
          title="Onboarding video"
        />

        <Text>Watch this 60-second intro to learn the basics.</Text>

        <Button
          kind="primary"
          onClick={() => {
            recordMetric();
            onClose();
          }}
          type="button"
        >
          Start building!
        </Button>
      </VideoDialog>
    </Modal>
  );
};

IntroductionVideo.propTypes = {
  onClose: PropTypes.func,
  recordMetric: PropTypes.func,
  visible: PropTypes.bool,
};

const mapStateToProps = state => {
  const onboarded = selectUsageProperty(state, ONBOARDING_PROPERTY);

  return {
    // Check usage property has been fetched and falsy
    visible: onboarded !== null && !onboarded,
  };
};

const mapDispatchToProps = {
  onClose: () => update({ [ONBOARDING_PROPERTY]: true }),
  recordMetric: () => attemptInteraction(UPDATE_USAGE_PROPERTY),
};

export default connect(mapStateToProps, mapDispatchToProps)(IntroductionVideo);
