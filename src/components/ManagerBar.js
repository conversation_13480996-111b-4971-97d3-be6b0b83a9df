import React from 'react';
import PropTypes from 'prop-types';
import { connect } from 'react-redux';
import isEmpty from 'lodash.isempty';
import { selectIsSpoofing } from 'ext/entities/account';
import useToggle from 'ext/lib/hooks/use-toggle';
import useAnalytics from 'ext/lib/hooks/use-analytics';
import { selectStatus } from 'ext/entities/status';
import {
  BottomBar,
  ButtonGroup,
  StatusIndicator,
  Button,
} from 'ext/components/ui';
import HelpManager from 'ext/components/HelpManager';
import SettingsManager from 'ext/components/SettingsManager';

import { SpoofingBanner } from 'ext/components/SpoofingBanner';
import { selectHasLocationMismatch } from 'lib/selectors';
import {
  toggleSidebar,
  toggleControls,
  selectControls,
} from 'entities/application';
import { selectFlow, flowShape } from 'entities/flows';
import { selectSelected, selectedShape } from 'entities/selected';
import FlowCreate from './FlowCreate';
import FlowManager from './FlowManager';
import PreviewMenu from './PreviewMenu';
import CreateFlowButton from './CreateFlowButton';
import Steps, { AddManager } from './Steps';
import NavigateAction from './NavigateAction';
import LocationBanner from './LocationBanner';
import LocalizedBanner from './LocalizedBanner';
import PublishedBanner from './PublishedBanner';

/*
 * TODO:
 *  - add various placeholders in place of <Steps />
 *  - add tooltips for button icons along with a11y attributes
 *  - FOUC when switching flows as it reverts to no flow selected temporarily
 */

export function ManagerBar({
  collapsed,
  flow,
  hasLocationMismatch,
  isSpoofing,
  onHide,
  onSidebarToggle,
  selected,
  status,
}) {
  const [isCreatorVisible, toggleCreator] = useToggle();
  const { track } = useAnalytics();

  const noFlowSelected = !(flow || selected);
  const hasNoSteps = !!flow && isEmpty(flow.steps);
  const hasSteps = !!(flow && selected);
  const isPublished = !!flow && flow.published;

  const messages = [];

  if (isSpoofing) {
    messages.push(<SpoofingBanner key="spoofing" />);
  }

  if (hasLocationMismatch) {
    messages.push(
      <LocationBanner flow={flow} selected={selected} key="location" />
    );
  }

  if (flow && flow.lockedForLocalization) {
    messages.push(<LocalizedBanner key="localized" />);
  }

  if (isPublished) {
    messages.push(<PublishedBanner key="published" />);
  }

  return (
    <BottomBar
      messages={messages}
      collapsed={collapsed}
      onClick={() => {
        track('Builder interaction', {
          name: 'Toggle Bottom Bar Visibility',
          component: 'ManagerBar',
        });
        onHide();
      }}
    >
      <FlowManager flow={flow} />

      {noFlowSelected && (
        <>
          <CreateFlowButton
            onClick={() => {
              track('Builder view', {
                name: 'Opened Create New Flow Modal',
                component: 'ManagerBar',
              });
              toggleCreator();
            }}
          />
          <FlowCreate onClose={toggleCreator} visible={isCreatorVisible} />
        </>
      )}

      {hasNoSteps && (
        <>
          <AddManager open />
          <ButtonGroup right>
            <HelpManager />
          </ButtonGroup>
        </>
      )}

      {hasSteps && (
        <>
          <NavigateAction selected={selected} />

          <Steps
            groupOrder={flow.steps}
            selected={selected}
            locked={flow.lockedForLocalization}
          />

          <ButtonGroup right>
            <StatusIndicator status={status} />

            <SettingsManager sidebarToggle={onSidebarToggle} />

            <HelpManager />

            <PreviewMenu flow={flow.id} stepGroup={selected.stepGroup} />

            <Button
              as="a"
              href={`${STUDIO_URL}/flows/${flow.id}/settings`}
              kind="primary"
              rel="noreferrer"
              target="_blank"
              onClick={() => {
                track('Builder interaction', {
                  name: 'Clicked Target and Publish Button',
                  component: 'ManagerBar',
                });
              }}
            >
              Target &amp; Publish
            </Button>
          </ButtonGroup>
        </>
      )}
    </BottomBar>
  );
}

ManagerBar.propTypes = {
  collapsed: PropTypes.bool,
  flow: flowShape,
  hasLocationMismatch: PropTypes.bool,
  isSpoofing: PropTypes.bool,
  onHide: PropTypes.func,
  onSidebarToggle: PropTypes.func,
  selected: selectedShape,
  status: PropTypes.string,
};

const mapStateToProps = state => ({
  flow: selectFlow(state),
  selected: selectSelected(state),
  isSpoofing: selectIsSpoofing(state),
  collapsed: selectControls(state).collapsed,
  hasLocationMismatch: selectHasLocationMismatch(state),
  status: selectStatus(state),
});

const mapDispatchToProps = {
  onHide: toggleControls,
  onSidebarToggle: toggleSidebar,
};

export default connect(mapStateToProps, mapDispatchToProps)(ManagerBar);
