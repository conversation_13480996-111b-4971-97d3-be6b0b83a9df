import React from 'react';
import PropTypes from 'prop-types';
import { connect } from 'react-redux';
import styled from 'styled-components';
import SideBar from 'ext/components/SideBar';
import Loader from 'ext/components/Loader';
import { getStepLabel, MODAL } from 'entities/step-groups';
import {
  selectSidebar,
  toggleSidebar,
  moveSidebar,
  sidebarShape,
  selectControls,
} from 'entities/application';
import { selectedShape } from 'entities/selected';
import LayoutsList from 'components/LayoutsList';
import Settings from 'components/Settings';

const FullHeightLoader = styled(Loader)`
  flex-grow: 1;
`;

export function SideBarManager({
  mode,
  position,
  collapsed,
  stepType = MODAL,
  onMove,
  onToggle,
  selected,
  isBottomBarCollapsed,
}) {
  // Create step label such that e.g. slideout types will have the label
  // "slideout step" and missing/unknown types will output just "step"
  const stepLabel = [stepType && getStepLabel(stepType), 'step']
    .filter(Boolean)
    .join(' ');

  const title = mode === 'create' ? `Add ${stepLabel}` : `Edit ${stepLabel}`;

  return (
    <SideBar
      onClickMove={onMove}
      onClickToggle={onToggle}
      collapsed={collapsed}
      position={position}
      title={title}
      isBottomBarCollapsed={isBottomBarCollapsed}
    >
      {mode === 'create' && (
        <LayoutsList position={position} stepType={stepType} />
      )}
      {mode === 'edit' && selected && <Settings selected={selected} />}
      {mode === 'wait' && <FullHeightLoader />}
    </SideBar>
  );
}

SideBarManager.propTypes = {
  ...sidebarShape,
  onMove: PropTypes.func,
  onToggle: PropTypes.func,
  selected: selectedShape,
};

const mapStateToProps = state => ({
  ...selectSidebar(state),
  isBottomBarCollapsed: selectControls(state).collapsed,
});

const mapDispatchToProps = {
  onMove: () => moveSidebar(),
  onToggle: () => toggleSidebar(),
};

export default connect(mapStateToProps, mapDispatchToProps)(SideBarManager);
