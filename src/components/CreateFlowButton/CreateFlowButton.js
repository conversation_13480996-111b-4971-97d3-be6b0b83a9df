import React from 'react';
import PropTypes from 'prop-types';
import { Button } from 'ext/components/ui';

/*
 * TODO: once FlowCreate modal is avaialble, remove `onClick` handler and open
 *       the modal directly from this component
 */
const CreateFlowButton = ({ onClick }) => (
  <Button kind="secondary" onClick={onClick}>
    Create New Flow
  </Button>
);

CreateFlowButton.propTypes = {
  onClick: PropTypes.func,
};

export default CreateFlowButton;
