import React from 'react';
import PropTypes from 'prop-types';
import { connect } from 'react-redux';

import { MoreInfo, StatusMessage } from 'ext/components/ui';
import useToggle from 'ext/lib/hooks/use-toggle';

import { flowShape } from 'entities/flows';
import { focus as focusStepGroup, stepGroupShape } from 'entities/step-groups';
import { selectRelevantAction } from 'lib/selectors';
import FlowDetails from 'components/FlowDetails';

export function LocationBanner({ flow, actionStep, onFocusStepGroup }) {
  const [visible, toggle] = useToggle(false);

  return (
    <>
      <StatusMessage aria-label="url mismatch">
        {actionStep
          ? `This page doesn't match the navigation step URL.`
          : `This page doesn't match the build URL.`}
        <MoreInfo
          // If we're showing this banner and the selected step exists after an
          // action step, we'll wanna update the action step's settings to fix
          // the location mismatch. Otherwise, we'll wanna change the build URL
          // via FlowDetails
          onClick={actionStep ? () => onFocusStepGroup(actionStep.id) : toggle}
        >
          Edit
        </MoreInfo>
      </StatusMessage>
      <FlowDetails flow={flow} onClose={toggle} visible={visible} />
    </>
  );
}

LocationBanner.propTypes = {
  flow: flowShape,
  actionStep: stepGroupShape,
  onFocusStepGroup: PropTypes.func,
};

const mapStateToProps = (state, ownProps) => {
  return {
    actionStep:
      ownProps.selected &&
      selectRelevantAction(state, ownProps.selected.stepGroup),
  };
};

const mapDispatchToProps = {
  onFocusStepGroup: focusStepGroup,
};

export default connect(mapStateToProps, mapDispatchToProps)(LocationBanner);
