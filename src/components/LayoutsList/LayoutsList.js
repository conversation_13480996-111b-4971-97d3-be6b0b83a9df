import React, { useState } from 'react';
import PropTypes from 'prop-types';
import { connect } from 'react-redux';
import { Button } from 'ext/components/ui';
import { cancel } from 'entities/step-children';
import DefaultLayouts from './DefaultLayouts';
import SavedTemplates from './SavedTemplates';
import { Footer, Tab, Tabs } from './styled';
/**
 * TODO:
 *   - Update styles to match the sidebar footer of the event builder e.g.
 *     sticky footer
 */

const DEFAULT = 'default';
const SAVED = 'saved';

function LayoutsList({ onCancel, position, stepType, layouts }) {
  const [tab, handleClick] = useState(DEFAULT);

  return (
    <>
      <Tabs>
        <Tab onClick={() => handleClick(DEFAULT)} active={tab === DEFAULT}>
          Default
        </Tab>
        <Tab onClick={() => handleClick(SAVED)} active={tab === SAVED}>
          Saved
        </Tab>
      </Tabs>

      {tab === DEFAULT && (
        <DefaultLayouts
          stepType={stepType}
          position={position}
          layouts={layouts?.[stepType] ?? []}
        />
      )}
      {tab === SAVED && <SavedTemplates stepType={stepType} />}

      <Footer>
        <Button kind="tertiary" onClick={onCancel} type="button">
          Cancel
        </Button>
      </Footer>
    </>
  );
}

LayoutsList.propTypes = {
  onCancel: PropTypes.func,
  position: PropTypes.string,
  stepType: PropTypes.string,
  // eslint-disable-next-line react/forbid-prop-types
  layouts: PropTypes.object,
};

const mapDispatchToProps = {
  onCancel: cancel,
};

const mapStateToProps = state => ({
  layouts: state.layouts,
});

export default connect(mapStateToProps, mapDispatchToProps)(LayoutsList);
