import React from 'react';
import styled from 'styled-components';
import { Button } from 'ext/components/ui';
import { SLIDEOUT } from 'entities/step-groups';

export const PreviewPane = styled.div.attrs(({ img, label }) => ({
  children: <img src={img} alt={`${label} preview`} />,
}))`
  position: fixed;
  top: 0;
  bottom: var(--bottom-bar-height);
  width: calc(100vw - var(--side-bar-width));
  justify-content: center;
  background: rgba(0, 0, 0, 0.5);
  align-items: center;
  pointer-events: none;
  display: flex;
  opacity: ${props => (props.visible ? 1 : 0)};
  transition: opacity 0.15s ease-out;
  ${props => (props.position === 'left' ? 'right: 0;' : 'left: 0;')}
  & > img {
    ${props =>
      props.type === SLIDEOUT
        ? `
        left: -14px;
        bottom: -34px;
        position: absolute;
    `
        : ''}
  }
`;

export const Panel = styled.section`
  padding: 12px 24px;
  overflow-y: auto;
`;

export const Wrapper = styled.section`
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  padding: 12px 34px;

  ${Button} {
    width: 100%;
    margin-bottom: 24px;
    font-weight: var(--bold);
    line-height: 1.5;

    &:hover {
      border: 2px solid var(--primary-light);
      padding: 6px 8px;
    }
  }
`;

export const Tabs = styled.nav`
  background-color: var(--background);
  display: flex;
  height: 60px;
  line-height: 60px;
`;

export const Tab = styled.div`
  border-bottom: 2px solid rgba(255, 255, 255, 0.1);
  color: var(--white);
  cursor: pointer;
  flex-basis: 50%;
  position: relative;
  text-align: center;
  font-size: var(--medium-large);

  ${({ active }) =>
    active &&
    `
      border-color: var(--secondary-light);
    color: var(--secondary-light);
    cursor: default;
    `}
`;

export const Footer = styled.footer`
  align-items: center;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  display: flex;
  padding: 12px 24px;
  width: 100%;
  margin: auto 0 0;
`;

export const OptionLabel = styled.figcaption`
  background: var(--background);
  border-bottom-left-radius: 6px;
  border-bottom-right-radius: 6px;
  width: 100%;
  height: 32px;
  text-align: center;
  line-height: 32px;
`;

export const OptionTile = styled.figure.attrs({ role: 'button' })`
  background: var(--background-x-light);
  border-radius: 6px;
  border-top: 2px solid transparent;
  box-shadow: 0px 4px 6px rgba(36, 42, 53, 0.4);
  color: var(--text-color);
  cursor: pointer;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  align-items: center;
  height: 128px;
  width: 130px;
  margin: 0 0 24px;

  &:hover {
    border: 2px solid var(--primary-light);

    & ${OptionLabel} {
      height: 30px;
    }
  }

  &:hover + ${PreviewPane} {
    opacity: 1;
  }
`;

export const Thumb = styled.img`
  max-height: 75px;
  max-width: 100px;
  width: auto;
  margin: auto;
`;

export const SavedList = styled.ul`
  margin: 12px 0 0 0;
  padding: 0;
`;

export const SavedLayout = styled.li`
  background: var(--background-x-light);
  align-content: center;
  border-radius: 6px;
  border: 2px solid transparent;
  box-shadow: 0px 4px 6px rgba(36, 42, 53, 0.4);
  color: var(--white);
  cursor: pointer;
  display: flex;
  height: 50px;
  line-height: 35px;
  justify-content: space-between;
  margin-bottom: 12px;
  padding: 5px 10px;

  &:hover {
    background: var(--background);
  }
`;

export const Control = styled(Button)`
  background-color: inherit;
  font-size: var(--x-large);
  &:hover {
    color: var(--white);
  }
`;

export const EmptyList = styled.div`
  align-items: center;
  color: var(--light-text-color);
  display: flex;
  justify-content: center;
  margin-top: 30px;
`;
