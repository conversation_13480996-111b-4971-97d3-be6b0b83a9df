import React, { useRef } from 'react';
import PropTypes from 'prop-types';
import mergeRefs from 'ext/lib/merge-refs';
import useToggle from 'ext/lib/hooks/use-toggle';
import useClickOutside from 'ext/lib/hooks/use-click-outside';
import { MiniButton, FontIcon, Menu, List, ListItem } from 'ext/components/ui';
import Tether from 'ext/components/Tether';
import TemplateForm from 'components/TemplateForm';
import ConfirmDeleteTemplate from './ConfirmDeleteTemplate';

const TemplateMenu = ({ name, onDelete, onEditName }) => {
  const $button = useRef();
  const $menu = useRef();

  const [isMenuVisible, toggleMenuVisible] = useToggle(false);
  const [isDeleteVisible, toggleDeleteVisible] = useToggle(false);
  const [isEditVisible, toggleEditVisible] = useToggle(false);

  useClickOutside([$button, $menu], isMenuVisible && toggleMenuVisible);

  const handleClick = e => {
    e.stopPropagation();
    toggleMenuVisible();
  };

  const handleDeleteMenuItemClick = e => {
    e.stopPropagation();
    toggleDeleteVisible();
    toggleMenuVisible();
  };

  const handleEditMenuItemClick = e => {
    e.stopPropagation();
    toggleEditVisible();
    toggleMenuVisible();
  };

  const menu = ({ ref }) => (
    <Menu ref={mergeRefs([$menu, ref])}>
      <List aria-label="Template options menu" role="menu">
        <ListItem role="menuitem" onClick={handleEditMenuItemClick}>
          Edit Name
        </ListItem>
        <ListItem role="menuitem" onClick={handleDeleteMenuItemClick}>
          Delete
        </ListItem>
      </List>
    </Menu>
  );

  return (
    <>
      <Tether
        attachment={menu}
        offset={{ x: -40 }}
        placement="bottom-left"
        visible={isMenuVisible}
      >
        <MiniButton
          ref={$button}
          size={22}
          aria-label={`Template options for ${name}`}
          onClick={handleClick}
        >
          <FontIcon icon="ellipsis-h" />
        </MiniButton>
      </Tether>

      <TemplateForm
        visible={isEditVisible}
        title="Edit Template Name"
        initialName={name}
        onClose={toggleEditVisible}
        onSave={onEditName}
      />

      <ConfirmDeleteTemplate
        visible={isDeleteVisible}
        name={name}
        onClose={toggleDeleteVisible}
        onDelete={onDelete}
      />
    </>
  );
};

TemplateMenu.propTypes = {
  name: PropTypes.string,
  onDelete: PropTypes.func,
  onEditName: PropTypes.func,
};

export default TemplateMenu;
