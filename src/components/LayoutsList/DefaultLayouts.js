import React, { useState } from 'react';
import PropTypes from 'prop-types';
import { connect } from 'react-redux';
import Portal from 'ext/components/Portal';
import { Button } from 'ext/components/ui';
import { stepChildShape, create } from 'entities/step-children';
import { Wrapper, OptionTile, Thumb, OptionLabel, PreviewPane } from './styled';

export function DefaultLayouts({ layouts, onClick, stepType, position }) {
  const [showing, setShowing] = useState('');
  const [blank, ...options] = layouts;

  return (
    <Wrapper>
      <Button onClick={() => onClick({ step: blank.step })}>
        Start from scratch
      </Button>
      {options.map(({ label, imageURL, step }) => (
        <OptionTile
          key={label}
          onClick={() => onClick({ step })}
          onMouseEnter={() => setShowing(label)}
          onMouseLeave={() => setShowing('')}
        >
          <Thumb src={imageURL} />
          <OptionLabel>{label}</OptionLabel>
        </OptionTile>
      ))}
      <Portal>
        {layouts.map(({ label, imageURL }) => (
          <PreviewPane
            key={`${label} preview`}
            img={imageURL}
            label={label}
            position={position}
            type={stepType}
            visible={showing === label}
          />
        ))}
      </Portal>
    </Wrapper>
  );
}

DefaultLayouts.propTypes = {
  layouts: PropTypes.arrayOf(
    PropTypes.shape({
      imageURL: PropTypes.string,
      label: PropTypes.string,
      step: stepChildShape,
    })
  ).isRequired,
  onClick: PropTypes.func.isRequired,
  stepType: PropTypes.string,
  position: PropTypes.oneOf(['left', 'right']),
};

const mapDispatchToProps = {
  onClick: create,
};

export default connect(null, mapDispatchToProps)(DefaultLayouts);
