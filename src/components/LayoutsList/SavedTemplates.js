import React, { useState, useMemo } from 'react';
import PropTypes from 'prop-types';
import { connect } from 'react-redux';
import search from 'ext/lib/search';
import { FontIcon, Input, Search, Truncated } from 'ext/components/ui';
import { attemptInteraction } from 'ext/lib/track';
import {
  UPDATE_TEMPLATE,
  DELETE_TEMPLATE,
} from 'lib/metrics/interaction-types';
import { stepChildShape, create } from 'entities/step-children';
import { update, remove, selectTemplates } from 'entities/templates';
import { EmptyList, SavedList, SavedLayout, Panel } from './styled';
import TemplateMenu from './TemplateMenu';

export function SavedTemplates({
  layouts = [],
  onClick,
  onEditName,
  onDelete,
  recordMetric,
}) {
  const [query, setQuery] = useState('');

  const handleChange = ({ target: { value } }) => setQuery(value);

  const filtered = useMemo(() => {
    return search(layouts, 'name', query);
  }, [query, layouts]);

  return (
    <Panel>
      <Search wrapped key="search">
        <FontIcon icon="search" />
        <Input
          onChange={handleChange}
          placeholder="Search your templates..."
          role="textbox"
          value={query}
        />
      </Search>

      <SavedList key="layouts">
        {filtered.length === 0 && <EmptyList>No templates found</EmptyList>}

        {filtered.length > 0 &&
          filtered.map(({ name, step, id }) => (
            <SavedLayout
              key={name}
              onClick={() => onClick({ step, fromTemplate: true })}
            >
              <Truncated title={name}>{name}</Truncated>
              <TemplateMenu
                name={name}
                onEditName={newName => {
                  recordMetric(UPDATE_TEMPLATE);
                  onEditName(id, { name: newName });
                }}
                onDelete={() => {
                  recordMetric(DELETE_TEMPLATE);
                  onDelete(id);
                }}
              />
            </SavedLayout>
          ))}
      </SavedList>
    </Panel>
  );
}

SavedTemplates.propTypes = {
  layouts: PropTypes.arrayOf(
    PropTypes.shape({
      id: PropTypes.string,
      name: PropTypes.string,
      step: stepChildShape,
    })
  ),
  onClick: PropTypes.func,
  onEditName: PropTypes.func,
  onDelete: PropTypes.func,
  recordMetric: PropTypes.func,
};

const mapStateToProps = (state, { stepType }) => ({
  layouts: selectTemplates(state, stepType),
});

const mapDispatchToProps = {
  onClick: create,
  onEditName: update,
  onDelete: remove,
  recordMetric: attemptInteraction,
};

export default connect(mapStateToProps, mapDispatchToProps)(SavedTemplates);
