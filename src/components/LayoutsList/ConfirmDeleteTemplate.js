import React from 'react';
import PropTypes from 'prop-types';
import styled from 'styled-components';
import {
  <PERSON><PERSON>,
  Modal<PERSON>eader,
  Button,
  ButtonGroup,
  Heading,
} from 'ext/components/ui';

const Message = styled.p`
  margin: 0;
  max-width: 360px;
`;

const ConfirmDeleteTemplate = ({ visible, name, onClose, onDelete }) => {
  const handleDelete = () => {
    onDelete();
    onClose();
  };

  return (
    <Modal visible={visible}>
      <ModalHeader>
        <Heading>Delete Template</Heading>
      </ModalHeader>
      <Message>Are you sure you want to delete &ldquo;{name}&rdquo;?</Message>
      <ButtonGroup right>
        <Button kind="tertiary" onClick={onClose} type="button">
          Cancel
        </Button>
        <Button kind="danger" onClick={handleDelete} type="button">
          Delete
        </Button>
      </ButtonGroup>
    </Modal>
  );
};
ConfirmDeleteTemplate.propTypes = {
  visible: PropTypes.bool,
  name: PropTypes.string,
  onClose: PropTypes.func,
  onDelete: PropTypes.func,
};

export default ConfirmDeleteTemplate;
