/* global REACT_DEVTOOLS */
import '@webcomponents/custom-elements';
import Builder from 'ext/BuilderElement';
import { inject } from 'root/inject.standalone';

// inject global AppcuesBuilder custom listeners
import 'ext/lib/builder-custom-events';

if (REACT_DEVTOOLS) {
  // eslint-disable-next-line global-require
  require('react-devtools');
}

function onReady() {
  const doesBuilderElementExist = window.customElements.get(
    'appcues-builder-standalone'
  );

  const $builder = document.body.querySelector('appcues-builder-standalone');
  if (!$builder) return;

  $builder.setAttribute('id', 'flow-builder');
  window.addEventListener(
    'builder:connected',
    ({ detail: { rootContainer, shadowRoot, container } }) => {
      inject(rootContainer, shadowRoot, container);
    }
  );

  if (!doesBuilderElementExist)
    window.customElements.define('appcues-builder-standalone', Builder);
}

if (document.readyState !== 'loading') {
  onReady();
} else {
  document.addEventListener('DOMContentLoaded', onReady);
}
